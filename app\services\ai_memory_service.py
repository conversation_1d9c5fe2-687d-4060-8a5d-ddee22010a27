"""
AI记忆管理服务
统一管理向量存储、语义编码和记忆提取
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.models.memory import (
    MemoryEntry, MemoryType, MemorySearchRequest, MemorySearchResult,
    MemoryExtractionRequest, MemoryExtractionResult,
    MemoryContextRequest, MemoryContextResponse,
    CharacterMemory, SceneMemory, PlotMemory
)
from app.services.embedding_service import get_embedding_service
from app.services.vector_store_service import get_vector_store_service
from app.services.memory_extraction_service import get_memory_extraction_service
from app.services.character_storage_service import character_storage_service

logger = logging.getLogger(__name__)


class AIMemoryService:
    """AI记忆管理服务"""
    
    def __init__(self):
        self.embedding_service = get_embedding_service()
        self.vector_store = get_vector_store_service()
        self.extraction_service = get_memory_extraction_service()
    
    def process_chapter_content(
        self, 
        project_id: str, 
        chapter_id: str, 
        content: str,
        extract_types: List[MemoryType] = None
    ) -> MemoryExtractionResult:
        """处理章节内容，提取并存储记忆"""
        try:
            logger.info(f"开始处理章节内容，项目: {project_id}, 章节: {chapter_id}")
            
            if extract_types is None:
                extract_types = [
                    MemoryType.CHARACTER,
                    MemoryType.SCENE,
                    MemoryType.PLOT,
                    MemoryType.WORLD
                ]
            
            # 1. 提取结构化信息
            extraction_request = MemoryExtractionRequest(
                project_id=project_id,
                chapter_id=chapter_id,
                content=content,
                extract_types=extract_types
            )
            
            extraction_result = self.extraction_service.extract_memories(extraction_request)
            
            if not extraction_result.success:
                return extraction_result
            
            # 2. 批量生成向量并存储记忆
            stored_count = self._store_memories_batch(extraction_result.extracted_memories)

            # 3. 保存角色信息到角色存储系统
            if extraction_result.characters:
                try:
                    saved_characters = character_storage_service.save_character_memories(
                        extraction_result.characters, project_id, chapter_id
                    )
                    logger.info(f"保存角色信息: {len(saved_characters)} 个角色")
                except Exception as e:
                    logger.error(f"保存角色信息失败: {e}")

            logger.info(f"章节处理完成，提取 {len(extraction_result.extracted_memories)} 条记忆，存储 {stored_count} 条")

            return extraction_result
            
        except Exception as e:
            logger.error(f"处理章节内容失败: {e}")
            return MemoryExtractionResult(
                success=False,
                error_message=str(e)
            )
    
    def _store_memory(self, memory: MemoryEntry) -> bool:
        """存储单个记忆"""
        try:
            # 生成向量
            text_for_embedding = f"{memory.title}\n{memory.summary}\n{memory.content}"
            embedding = self.embedding_service.encode_text(text_for_embedding)

            if not embedding:
                logger.error(f"为记忆 {memory.id} 生成向量失败")
                return False

            # 存储到向量数据库
            success = self.vector_store.add_memory(memory, embedding)

            if success:
                logger.debug(f"记忆 {memory.id} 存储成功")
            else:
                logger.error(f"记忆 {memory.id} 存储失败")

            return success

        except Exception as e:
            logger.error(f"存储记忆失败: {e}")
            return False

    def _store_memories_batch(self, memories: List[MemoryEntry]) -> int:
        """批量存储记忆"""
        try:
            if not memories:
                return 0

            logger.info(f"开始批量存储 {len(memories)} 条记忆")

            # 批量生成向量
            memories_with_embeddings = []

            for memory in memories:
                text_for_embedding = f"{memory.title}\n{memory.summary}\n{memory.content}"
                embedding = self.embedding_service.encode_text(text_for_embedding)

                if embedding:
                    memories_with_embeddings.append((memory, embedding))
                else:
                    logger.warning(f"为记忆 {memory.id} 生成向量失败，跳过")

            if not memories_with_embeddings:
                logger.warning("没有成功生成向量的记忆")
                return 0

            # 批量存储到向量数据库
            project_id = memories[0].project_id
            stored_count = self.vector_store.add_batch_memories(memories_with_embeddings, project_id)

            # 保存索引
            self.vector_store._save_project_index(project_id)

            logger.info(f"批量存储完成，成功存储 {stored_count} 条记忆")
            return stored_count

        except Exception as e:
            logger.error(f"批量存储记忆失败: {e}")
            return 0
    
    def search_memories(
        self, 
        query: str, 
        project_id: str,
        memory_types: List[MemoryType] = None,
        characters: List[str] = None,
        scenes: List[str] = None,
        tags: List[str] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7
    ) -> List[MemorySearchResult]:
        """搜索相关记忆"""
        try:
            # 生成查询向量
            query_embedding = self.embedding_service.encode_text(query)
            if not query_embedding:
                logger.error("生成查询向量失败")
                return []
            
            # 构建搜索请求
            search_request = MemorySearchRequest(
                query=query,
                project_id=project_id,
                memory_types=memory_types,
                characters=characters,
                scenes=scenes,
                tags=tags,
                limit=limit,
                similarity_threshold=similarity_threshold
            )
            
            # 执行搜索
            results = self.vector_store.search_memories(search_request, query_embedding)
            
            logger.info(f"搜索完成，找到 {len(results)} 个相关记忆")
            return results
            
        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            return []
    
    def get_writing_context(self, request: MemoryContextRequest) -> MemoryContextResponse:
        """获取写作上下文"""
        try:
            logger.info(f"获取写作上下文，项目: {request.project_id}")
            
            response = MemoryContextResponse(success=True)
            
            # 1. 搜索相关记忆
            if request.writing_focus:
                relevant_memories = self.search_memories(
                    query=request.writing_focus,
                    project_id=request.project_id,
                    characters=request.characters_involved,
                    scenes=request.scenes_involved,
                    limit=20
                )
                response.relevant_memories = relevant_memories
            
            # 2. 获取角色状态
            if request.characters_involved:
                for character_name in request.characters_involved:
                    character_memories = self.search_memories(
                        query=f"角色 {character_name}",
                        project_id=request.project_id,
                        memory_types=[MemoryType.CHARACTER],
                        characters=[character_name],
                        limit=5
                    )
                    
                    if character_memories:
                        # 合并角色信息
                        character_info = self._merge_character_info(character_memories)
                        response.character_states[character_name] = character_info
            
            # 3. 获取场景信息
            if request.scenes_involved:
                for scene_name in request.scenes_involved:
                    scene_memories = self.search_memories(
                        query=f"场景 {scene_name}",
                        project_id=request.project_id,
                        memory_types=[MemoryType.SCENE],
                        scenes=[scene_name],
                        limit=3
                    )
                    
                    if scene_memories:
                        scene_info = self._merge_scene_info(scene_memories)
                        response.scene_info[scene_name] = scene_info
            
            # 4. 获取活跃剧情
            plot_memories = self.search_memories(
                query="剧情 主线 支线",
                project_id=request.project_id,
                memory_types=[MemoryType.PLOT],
                limit=10
            )
            
            for plot_memory in plot_memories:
                if plot_memory.memory.metadata.get('status') == 'ongoing':
                    plot_info = PlotMemory(**plot_memory.memory.metadata)
                    response.active_plots.append(plot_info)
            
            # 5. 生成上下文摘要
            response.context_summary = self._generate_context_summary(response)
            
            logger.info(f"上下文获取完成")
            return response
            
        except Exception as e:
            logger.error(f"获取写作上下文失败: {e}")
            return MemoryContextResponse(
                success=False,
                error_message=str(e)
            )
    
    def _merge_character_info(self, memories: List[MemorySearchResult]) -> CharacterMemory:
        """合并角色信息"""
        if not memories:
            return CharacterMemory(name="", identity="", description="")
        
        # 使用最相似的记忆作为基础
        base_memory = memories[0].memory
        character_data = base_memory.metadata
        
        # 合并其他记忆的信息
        all_tags = set(character_data.get('personality_tags', []))
        all_abilities = set(character_data.get('abilities', []))
        all_goals = set(character_data.get('goals', []))
        
        for memory_result in memories[1:]:
            char_data = memory_result.memory.metadata
            all_tags.update(char_data.get('personality_tags', []))
            all_abilities.update(char_data.get('abilities', []))
            all_goals.update(char_data.get('goals', []))
        
        return CharacterMemory(
            name=character_data.get('name', ''),
            identity=character_data.get('identity', ''),
            description=character_data.get('description', ''),
            personality_tags=list(all_tags),
            appearance=character_data.get('appearance', ''),
            background=character_data.get('background', ''),
            current_status=character_data.get('current_status', ''),
            goals=list(all_goals),
            abilities=list(all_abilities),
            weaknesses=character_data.get('weaknesses', [])
        )
    
    def _merge_scene_info(self, memories: List[MemorySearchResult]) -> SceneMemory:
        """合并场景信息"""
        if not memories:
            return SceneMemory(name="", location="", description="")
        
        base_memory = memories[0].memory
        scene_data = base_memory.metadata
        
        return SceneMemory(
            name=scene_data.get('name', ''),
            location=scene_data.get('location', ''),
            description=scene_data.get('description', ''),
            atmosphere=scene_data.get('atmosphere', ''),
            geography=scene_data.get('geography', ''),
            climate=scene_data.get('climate', ''),
            culture=scene_data.get('culture', ''),
            rules=scene_data.get('rules', []),
            dangers=scene_data.get('dangers', []),
            resources=scene_data.get('resources', [])
        )
    
    def _generate_context_summary(self, context: MemoryContextResponse) -> str:
        """生成上下文摘要"""
        summary_parts = []
        
        if context.character_states:
            char_names = list(context.character_states.keys())
            summary_parts.append(f"涉及角色: {', '.join(char_names)}")
        
        if context.scene_info:
            scene_names = list(context.scene_info.keys())
            summary_parts.append(f"相关场景: {', '.join(scene_names)}")
        
        if context.active_plots:
            plot_count = len(context.active_plots)
            summary_parts.append(f"活跃剧情线: {plot_count}条")
        
        if context.relevant_memories:
            memory_count = len(context.relevant_memories)
            summary_parts.append(f"相关记忆: {memory_count}条")
        
        return "; ".join(summary_parts) if summary_parts else "暂无相关上下文"
    
    def get_project_memory_stats(self, project_id: str) -> Dict[str, Any]:
        """获取项目记忆统计"""
        return self.vector_store.get_project_stats(project_id)
    
    def delete_project_memories(self, project_id: str) -> bool:
        """删除项目的所有记忆"""
        return self.vector_store.delete_project_memories(project_id)

    def get_enhanced_memory_stats(self, project_id: str) -> Dict[str, Any]:
        """获取增强的记忆统计信息"""
        try:
            # 获取向量存储状态
            index_status = self.vector_store.get_index_status(project_id)

            if not index_status.get('exists', False):
                return {
                    'total_memories': 0,
                    'types': {},
                    'characters': [],
                    'scenes': [],
                    'tags': [],
                    'index_status': 'not_exists'
                }

            # 获取项目的所有元数据
            metadata_list = self.vector_store.metadata.get(project_id, [])

            # 统计信息
            stats = {
                'total_memories': len(metadata_list),
                'types': {},
                'characters': set(),
                'scenes': set(),
                'tags': set(),
                'index_status': 'exists',
                'vector_count': index_status.get('vector_count', 0),
                'is_dirty': index_status.get('is_dirty', False),
                'last_updated': index_status.get('info', {}).get('last_updated', 'unknown')
            }

            # 分析元数据
            for metadata in metadata_list:
                # 统计类型
                memory_type = metadata.get('type', 'unknown')
                stats['types'][memory_type] = stats['types'].get(memory_type, 0) + 1

                # 收集角色
                characters = metadata.get('characters', [])
                stats['characters'].update(characters)

                # 收集场景
                scenes = metadata.get('scenes', [])
                stats['scenes'].update(scenes)

                # 收集标签
                tags = metadata.get('tags', [])
                stats['tags'].update(tags)

            # 转换为列表
            stats['characters'] = list(stats['characters'])
            stats['scenes'] = list(stats['scenes'])
            stats['tags'] = list(stats['tags'])

            return stats

        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            return {
                'total_memories': 0,
                'types': {},
                'characters': [],
                'scenes': [],
                'tags': [],
                'index_status': 'error',
                'error': str(e)
            }

    def save_all_memories(self) -> Dict[str, int]:
        """保存所有待保存的记忆索引"""
        try:
            saved_count = self.vector_store.save_all_dirty_indexes()
            return {
                'success': True,
                'saved_projects': saved_count
            }

        except Exception as e:
            logger.error(f"保存记忆索引失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'saved_projects': 0
            }

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 获取各服务状态
            embedding_info = self.embedding_service.get_model_info()
            vector_status = self.vector_store.get_index_status()

            return {
                'embedding_service': {
                    'status': 'running' if embedding_info['is_loaded'] else 'error',
                    'model_name': embedding_info['model_name'],
                    'dimension': embedding_info['dimension'],
                    'device': embedding_info['device'],
                    'model_source': embedding_info.get('model_source', 'unknown')
                },
                'vector_store': {
                    'status': 'running',
                    'total_projects': vector_status['total_projects'],
                    'total_vectors': vector_status['total_vectors'],
                    'dirty_projects': vector_status['dirty_projects']
                },
                'extraction_service': {
                    'status': 'running'
                }
            }

        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }


# 全局实例
ai_memory_service = AIMemoryService()


def get_ai_memory_service() -> AIMemoryService:
    """获取AI记忆服务实例"""
    return ai_memory_service
