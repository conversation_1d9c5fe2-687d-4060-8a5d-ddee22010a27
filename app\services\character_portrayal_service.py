#!/usr/bin/env python3
"""
人物刻画服务
构建丰富的人物描写提示词模板，包含对话、心理、动作等多维度描写
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class PortrayalType(Enum):
    """刻画类型"""
    DIALOGUE = "dialogue"  # 对话
    INNER_THOUGHT = "inner_thought"  # 内心独白
    ACTION = "action"  # 动作描写
    APPEARANCE = "appearance"  # 外貌描写
    EMOTION = "emotion"  # 情绪表达
    HABIT = "habit"  # 习惯特征
    INTERACTION = "interaction"  # 互动行为
    BACKGROUND = "background"  # 背景展现


class PersonalityTrait(Enum):
    """性格特质"""
    CONFIDENT = "confident"  # 自信
    SHY = "shy"  # 害羞
    AGGRESSIVE = "aggressive"  # 激进
    CALM = "calm"  # 冷静
    HUMOROUS = "humorous"  # 幽默
    SERIOUS = "serious"  # 严肃
    OPTIMISTIC = "optimistic"  # 乐观
    PESSIMISTIC = "pessimistic"  # 悲观
    INTELLIGENT = "intelligent"  # 聪明
    NAIVE = "naive"  # 天真
    MYSTERIOUS = "mysterious"  # 神秘
    WARM = "warm"  # 温暖
    NERVOUS = "nervous"  # 紧张


@dataclass
class PortrayalTemplate:
    """刻画模板"""
    template_id: str
    portrayal_type: PortrayalType
    personality_traits: List[PersonalityTrait]
    template_content: str
    example_output: str
    usage_tips: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "template_id": self.template_id,
            "portrayal_type": self.portrayal_type.value,
            "personality_traits": [trait.value for trait in self.personality_traits],
            "template_content": self.template_content,
            "example_output": self.example_output,
            "usage_tips": self.usage_tips
        }


class CharacterPortrayalService:
    """人物刻画服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.templates = self._initialize_templates()
        self.dialogue_patterns = self._initialize_dialogue_patterns()
        self.action_patterns = self._initialize_action_patterns()
        self.emotion_expressions = self._initialize_emotion_expressions()
    
    def _initialize_templates(self) -> Dict[str, PortrayalTemplate]:
        """初始化刻画模板"""
        templates = {}
        
        # 对话模板
        templates["confident_dialogue"] = PortrayalTemplate(
            template_id="confident_dialogue",
            portrayal_type=PortrayalType.DIALOGUE,
            personality_traits=[PersonalityTrait.CONFIDENT],
            template_content="""
{character_name}的对话特点：
- 语调坚定，用词果断
- 经常使用肯定句式
- 声音清晰有力，不拖泥带水
- 在关键时刻能够主导对话节奏

对话内容："{dialogue_content}"

请按照以上特点，生成{character_name}的对话，包含语言、语调、神态等细节描写：
""",
            example_output='"我知道该怎么做。"林默的声音清晰而坚定，目光直视着对方，"这件事交给我，你们不用担心。"',
            usage_tips=["适用于领导者角色", "关键决策时刻", "展现角色权威性"]
        )
        
        templates["shy_dialogue"] = PortrayalTemplate(
            template_id="shy_dialogue",
            portrayal_type=PortrayalType.DIALOGUE,
            personality_traits=[PersonalityTrait.SHY],
            template_content="""
{character_name}的对话特点：
- 声音较小，语速较慢
- 经常使用疑问句或不确定的表达
- 说话时会有停顿和犹豫
- 避免直接的眼神接触

对话内容："{dialogue_content}"

请按照以上特点，生成{character_name}的对话，包含语言、语调、神态等细节描写：
""",
            example_output='"那个...我觉得...也许可以试试？"小雨低着头，声音轻得像蚊子叫，手指不安地绞着衣角。',
            usage_tips=["适用于内向角色", "初次见面场景", "表现角色脆弱面"]
        )
        
        # 内心独白模板
        templates["conflicted_thought"] = PortrayalTemplate(
            template_id="conflicted_thought",
            portrayal_type=PortrayalType.INNER_THOUGHT,
            personality_traits=[PersonalityTrait.INTELLIGENT],
            template_content="""
{character_name}的内心冲突：
- 理性与感性的斗争
- 多角度思考问题
- 内心对话丰富
- 思维逻辑清晰但情感复杂

冲突内容：{conflict_description}

请生成{character_name}的内心独白，展现其思维过程和情感变化：
""",
            example_output='他心中涌起一阵矛盾。理智告诉他这样做是对的，但内心深处却有个声音在反对。"如果我这样做了，会不会伤害到她？"他闭上眼睛，试图理清思绪。',
            usage_tips=["重要决策时刻", "角色成长关键点", "展现角色深度"]
        )
        
        # 动作描写模板
        templates["nervous_action"] = PortrayalTemplate(
            template_id="nervous_action",
            portrayal_type=PortrayalType.ACTION,
            personality_traits=[PersonalityTrait.SHY],
            template_content="""
{character_name}的紧张表现：
- 小动作频繁（摸头发、咬唇等）
- 身体姿态不自然
- 呼吸节奏变化
- 避免直接接触

情境：{situation_description}

请描写{character_name}在此情境下的具体动作和身体语言：
""",
            example_output='她的手指无意识地绕着一缕头发，脚尖轻点着地面。每当有人看向她时，她就会下意识地后退半步，双手紧紧握在身前。',
            usage_tips=["紧张场景", "初次约会", "面试等正式场合"]
        )
        
        templates["confident_action"] = PortrayalTemplate(
            template_id="confident_action",
            portrayal_type=PortrayalType.ACTION,
            personality_traits=[PersonalityTrait.CONFIDENT],
            template_content="""
{character_name}的自信表现：
- 姿态挺拔，步伐稳健
- 手势大方得体
- 眼神坚定有力
- 占据空间的方式显示主导性

情境：{situation_description}

请描写{character_name}在此情境下的具体动作和身体语言：
""",
            example_output='他大步走向会议桌，背脊挺直，双肩放松。坐下时动作干净利落，双手自然地放在桌面上，目光扫视全场，每个人都能感受到他的存在感。',
            usage_tips=["领导场景", "重要演讲", "关键谈判"]
        )
        
        # 情绪表达模板
        templates["anger_expression"] = PortrayalTemplate(
            template_id="anger_expression",
            portrayal_type=PortrayalType.EMOTION,
            personality_traits=[PersonalityTrait.AGGRESSIVE],
            template_content="""
{character_name}的愤怒表达：
- 生理反应：面红耳赤、呼吸急促
- 声音变化：音调升高、语速加快
- 身体语言：握拳、肌肉紧张
- 言语特点：用词激烈、语气强硬

愤怒原因：{anger_trigger}

请描写{character_name}的愤怒表现，包含生理、心理、行为等多个层面：
""",
            example_output='血液冲上了他的太阳穴，拳头紧握到指节发白。"够了！"他的声音如雷鸣般炸响，胸膛剧烈起伏，眼中燃烧着怒火。',
            usage_tips=["冲突高潮", "正义愤怒", "角色爆发点"]
        )
        
        return templates
    
    def _initialize_dialogue_patterns(self) -> Dict[str, Dict[str, Any]]:
        """初始化对话模式"""
        return {
            PersonalityTrait.HUMOROUS.value: {
                "speech_patterns": ["经常使用比喻和双关", "语调轻松活泼", "善于自嘲"],
                "vocabulary": ["有趣的俚语", "流行词汇", "幽默的比较"],
                "delivery": ["语速适中", "语调有起伏", "经常停顿制造效果"]
            },
            PersonalityTrait.SERIOUS.value: {
                "speech_patterns": ["用词准确严谨", "逻辑清晰", "很少使用感叹句"],
                "vocabulary": ["正式用词", "专业术语", "书面语较多"],
                "delivery": ["语速稳定", "语调平稳", "停顿有力"]
            },
            PersonalityTrait.MYSTERIOUS.value: {
                "speech_patterns": ["话中有话", "经常留有余地", "善用暗示"],
                "vocabulary": ["模糊词汇", "象征性表达", "古典用词"],
                "delivery": ["语速较慢", "语调低沉", "意味深长的停顿"]
            }
        }
    
    def _initialize_action_patterns(self) -> Dict[str, Dict[str, Any]]:
        """初始化动作模式"""
        return {
            PersonalityTrait.CALM.value: {
                "movement_style": ["动作缓慢而有控制", "姿态稳定", "很少多余动作"],
                "gestures": ["手势简洁", "动作幅度适中", "节奏感强"],
                "posture": ["背脊挺直", "肩膀放松", "重心稳定"]
            },
            PersonalityTrait.NERVOUS.value: {
                "movement_style": ["动作急促", "经常改变姿势", "小动作频繁"],
                "gestures": ["手势不安", "触摸脸部或头发", "摆弄物品"],
                "posture": ["身体紧张", "肩膀耸起", "重心不稳"]
            },
            PersonalityTrait.WARM.value: {
                "movement_style": ["动作柔和", "接近他人", "开放性姿态"],
                "gestures": ["手势温和", "经常微笑", "眼神温暖"],
                "posture": ["身体前倾", "开放姿态", "放松状态"]
            }
        }
    
    def _initialize_emotion_expressions(self) -> Dict[str, Dict[str, Any]]:
        """初始化情绪表达"""
        return {
            "joy": {
                "facial": ["眼角弯曲", "嘴角上扬", "面部放松"],
                "vocal": ["语调上扬", "笑声", "语速加快"],
                "physical": ["身体轻松", "动作活泼", "姿态开放"]
            },
            "sadness": {
                "facial": ["眉头紧锁", "嘴角下垂", "眼神黯淡"],
                "vocal": ["语调低沉", "声音颤抖", "语速缓慢"],
                "physical": ["肩膀下垂", "身体蜷缩", "动作迟缓"]
            },
            "fear": {
                "facial": ["眼睛睁大", "面色苍白", "嘴唇颤抖"],
                "vocal": ["声音发抖", "语调不稳", "呼吸急促"],
                "physical": ["身体僵硬", "后退动作", "保护性姿势"]
            }
        }
    
    def generate_character_portrayal(
        self, 
        character_info: Dict[str, Any],
        portrayal_type: PortrayalType,
        context: Dict[str, Any] = None
    ) -> str:
        """生成角色刻画"""
        try:
            character_name = character_info.get("name", "角色")
            personality_tags = character_info.get("personality_tags", [])
            
            # 选择合适的模板
            template = self._select_template(portrayal_type, personality_tags)
            
            if not template:
                return self._generate_fallback_portrayal(character_info, portrayal_type, context)
            
            # 构建提示词
            prompt = self._build_portrayal_prompt(template, character_info, context)
            
            # 这里应该调用AI生成，暂时返回示例
            return template.example_output
            
        except Exception as e:
            self.logger.error(f"生成角色刻画失败: {e}")
            return f"[{character_name}的{portrayal_type.value}描写]"
    
    def _select_template(
        self, 
        portrayal_type: PortrayalType, 
        personality_tags: List[str]
    ) -> Optional[PortrayalTemplate]:
        """选择合适的模板"""
        # 根据刻画类型和性格标签选择最匹配的模板
        matching_templates = []
        
        for template in self.templates.values():
            if template.portrayal_type == portrayal_type:
                # 计算匹配度
                trait_names = [trait.value for trait in template.personality_traits]
                match_score = len(set(trait_names) & set(personality_tags))
                if match_score > 0:
                    matching_templates.append((template, match_score))
        
        if matching_templates:
            # 返回匹配度最高的模板
            matching_templates.sort(key=lambda x: x[1], reverse=True)
            return matching_templates[0][0]
        
        # 如果没有匹配的，返回同类型的第一个模板
        for template in self.templates.values():
            if template.portrayal_type == portrayal_type:
                return template
        
        return None
    
    def _build_portrayal_prompt(
        self, 
        template: PortrayalTemplate,
        character_info: Dict[str, Any],
        context: Dict[str, Any] = None
    ) -> str:
        """构建刻画提示词"""
        prompt = template.template_content
        
        # 替换角色信息
        prompt = prompt.replace("{character_name}", character_info.get("name", "角色"))
        
        # 根据上下文替换其他占位符
        if context:
            for key, value in context.items():
                placeholder = "{" + key + "}"
                if placeholder in prompt:
                    prompt = prompt.replace(placeholder, str(value))
        
        return prompt
    
    def _generate_fallback_portrayal(
        self, 
        character_info: Dict[str, Any],
        portrayal_type: PortrayalType,
        context: Dict[str, Any] = None
    ) -> str:
        """生成备用刻画"""
        character_name = character_info.get("name", "角色")
        
        if portrayal_type == PortrayalType.DIALOGUE:
            return f'"{context.get("dialogue_content", "...")}" {character_name}说道。'
        elif portrayal_type == PortrayalType.ACTION:
            return f'{character_name}做出了相应的动作。'
        elif portrayal_type == PortrayalType.INNER_THOUGHT:
            return f'{character_name}心中想着。'
        else:
            return f'{character_name}的{portrayal_type.value}表现。'
    
    def get_portrayal_suggestions(
        self, 
        character_info: Dict[str, Any],
        scene_context: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """获取刻画建议"""
        suggestions = []
        personality_tags = character_info.get("personality_tags", [])
        
        # 基于性格特征推荐刻画方式
        for trait in personality_tags:
            if trait in self.dialogue_patterns:
                suggestions.append({
                    "type": "dialogue",
                    "trait": trait,
                    "description": f"基于{trait}特质的对话刻画",
                    "patterns": self.dialogue_patterns[trait]
                })
            
            if trait in self.action_patterns:
                suggestions.append({
                    "type": "action",
                    "trait": trait,
                    "description": f"基于{trait}特质的动作刻画",
                    "patterns": self.action_patterns[trait]
                })
        
        # 基于场景推荐
        if scene_context:
            scene_type = scene_context.get("type", "")
            if scene_type == "conflict":
                suggestions.append({
                    "type": "emotion",
                    "description": "冲突场景的情绪表达",
                    "focus": ["愤怒", "紧张", "决心"]
                })
            elif scene_type == "romantic":
                suggestions.append({
                    "type": "emotion",
                    "description": "浪漫场景的情绪表达",
                    "focus": ["温柔", "害羞", "甜蜜"]
                })
        
        return suggestions
    
    def enhance_dialogue_with_personality(
        self, 
        dialogue_content: str,
        character_info: Dict[str, Any]
    ) -> str:
        """基于性格增强对话"""
        personality_tags = character_info.get("personality_tags", [])
        character_name = character_info.get("name", "角色")
        
        # 基于性格特征调整对话风格
        enhanced_dialogue = dialogue_content
        
        for trait in personality_tags:
            if trait == "humorous":
                # 添加幽默元素
                enhanced_dialogue = f'"{enhanced_dialogue}" {character_name}笑着说道，眼中闪烁着调皮的光芒。'
            elif trait == "serious":
                # 添加严肃元素
                enhanced_dialogue = f'"{enhanced_dialogue}" {character_name}严肃地说道，语气不容置疑。'
            elif trait == "shy":
                # 添加害羞元素
                enhanced_dialogue = f'"{enhanced_dialogue}" {character_name}小声说道，脸颊微微泛红。'
        
        return enhanced_dialogue


# 全局实例
character_portrayal_service = CharacterPortrayalService()


def get_character_portrayal_service() -> CharacterPortrayalService:
    """获取人物刻画服务实例"""
    return character_portrayal_service
