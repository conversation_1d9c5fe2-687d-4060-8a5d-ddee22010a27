# API路由修复指南

## 🎯 问题分析

前端调用的API路径：`/api/v1/characters/{project_id}`
后端注册的路径：`/api/v1/characters/{project_id}` (通过advanced_router)

路径匹配正确！问题可能在于：

1. 后端服务未重启
2. 数据库中没有角色数据
3. 角色模型字段不匹配

## 🔧 修复步骤

### 1. 重启后端服务
```bash
# 停止当前服务 (Ctrl+C)
# 然后重新启动
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 测试API端点
```bash
# 运行测试脚本
python test_advanced_api.py
```

### 3. 检查数据库
确保项目中有角色数据：
```bash
# 如果使用SQLite，可以查看数据库文件
# 或者通过现有的角色管理界面添加一些测试角色
```

### 4. 验证前端调用
在浏览器开发者工具中检查：
- Network面板查看API请求
- Console面板查看错误信息

## 🧪 测试用例

### 基础测试
1. 访问：`http://localhost:8000/api/v1/characters/{project_id}`
2. 应该返回角色列表（可能为空数组）

### 完整测试
1. 先在基础视图中添加一些角色
2. 切换到高级视图
3. 验证角色列表、搜索、详情等功能

## 🐛 常见问题

### 404错误
- 检查路由是否正确注册
- 确认项目ID是否正确
- 验证URL路径拼写

### 500错误
- 检查后端日志
- 验证数据库连接
- 确认模型字段匹配

### 前端错误
- 检查D3.js是否正确安装
- 验证Vue组件导入
- 确认API响应格式

## 📝 调试技巧

### 后端调试
```python
# 在API端点中添加日志
import logging
logger = logging.getLogger(__name__)
logger.info(f"收到请求: {project_id}")
```

### 前端调试
```javascript
// 在API调用中添加日志
console.log('API调用:', endpoint)
console.log('响应数据:', response.data)
```

## ✅ 验证清单

- [ ] 后端服务正常启动
- [ ] 路由正确注册
- [ ] 数据库有角色数据
- [ ] API端点返回正确格式
- [ ] 前端组件正确导入
- [ ] D3.js依赖已安装
- [ ] 视图切换正常工作

## 🎉 成功标志

当以下情况出现时，说明修复成功：
1. 高级视图能正常加载角色列表
2. 搜索和过滤功能正常
3. 角色详情侧边栏能正常显示
4. 关系网络图能正常渲染（如果有关系数据）

## 📞 需要帮助？

如果问题仍然存在，请提供：
1. 后端服务日志
2. 前端控制台错误
3. 网络请求详情
4. 数据库状态
