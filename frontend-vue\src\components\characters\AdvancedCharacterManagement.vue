<template>
  <div class="advanced-character-management">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <h2>
          <i class="bi bi-people me-2"></i>
          高级角色管理
        </h2>
        <span class="character-count">共 {{ characters.length }} 个角色</span>
      </div>
      
      <div class="toolbar-right">
        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            v-model="searchQuery" 
            @input="handleSearch"
            placeholder="搜索角色..." 
            class="search-input"
          />
          <select v-model="searchType" class="search-type">
            <option value="all">全部</option>
            <option value="name">姓名</option>
            <option value="description">描述</option>
            <option value="personality">性格</option>
          </select>
        </div>
        
        <!-- 过滤和排序 -->
        <select v-model="filterBy" @change="loadCharacters" class="filter-select">
          <option value="all">全部角色</option>
          <option value="main">主要角色</option>
          <option value="supporting">配角</option>
          <option value="minor">次要角色</option>
        </select>
        
        <select v-model="sortBy" @change="loadCharacters" class="sort-select">
          <option value="importance">按重要性</option>
          <option value="name">按姓名</option>
          <option value="chapters">按出场次数</option>
          <option value="relationships">按关系数量</option>
        </select>
        
        <!-- 视图切换 -->
        <div class="view-toggle">
          <button 
            :class="{ active: viewMode === 'list' }" 
            @click="viewMode = 'list'"
            class="view-btn"
          >
            <i class="bi bi-list me-1"></i>列表
          </button>
          <button 
            :class="{ active: viewMode === 'network' }" 
            @click="viewMode = 'network'"
            class="view-btn"
          >
            <i class="bi bi-diagram-3 me-1"></i>关系图
          </button>
        </div>
      </div>
    </div>

    <!-- 角色列表视图 -->
    <div v-if="viewMode === 'list'" class="character-list-view">
      <div v-if="loading" class="loading">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-3">加载角色列表...</p>
      </div>
      
      <div v-else-if="displayCharacters.length === 0" class="empty-state">
        <i class="bi bi-people display-1 text-muted mb-3"></i>
        <h4>{{ searchQuery ? '未找到匹配的角色' : '暂无角色数据' }}</h4>
        <p class="text-muted">
          {{ searchQuery ? '尝试调整搜索条件' : '请先进行章节向量化以提取角色信息' }}
        </p>
      </div>
      
      <div v-else class="character-grid">
        <div 
          v-for="character in displayCharacters" 
          :key="character.id"
          @click="selectCharacter(character)"
          :class="{ 
            'character-card': true, 
            'selected': selectedCharacter?.id === character.id,
            'main-character': character.importance_score >= 7,
            'supporting-character': character.importance_score >= 4 && character.importance_score < 7,
            'minor-character': character.importance_score < 4
          }"
        >
          <div class="character-header">
            <h3 class="character-name">{{ character.name }}</h3>
            <div class="importance-badge">
              <span class="importance-score">{{ character.importance_score.toFixed(1) }}</span>
              <span class="importance-label">{{ getImportanceLabel(character.importance_score) }}</span>
            </div>
          </div>
          
          <div class="character-info">
            <p class="character-description">{{ character.description }}</p>
            
            <div class="character-tags">
              <span 
                v-for="tag in character.personality_tags" 
                :key="tag"
                class="personality-tag"
              >
                {{ tag }}
              </span>
            </div>
            
            <div class="character-stats">
              <div class="stat-item">
                <span class="stat-label">出场章节:</span>
                <span class="stat-value">{{ character.total_chapters }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">关系数量:</span>
                <span class="stat-value">{{ character.relationship_count }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">事件数量:</span>
                <span class="stat-value">{{ character.event_count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关系网络视图 -->
    <div v-if="viewMode === 'network'" class="network-view">
      <div class="network-controls">
        <label>
          <input 
            type="checkbox" 
            v-model="networkOptions.includeWeak"
            @change="loadNetworkData"
          />
          显示弱关系
        </label>
        <label>
          最小强度:
          <input 
            type="range" 
            min="0" 
            max="1" 
            step="0.1"
            v-model="networkOptions.minStrength"
            @change="loadNetworkData"
          />
          {{ networkOptions.minStrength }}
        </label>
      </div>
      
      <div id="network-container" class="network-container"></div>
      
      <div class="network-stats">
        <div class="stat-card">
          <h4>网络统计</h4>
          <p>角色数量: {{ networkData.stats?.total_characters || 0 }}</p>
          <p>关系数量: {{ networkData.stats?.total_relationships || 0 }}</p>
          <p>正面关系: {{ networkData.stats?.positive_relationships || 0 }}</p>
          <p>负面关系: {{ networkData.stats?.negative_relationships || 0 }}</p>
          <p>网络密度: {{ (networkData.stats?.network_density * 100 || 0).toFixed(1) }}%</p>
        </div>
      </div>
    </div>

    <!-- 角色详情侧边栏 -->
    <div v-if="selectedCharacter" class="character-detail-sidebar">
      <div class="sidebar-header">
        <h3>{{ selectedCharacter.name }}</h3>
        <button @click="selectedCharacter = null" class="close-btn">
          <i class="bi bi-x-lg"></i>
        </button>
      </div>
      
      <div class="sidebar-content">
        <div v-if="characterDetail" class="detail-sections">
          <!-- 基础信息 -->
          <div class="detail-section">
            <h4><i class="bi bi-info-circle me-2"></i>基础信息</h4>
            <div class="info-grid">
              <div class="info-item">
                <label>描述:</label>
                <p>{{ characterDetail.basic_info.description }}</p>
              </div>
              <div class="info-item">
                <label>外观:</label>
                <p>{{ characterDetail.basic_info.appearance }}</p>
              </div>
              <div class="info-item">
                <label>首次出场:</label>
                <p>{{ characterDetail.basic_info.first_appearance }}</p>
              </div>
              <div class="info-item">
                <label>最新章节:</label>
                <p>{{ characterDetail.basic_info.latest_chapter }}</p>
              </div>
              <div class="info-item">
                <label>当前版本:</label>
                <p>v{{ characterDetail.basic_info.current_version }}</p>
              </div>
            </div>
          </div>

          <!-- 性格特征 -->
          <div class="detail-section">
            <h4><i class="bi bi-heart me-2"></i>性格特征</h4>
            <div class="personality-traits">
              <span 
                v-for="trait in characterDetail.personality.traits" 
                :key="trait"
                class="trait-tag"
              >
                {{ trait }}
              </span>
            </div>
            
            <div v-if="characterDetail.personality.trait_evolution.length > 0" class="trait-evolution">
              <h5>性格演变</h5>
              <div 
                v-for="evolution in characterDetail.personality.trait_evolution" 
                :key="evolution.chapter_id"
                class="evolution-item"
              >
                <span class="chapter-label">{{ evolution.chapter_id }}:</span>
                <span class="evolution-desc">{{ evolution.description }}</span>
              </div>
            </div>
          </div>

          <!-- 关系网络 -->
          <div class="detail-section">
            <h4><i class="bi bi-diagram-3 me-2"></i>关系网络 ({{ characterDetail.relationships.length }})</h4>
            <div class="relationships-list">
              <div 
                v-for="relationship in characterDetail.relationships" 
                :key="relationship.character"
                :class="`relationship-item ${relationship.is_positive ? 'positive' : relationship.is_negative ? 'negative' : 'neutral'}`"
              >
                <div class="relationship-header">
                  <span class="other-character">{{ relationship.character }}</span>
                  <span class="bond-type">{{ relationship.bond_type_display }}</span>
                  <span class="strength-bar">
                    <div 
                      class="strength-fill" 
                      :style="{ width: (relationship.strength * 100) + '%' }"
                    ></div>
                  </span>
                </div>
                <p class="relationship-desc">{{ relationship.description }}</p>
                <div class="relationship-meta">
                  <span>起源: {{ relationship.origin_chapter }}</span>
                  <span>强度: {{ relationship.intensity }}</span>
                  <span v-if="relationship.evolution_count > 0">
                    演变: {{ relationship.evolution_count }}次
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="detail-section">
            <h4><i class="bi bi-bar-chart me-2"></i>统计信息</h4>
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-number">{{ characterDetail.statistics.total_events }}</div>
                <div class="stat-label">总事件数</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ characterDetail.statistics.total_relationships }}</div>
                <div class="stat-label">关系数量</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ characterDetail.statistics.chapters_appeared }}</div>
                <div class="stat-label">出场章节</div>
              </div>
              <div class="stat-card">
                <div class="stat-number">{{ characterDetail.statistics.importance_score.toFixed(1) }}</div>
                <div class="stat-label">重要性分数</div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="loading-detail">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-3">加载角色详情中...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { projectApi } from '@/api/projects'
import { useAppStore } from '@/stores/app'
import * as d3 from 'd3'

export default {
  name: 'AdvancedCharacterManagement',
  props: {
    projectId: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const appStore = useAppStore()
    
    // 响应式数据
    const characters = ref([])
    const displayCharacters = ref([])
    const selectedCharacter = ref(null)
    const characterDetail = ref(null)
    const networkData = ref({ nodes: [], edges: [], stats: {} })
    const loading = ref(false)
    const viewMode = ref('list')
    
    // 搜索和过滤
    const searchQuery = ref('')
    const searchType = ref('all')
    const filterBy = ref('all')
    const sortBy = ref('importance')
    
    // 网络视图选项
    const networkOptions = reactive({
      includeWeak: false,
      minStrength: 0.3
    })

    // 计算属性
    const filteredCharacters = computed(() => {
      if (!searchQuery.value) {
        return characters.value
      }
      
      return characters.value.filter(character => {
        const query = searchQuery.value.toLowerCase()
        
        switch (searchType.value) {
          case 'name':
            return character.name.toLowerCase().includes(query)
          case 'description':
            return character.description.toLowerCase().includes(query)
          case 'personality':
            return character.personality_tags.some(tag => 
              tag.toLowerCase().includes(query)
            )
          default:
            return character.name.toLowerCase().includes(query) ||
                   character.description.toLowerCase().includes(query) ||
                   character.personality_tags.some(tag => 
                     tag.toLowerCase().includes(query)
                   )
        }
      })
    })

    // 方法
    const loadCharacters = async () => {
      try {
        loading.value = true
        const response = await projectApi.getAdvancedCharacterList(props.projectId, {
          sort_by: sortBy.value,
          filter_by: filterBy.value
        })
        
        characters.value = response.data || []
        displayCharacters.value = filteredCharacters.value
      } catch (error) {
        console.error('Failed to load characters:', error)
        appStore.showError('加载角色列表失败')
      } finally {
        loading.value = false
      }
    }

    const selectCharacter = async (character) => {
      selectedCharacter.value = character
      characterDetail.value = null
      
      try {
        const response = await projectApi.getCharacterDetail(props.projectId, character.name)
        characterDetail.value = response.data
      } catch (error) {
        console.error('Failed to load character detail:', error)
        appStore.showError('加载角色详情失败')
      }
    }

    const handleSearch = () => {
      displayCharacters.value = filteredCharacters.value
    }

    const loadNetworkData = async () => {
      try {
        const response = await projectApi.getRelationshipNetwork(props.projectId, {
          include_weak: networkOptions.includeWeak,
          min_strength: networkOptions.minStrength
        })
        
        networkData.value = response.data || { nodes: [], edges: [], stats: {} }
        await nextTick()
        renderNetwork()
      } catch (error) {
        console.error('Failed to load network data:', error)
        appStore.showError('加载关系网络失败')
      }
    }

    const renderNetwork = () => {
      // 清除现有的网络图
      d3.select('#network-container').selectAll('*').remove()
      
      if (!networkData.value.nodes || networkData.value.nodes.length === 0) {
        return
      }

      const container = d3.select('#network-container')
      const width = 800
      const height = 600

      const svg = container
        .append('svg')
        .attr('width', width)
        .attr('height', height)

      // 创建力导向图
      const simulation = d3.forceSimulation(networkData.value.nodes)
        .force('link', d3.forceLink(networkData.value.edges).id(d => d.id).distance(100))
        .force('charge', d3.forceManyBody().strength(-300))
        .force('center', d3.forceCenter(width / 2, height / 2))

      // 绘制连线
      const links = svg.append('g')
        .selectAll('line')
        .data(networkData.value.edges)
        .enter().append('line')
        .attr('stroke', d => d.edge_color)
        .attr('stroke-width', d => d.edge_width)
        .attr('stroke-opacity', 0.6)

      // 绘制节点
      const nodes = svg.append('g')
        .selectAll('circle')
        .data(networkData.value.nodes)
        .enter().append('circle')
        .attr('r', d => d.node_size)
        .attr('fill', d => d.node_color)
        .attr('stroke', '#fff')
        .attr('stroke-width', 2)
        .call(d3.drag()
          .on('start', dragstarted)
          .on('drag', dragged)
          .on('end', dragended))

      // 添加标签
      const labels = svg.append('g')
        .selectAll('text')
        .data(networkData.value.nodes)
        .enter().append('text')
        .text(d => d.name)
        .attr('font-size', 12)
        .attr('text-anchor', 'middle')
        .attr('dy', 4)

      // 添加工具提示
      nodes.append('title')
        .text(d => `${d.name}\n${d.description}`)

      links.append('title')
        .text(d => `${d.source.name} → ${d.target.name}\n${d.bond_type_display}\n强度: ${d.strength.toFixed(2)}`)

      // 更新位置
      simulation.on('tick', () => {
        links
          .attr('x1', d => d.source.x)
          .attr('y1', d => d.source.y)
          .attr('x2', d => d.target.x)
          .attr('y2', d => d.target.y)

        nodes
          .attr('cx', d => d.x)
          .attr('cy', d => d.y)

        labels
          .attr('x', d => d.x)
          .attr('y', d => d.y)
      })

      function dragstarted(event, d) {
        if (!event.active) simulation.alphaTarget(0.3).restart()
        d.fx = d.x
        d.fy = d.y
      }

      function dragged(event, d) {
        d.fx = event.x
        d.fy = event.y
      }

      function dragended(event, d) {
        if (!event.active) simulation.alphaTarget(0)
        d.fx = null
        d.fy = null
      }
    }

    const getImportanceLabel = (score) => {
      if (score >= 7) return '主角'
      if (score >= 4) return '重要配角'
      if (score >= 2) return '配角'
      return '次要角色'
    }

    // 监听器
    watch(filteredCharacters, (newVal) => {
      displayCharacters.value = newVal
    })

    watch(viewMode, (newMode) => {
      if (newMode === 'network') {
        loadNetworkData()
      }
    })

    // 生命周期
    onMounted(() => {
      loadCharacters()
    })

    return {
      characters,
      displayCharacters,
      selectedCharacter,
      characterDetail,
      networkData,
      loading,
      viewMode,
      searchQuery,
      searchType,
      filterBy,
      sortBy,
      networkOptions,
      loadCharacters,
      selectCharacter,
      handleSearch,
      loadNetworkData,
      getImportanceLabel
    }
  }
}
</script>

<style lang="scss" scoped>
.advanced-character-management {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.toolbar-left h2 {
  margin: 0;
  color: #495057;
  font-size: 1.5rem;
  font-weight: 600;
}

.character-count {
  color: #6c757d;
  font-size: 0.9rem;
  background: #e9ecef;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

.search-input {
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  outline: none;
  width: 200px;
  font-size: 0.9rem;
}

.search-type {
  padding: 0.5rem;
  border: none;
  background: #e9ecef;
  outline: none;
  font-size: 0.8rem;
  color: #495057;
}

.filter-select, .sort-select {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  background: white;
  outline: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: #495057;
}

.view-toggle {
  display: flex;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  overflow: hidden;
}

.view-btn {
  padding: 0.5rem 1rem;
  border: none;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
  color: #495057;
}

.view-btn:hover {
  background: #f8f9fa;
}

.view-btn.active {
  background: #0d6efd;
  color: white;
}

/* 角色列表视图 */
.character-list-view {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.loading, .empty-state {
  text-align: center;
  padding: 3rem;
  color: #6c757d;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.character-card {
  background: white;
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 4px solid #dee2e6;
  position: relative;
}

.character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

.character-card.selected {
  border-left-color: #0d6efd;
  box-shadow: 0 4px 16px rgba(13,110,253,0.15);
}

.character-card.main-character {
  border-left-color: #dc3545;
}

.character-card.supporting-character {
  border-left-color: #20c997;
}

.character-card.minor-character {
  border-left-color: #6f42c1;
}

.character-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.character-name {
  margin: 0;
  color: #212529;
  font-size: 1.25rem;
  font-weight: 600;
}

.importance-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #f8f9fa;
  padding: 0.5rem;
  border-radius: 0.5rem;
  min-width: 60px;
}

.importance-score {
  font-size: 1.1rem;
  font-weight: bold;
  color: #495057;
}

.importance-label {
  font-size: 0.7rem;
  color: #6c757d;
  text-align: center;
}

.character-description {
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.character-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.personality-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
}

.character-stats {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-label {
  color: #6c757d;
  font-size: 0.75rem;
}

.stat-value {
  font-weight: bold;
  color: #495057;
  font-size: 1rem;
}

/* 网络视图 */
.network-view {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

.network-controls {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.network-controls label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
}

.network-container {
  flex: 1;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  overflow: hidden;
}

.network-stats {
  margin-top: 1rem;
}

.stat-card {
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-card h4 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 1.1rem;
}

.stat-card p {
  margin: 0.25rem 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* 角色详情侧边栏 */
.character-detail-sidebar {
  position: fixed;
  right: 0;
  top: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 16px rgba(0,0,0,0.1);
  z-index: 1050;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
}

.sidebar-header h3 {
  margin: 0;
  color: #495057;
  font-size: 1.25rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: #6c757d;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.detail-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.detail-section h4 {
  margin: 0 0 1rem 0;
  color: #495057;
  font-size: 1.1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-item label {
  font-weight: 600;
  color: #495057;
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.info-item p {
  margin: 0;
  color: #6c757d;
  line-height: 1.4;
  font-size: 0.9rem;
}

.personality-traits {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.trait-tag {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
}

.trait-evolution {
  margin-top: 1rem;
}

.trait-evolution h5 {
  margin: 0 0 0.5rem 0;
  color: #495057;
  font-size: 0.9rem;
}

.evolution-item {
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.chapter-label {
  font-weight: bold;
  color: #0d6efd;
}

.evolution-desc {
  color: #6c757d;
}

.relationships-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.relationship-item {
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #dee2e6;
}

.relationship-item.positive {
  background: #f8fff8;
  border-left-color: #198754;
}

.relationship-item.negative {
  background: #fff8f8;
  border-left-color: #dc3545;
}

.relationship-item.neutral {
  background: #fff8f0;
  border-left-color: #fd7e14;
}

.relationship-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.other-character {
  font-weight: bold;
  color: #495057;
}

.bond-type {
  background: #e9ecef;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.8rem;
  color: #495057;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-left: auto;
  width: 60px;
}

.strength-fill {
  height: 100%;
  background: #0d6efd;
  transition: width 0.3s ease;
}

.relationship-desc {
  margin: 0.5rem 0;
  color: #6c757d;
  font-size: 0.85rem;
  line-height: 1.4;
}

.relationship-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #6c757d;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.stats-grid .stat-card {
  text-align: center;
  padding: 1rem;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: bold;
  color: #0d6efd;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #6c757d;
  font-size: 0.85rem;
}

.loading-detail {
  text-align: center;
  padding: 2rem;
  color: #6c757d;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .character-detail-sidebar {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .toolbar-right {
    flex-wrap: wrap;
  }

  .character-grid {
    grid-template-columns: 1fr;
  }

  .character-detail-sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>
