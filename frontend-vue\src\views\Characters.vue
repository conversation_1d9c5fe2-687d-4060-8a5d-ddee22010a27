<template>
  <div class="characters">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item">
              <router-link :to="`/projects/${projectId}`">{{ projectName }}</router-link>
            </li>
            <li class="breadcrumb-item active">角色管理</li>
          </ol>
        </nav>
        <h1 class="h3 mb-0">
          <i class="bi bi-people me-2"></i>
          角色管理
        </h1>
      </div>
      <div class="d-flex gap-2">
        <!-- 视图切换 -->
        <div class="btn-group me-3" role="group">
          <input
            type="radio"
            class="btn-check"
            name="viewMode"
            id="basicView"
            v-model="viewMode"
            value="basic"
          >
          <label class="btn btn-outline-secondary" for="basicView">
            <i class="bi bi-grid me-1"></i>基础视图
          </label>

          <input
            type="radio"
            class="btn-check"
            name="viewMode"
            id="advancedView"
            v-model="viewMode"
            value="advanced"
          >
          <label class="btn btn-outline-secondary" for="advancedView">
            <i class="bi bi-diagram-3 me-1"></i>高级视图
          </label>
        </div>

        <button
          class="btn btn-outline-primary"
          @click="showCreateCharacterModal = true"
        >
          <i class="bi bi-person-plus me-2"></i>
          添加角色
        </button>
        <button
          class="btn btn-outline-success"
          @click="showRelationshipModal = true"
          :disabled="characters.length < 2"
        >
          <i class="bi bi-diagram-3 me-2"></i>
          关系图谱
        </button>
      </div>
    </div>

    <!-- 基础视图 -->
    <div v-if="viewMode === 'basic'" class="basic-view">
      <!-- 搜索和筛选 -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="input-group">
            <span class="input-group-text">
              <i class="bi bi-search"></i>
            </span>
            <input
              v-model="searchQuery"
              type="text"
              class="form-control"
              placeholder="搜索角色..."
            >
          </div>
        </div>
        <div class="col-md-3">
          <select v-model="filterRole" class="form-select">
            <option value="">所有角色</option>
            <option value="protagonist">主角</option>
            <option value="antagonist">反派</option>
            <option value="supporting">配角</option>
            <option value="minor">次要角色</option>
          </select>
        </div>
        <div class="col-md-3">
          <select v-model="filterStatus" class="form-select">
            <option value="">所有状态</option>
            <option value="alive">在世</option>
            <option value="dead">已故</option>
            <option value="unknown">未知</option>
          </select>
        </div>
      </div>

      <!-- 角色列表 -->
      <div v-if="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-3 text-muted">加载角色列表...</p>
      </div>

      <div v-else-if="filteredCharacters.length === 0" class="text-center py-5">
        <i class="bi bi-people display-1 text-muted mb-3"></i>
        <h4 class="text-muted">{{ searchQuery ? '未找到匹配的角色' : '暂无角色' }}</h4>
        <p class="text-muted">
          {{ searchQuery ? '尝试调整搜索条件' : '点击上方按钮添加您的第一个角色' }}
        </p>
      </div>

      <div v-else class="row">
        <div
          v-for="character in filteredCharacters"
          :key="character.id"
          class="col-lg-4 col-md-6 mb-4"
        >
          <CharacterCard
            :character="character"
            @edit="editCharacter"
            @delete="deleteCharacter"
            @view="viewCharacter"
          />
        </div>
      </div>
    </div>

    <!-- 高级视图 -->
    <div v-if="viewMode === 'advanced'" class="advanced-view">
      <AdvancedCharacterManagement :project-id="projectId" />
    </div>

    <!-- 角色详情模态框 -->
    <CharacterDetailModal
      v-model:show="showDetailModal"
      :character="selectedCharacter"
      @updated="handleCharacterUpdated"
    />

    <!-- 创建角色模态框 -->
    <CreateCharacterModal
      v-model:show="showCreateCharacterModal"
      :project-id="projectId"
      @created="handleCharacterCreated"
    />

    <!-- 关系图谱模态框 -->
    <RelationshipModal
      v-model:show="showRelationshipModal"
      :characters="characters"
      :project-id="projectId"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useAppStore } from '@/stores/app'
import { projectApi } from '@/api/projects'
import CharacterCard from '@/components/characters/CharacterCard.vue'
import CharacterDetailModal from '@/components/modals/CharacterDetailModal.vue'
import CreateCharacterModal from '@/components/modals/CreateCharacterModal.vue'
import RelationshipModal from '@/components/modals/RelationshipModal.vue'
import AdvancedCharacterManagement from '@/components/characters/AdvancedCharacterManagement.vue'

export default {
  name: 'Characters',
  components: {
    CharacterCard,
    CharacterDetailModal,
    CreateCharacterModal,
    RelationshipModal,
    AdvancedCharacterManagement
  },
  setup() {
    const route = useRoute()
    const projectsStore = useProjectsStore()
    const appStore = useAppStore()

    const projectId = computed(() => route.params.id)
    const project = computed(() => projectsStore.currentProject)
    const projectName = computed(() => project.value?.name || '项目')

    const loading = ref(false)
    const searchQuery = ref('')
    const filterRole = ref('')
    const filterStatus = ref('')
    const viewMode = ref('basic') // 添加视图模式

    const characters = ref([])
    const selectedCharacter = ref(null)
    const showDetailModal = ref(false)
    const showCreateCharacterModal = ref(false)
    const showRelationshipModal = ref(false)

    const filteredCharacters = computed(() => {
      let filtered = characters.value

      // 搜索过滤
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        filtered = filtered.filter(character =>
          character.name.toLowerCase().includes(query) ||
          (character.description && character.description.toLowerCase().includes(query)) ||
          (character.background && character.background.toLowerCase().includes(query))
        )
      }

      // 角色类型过滤
      if (filterRole.value) {
        filtered = filtered.filter(character => character.role === filterRole.value)
      }

      // 状态过滤
      if (filterStatus.value) {
        filtered = filtered.filter(character => character.status === filterStatus.value)
      }

      return filtered
    })

    const loadCharacters = async () => {
      try {
        loading.value = true
        const response = await projectApi.getCharacters(projectId.value)
        characters.value = response.data || []
      } catch (error) {
        console.error('加载角色列表失败:', error)
        appStore.showError('加载角色列表失败')
      } finally {
        loading.value = false
      }
    }

    const viewCharacter = (character) => {
      selectedCharacter.value = character
      showDetailModal.value = true
    }

    const editCharacter = (character) => {
      selectedCharacter.value = character
      showDetailModal.value = true
    }

    const deleteCharacter = async (character) => {
      if (!confirm(`确定要删除角色"${character.name}"吗？此操作不可恢复。`)) {
        return
      }

      try {
        await projectApi.deleteCharacter(projectId.value, character.id)
        characters.value = characters.value.filter(c => c.id !== character.id)
        appStore.showSuccess(`角色"${character.name}"删除成功`)
      } catch (error) {
        console.error('删除角色失败:', error)
        appStore.showError('删除角色失败')
      }
    }

    const handleCharacterCreated = (character) => {
      characters.value.unshift(character)
      showCreateCharacterModal.value = false
      appStore.showSuccess(`角色"${character.name}"创建成功`)
    }

    const handleCharacterUpdated = (updatedCharacter) => {
      const index = characters.value.findIndex(c => c.id === updatedCharacter.id)
      if (index > -1) {
        characters.value[index] = updatedCharacter
      }
      showDetailModal.value = false
      appStore.showSuccess(`角色"${updatedCharacter.name}"更新成功`)
    }

    // 监听项目变化
    watch(() => projectId.value, (newId) => {
      if (newId) {
        loadCharacters()
      }
    }, { immediate: true })

    onMounted(() => {
      if (projectId.value) {
        loadCharacters()
      }
    })

    return {
      projectId,
      projectName,
      loading,
      searchQuery,
      filterRole,
      filterStatus,
      viewMode,
      characters,
      selectedCharacter,
      showDetailModal,
      showCreateCharacterModal,
      showRelationshipModal,
      filteredCharacters,
      viewCharacter,
      editCharacter,
      deleteCharacter,
      handleCharacterCreated,
      handleCharacterUpdated
    }
  }
}
</script>

<style lang="scss" scoped>
.characters {
  padding: 1.5rem;
}

.breadcrumb {
  margin-bottom: 0.5rem;

  a {
    color: #6c757d;
    text-decoration: none;

    &:hover {
      color: #495057;
    }
  }
}

.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

@media (max-width: 768px) {
  .characters {
    padding: 1rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .row.mb-4 > div {
    margin-bottom: 1rem;
  }
}
</style>
