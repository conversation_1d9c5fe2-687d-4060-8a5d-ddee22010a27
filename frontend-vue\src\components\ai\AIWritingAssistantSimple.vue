<template>
  <div class="ai-writing-assistant-simple">
    <!-- AI写作工具栏 -->
    <div class="ai-toolbar">
      <div class="toolbar-section">
        <h4>🤖 AI写作助手</h4>
        <div class="ai-status">
          <span class="status-dot online"></span>
          AI在线
        </div>
      </div>
      
      <div class="toolbar-actions">
        <button 
          @click="showAIPanel = !showAIPanel"
          class="btn btn-primary"
        >
          {{ showAIPanel ? '收起' : '展开' }} AI助手
        </button>
      </div>
    </div>
    
    <!-- AI助手面板 -->
    <div v-if="showAIPanel" class="ai-panel">
      <div class="panel-content">
        <h5>🤖 AI智能创作</h5>
        <p>AI写作助手正在开发中，敬请期待！</p>
        
        <!-- 简单的创作输入 -->
        <div class="form-group">
          <label>创作提示</label>
          <textarea 
            v-model="prompt" 
            class="form-control"
            rows="3"
            placeholder="输入你想要AI帮助创作的内容..."
          ></textarea>
        </div>
        
        <div class="form-actions">
          <button 
            @click="generateContent" 
            class="btn btn-primary"
            :disabled="!prompt.trim() || generating"
          >
            {{ generating ? '生成中...' : '🚀 开始创作' }}
          </button>
        </div>
        
        <!-- 生成结果 -->
        <div v-if="generatedContent" class="generated-result">
          <h6>生成结果：</h6>
          <div class="generated-text">{{ generatedContent }}</div>
          <div class="result-actions">
            <button @click="insertContent" class="btn btn-sm btn-primary">
              插入内容
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useAppStore } from '@/stores/app'

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  currentContent: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['content-generated'])

const appStore = useAppStore()

// 状态
const showAIPanel = ref(false)
const prompt = ref('')
const generating = ref(false)
const generatedContent = ref('')

// 方法
const generateContent = async () => {
  if (!prompt.value.trim()) {
    appStore.showError('请输入创作提示')
    return
  }

  generating.value = true

  try {
    // 使用统一AI助手API
    const { unifiedAIAssistantApi } = await import('@/api/unified-ai-assistant')

    const response = await unifiedAIAssistantApi.chat(props.projectId, {
      message: prompt.value,
      mode: 'generation',
      target_length: 300,
      include_suggestions: true
    })

    const result = response.data

    if (result.success) {
      if (result.generated_content) {
        generatedContent.value = result.generated_content
        appStore.showSuccess('AI创作完成！')
      } else if (result.response_text) {
        generatedContent.value = result.response_text
        appStore.showSuccess('AI响应完成！')
      } else {
        throw new Error('未收到有效的AI响应')
      }
    } else {
      throw new Error('AI创作失败')
    }

  } catch (error) {
    console.error('AI创作失败:', error)
    appStore.showError('AI创作失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    generating.value = false
  }
}

const insertContent = () => {
  emit('content-generated', {
    content: generatedContent.value,
    mode: 'insert'
  })
  appStore.showSuccess('内容已插入编辑器')
  generatedContent.value = ''
  prompt.value = ''
}
</script>

<style scoped>
.ai-writing-assistant-simple {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  margin-bottom: 1rem;
}

.ai-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px 8px 0 0;
}

.toolbar-section h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
}

.ai-status {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-dot.online {
  background: #10b981;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-primary:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.ai-panel {
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.panel-content {
  padding: 1.5rem;
}

.panel-content h5 {
  margin: 0 0 1rem 0;
  color: #374151;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
  margin-bottom: 1.5rem;
}

.generated-result {
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
}

.generated-result h6 {
  margin: 0 0 0.75rem 0;
  color: #374151;
}

.generated-text {
  white-space: pre-wrap;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.result-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}
</style>
