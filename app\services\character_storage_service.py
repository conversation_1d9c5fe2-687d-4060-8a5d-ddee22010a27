"""
角色信息存储服务
负责将提取的角色信息保存为JSON格式并存储到数据库
"""

import json
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import asdict

from app.storage.memory_storage import storage
from app.services.text_structure_analyzer import Character
from app.services.character_data_converter import get_character_data_converter

logger = logging.getLogger(__name__)

class CharacterStorageService:
    """角色信息存储服务"""
    
    def __init__(self):
        self.table = "characters"
        self.logger = logging.getLogger(__name__)
    
    def save_characters(self, project_id: str, chapter_id: str, characters: List[Character]) -> List[Dict[str, Any]]:
        """
        保存角色信息到数据库
        
        Args:
            project_id: 项目ID
            chapter_id: 章节ID
            characters: 角色列表
            
        Returns:
            保存的角色记录列表
        """
        saved_characters = []
        
        for character in characters:
            try:
                # 使用数据转换器转换格式
                converter = get_character_data_converter()
                character_data = converter.convert_extracted_character_to_db_format(
                    character, project_id, chapter_id
                )
                
                # 检查是否已存在相同角色
                existing = self._find_existing_character(project_id, character.name)
                
                if existing:
                    # 更新现有角色
                    updated_character = self._merge_character_data(existing, character_data)
                    updated_character['updated_at'] = datetime.now().isoformat()
                    
                    # 添加章节到出现记录
                    if 'appearances' not in updated_character:
                        updated_character['appearances'] = []
                    
                    if chapter_id not in updated_character['appearances']:
                        updated_character['appearances'].append(chapter_id)
                    
                    storage.update(self.table, existing['id'], updated_character)
                    saved_characters.append(updated_character)
                    
                    self.logger.info(f"更新角色: {character.name}")
                else:
                    # 创建新角色
                    character_data['appearances'] = [chapter_id]
                    created_character = storage.create(self.table, character_data)
                    saved_characters.append(created_character)
                    
                    self.logger.info(f"创建新角色: {character.name}")
                    
            except Exception as e:
                self.logger.error(f"保存角色失败 {character.name}: {e}")
                continue
        
        return saved_characters
    
    def _find_existing_character(self, project_id: str, character_name: str) -> Optional[Dict[str, Any]]:
        """查找已存在的角色"""
        try:
            characters = storage.get_by_field(self.table, 'project_id', project_id)
            for char in characters:
                if char.get('name', '').lower() == character_name.lower():
                    return char
            return None
        except Exception as e:
            self.logger.error(f"查找角色失败: {e}")
            return None
    
    def _merge_character_data(self, existing: Dict[str, Any], new_data: Dict[str, Any]) -> Dict[str, Any]:
        """合并角色数据"""
        merged = existing.copy()
        
        # 合并列表字段
        list_fields = ['personality_traits', 'abilities', 'specialties', 'skills', 'relationships']
        for field in list_fields:
            if field in new_data and new_data[field]:
                existing_items = set(str(item) for item in merged.get(field, []))
                new_items = new_data[field]
                
                for item in new_items:
                    if str(item) not in existing_items:
                        if field not in merged:
                            merged[field] = []
                        merged[field].append(item)
        
        # 更新文本字段（如果新数据更详细）
        text_fields = ['description', 'background', 'appearance', 'occupation']
        for field in text_fields:
            if field in new_data and new_data[field]:
                if not merged.get(field) or len(new_data[field]) > len(merged.get(field, '')):
                    merged[field] = new_data[field]
        
        # 更新重要性（取更高级别）
        if 'importance' in new_data:
            importance_levels = {'minor': 1, 'secondary': 2, 'main': 3}
            current_level = importance_levels.get(merged.get('importance', 'minor'), 1)
            new_level = importance_levels.get(new_data['importance'], 1)
            if new_level > current_level:
                merged['importance'] = new_data['importance']
        
        return merged
    
    def get_project_characters(self, project_id: str) -> List[Dict[str, Any]]:
        """获取项目的所有角色"""
        try:
            characters = storage.get_by_field(self.table, 'project_id', project_id)
            # 按重要性和出现次数排序
            characters.sort(key=lambda x: (
                {'main': 3, 'secondary': 2, 'minor': 1}.get(x.get('importance', 'minor'), 1),
                len(x.get('appearances', []))
            ), reverse=True)
            return characters
        except Exception as e:
            self.logger.error(f"获取项目角色失败: {e}")
            return []
    
    def get_character_by_name(self, project_id: str, character_name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取角色"""
        try:
            characters = storage.get_by_field(self.table, 'project_id', project_id)
            for char in characters:
                if char.get('name', '').lower() == character_name.lower():
                    return char
            return None
        except Exception as e:
            self.logger.error(f"获取角色失败: {e}")
            return None
    
    def export_characters_json(self, project_id: str) -> str:
        """导出角色信息为JSON格式"""
        try:
            characters = self.get_project_characters(project_id)
            
            # 清理数据，移除内部字段
            clean_characters = []
            for char in characters:
                clean_char = {k: v for k, v in char.items() 
                             if k not in ['id', 'created_at', 'updated_at', 'source']}
                clean_characters.append(clean_char)
            
            return json.dumps({
                'project_id': project_id,
                'export_time': datetime.now().isoformat(),
                'total_characters': len(clean_characters),
                'characters': clean_characters
            }, ensure_ascii=False, indent=2)
            
        except Exception as e:
            self.logger.error(f"导出角色JSON失败: {e}")
            return "{}"
    
    def get_character_statistics(self, project_id: str) -> Dict[str, Any]:
        """获取角色统计信息"""
        try:
            characters = self.get_project_characters(project_id)
            
            stats = {
                'total_characters': len(characters),
                'main_characters': len([c for c in characters if c.get('importance') == 'main']),
                'secondary_characters': len([c for c in characters if c.get('importance') == 'secondary']),
                'minor_characters': len([c for c in characters if c.get('importance') == 'minor']),
                'characters_with_abilities': len([c for c in characters if c.get('abilities')]),
                'characters_with_specialties': len([c for c in characters if c.get('specialties')]),
                'total_relationships': sum(len(c.get('relationships', [])) for c in characters),
                'most_active_character': None,
                'character_types': {}
            }
            
            # 找出最活跃的角色（出现次数最多）
            if characters:
                most_active = max(characters, key=lambda x: len(x.get('appearances', [])))
                stats['most_active_character'] = {
                    'name': most_active.get('name'),
                    'appearances': len(most_active.get('appearances', []))
                }
            
            # 统计角色类型（根据职业）
            for char in characters:
                occupation = char.get('occupation', '未知')
                stats['character_types'][occupation] = stats['character_types'].get(occupation, 0) + 1
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取角色统计失败: {e}")
            return {}
    
    def delete_character(self, character_id: str) -> bool:
        """删除角色"""
        try:
            storage.delete(self.table, character_id)
            self.logger.info(f"删除角色: {character_id}")
            return True
        except Exception as e:
            self.logger.error(f"删除角色失败: {e}")
            return False
    
    def update_character(self, character_id: str, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新角色信息"""
        try:
            updates['updated_at'] = datetime.now().isoformat()
            updated_character = storage.update(self.table, character_id, updates)
            self.logger.info(f"更新角色: {character_id}")
            return updated_character
        except Exception as e:
            self.logger.error(f"更新角色失败: {e}")
            return None

# 全局实例
character_storage_service = CharacterStorageService()
