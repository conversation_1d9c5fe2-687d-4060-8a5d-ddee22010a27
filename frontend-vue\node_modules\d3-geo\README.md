# d3-geo

<a href="https://d3js.org"><img src="https://github.com/d3/d3/raw/main/docs/public/logo.svg" width="256" height="256"></a>

This module uses spherical [GeoJSON](http://geojson.org/geojson-spec.html) to represent geographic features in JavaScript. D3 supports a wide variety of common and [unusual](https://github.com/d3/d3-geo-projection) map projections. And because D3 uses spherical geometry to represent data, you can apply any aspect to any projection by rotating geometry.

## Resources

- [Documentation](https://d3js.org/d3-geo)
- [Examples](https://observablehq.com/collection/@d3/d3-geo)
- [Releases](https://github.com/d3/d3-geo/releases)
- [Getting help](https://d3js.org/community)
