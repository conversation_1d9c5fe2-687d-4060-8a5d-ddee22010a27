<template>
  <div v-if="show" class="modal-overlay" @click="handleOverlayClick">
    <div class="modal-content ai-generation-modal" @click.stop>
      <div class="modal-header">
        <h3>🤖 AI内容生成</h3>
        <button class="close-btn" @click="$emit('close')">&times;</button>
      </div>
      
      <div class="modal-body">
        <!-- 生成类型选择 -->
        <div class="form-group">
          <label>生成类型</label>
          <select v-model="form.generationType" class="form-control">
            <option value="worldbuilding">🌍 世界观设定</option>
            <option value="character">👥 角色设定</option>
            <option value="plot">📖 情节设计</option>
            <option value="content">✍️ 小说内容</option>
          </select>
        </div>
        
        <!-- 提示词输入 -->
        <div class="form-group">
          <label>生成提示</label>
          <textarea 
            v-model="form.prompt" 
            class="form-control"
            rows="4"
            :placeholder="getPromptPlaceholder()"
            required
          ></textarea>
        </div>
        
        <!-- 生成参数 -->
        <div class="generation-params">
          <div class="param-group">
            <label>最大长度</label>
            <input 
              v-model.number="form.maxLength" 
              type="number" 
              class="form-control"
              min="100"
              max="2000"
              step="100"
            >
          </div>
          
          <div class="param-group">
            <label>创意度 ({{ form.temperature }})</label>
            <input 
              v-model.number="form.temperature" 
              type="range" 
              class="form-range"
              min="0.1"
              max="1.0"
              step="0.1"
            >
          </div>
          
          <div class="param-group">
            <label>创造性 ({{ form.creativity }})</label>
            <input 
              v-model.number="form.creativity" 
              type="range" 
              class="form-range"
              min="0.1"
              max="1.0"
              step="0.1"
            >
          </div>
        </div>
        
        <!-- 生成结果 -->
        <div v-if="generatedContent" class="generated-content">
          <div class="content-header">
            <h4>生成结果</h4>
            <div class="content-actions">
              <button @click="copyToClipboard" class="btn btn-sm btn-outline">
                📋 复制
              </button>
              <button @click="insertContent" class="btn btn-sm btn-primary">
                ✅ 使用此内容
              </button>
            </div>
          </div>
          <div class="content-text">{{ generatedContent }}</div>
        </div>
        
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>AI正在生成内容，请稍候...</p>
          <div class="loading-tips">
            <p>💡 提示：{{ getRandomTip() }}</p>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="$emit('close')" class="btn btn-secondary">取消</button>
        <button 
          @click="generateContent" 
          class="btn btn-primary"
          :disabled="loading || !form.prompt.trim()"
        >
          <span v-if="loading">生成中...</span>
          <span v-else>🚀 开始生成</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { modelsApi } from '@/api/models'
import { useAppStore } from '@/stores/app'

const props = defineProps({
  show: Boolean,
  projectId: String,
  generationType: {
    type: String,
    default: 'content'
  }
})

const emit = defineEmits(['close', 'generated'])

const appStore = useAppStore()

// 表单数据
const form = ref({
  generationType: props.generationType || 'content',
  prompt: '',
  maxLength: 1000,
  temperature: 0.7,
  creativity: 0.7
})

// 状态
const loading = ref(false)
const generatedContent = ref('')

// 提示词占位符
const getPromptPlaceholder = () => {
  const placeholders = {
    worldbuilding: '描述你想要创建的世界类型，例如：一个魔法与科技并存的现代都市世界...',
    character: '描述你想要创建的角色，例如：一个年轻的魔法师，性格勇敢但冲动...',
    plot: '描述你想要的情节发展，例如：主角发现了一个隐藏的秘密，这将改变一切...',
    content: '描述你想要的场景或情节，例如：描写主角第一次进入魔法学院的场景...'
  }
  return placeholders[form.value.generationType] || '请输入生成提示...'
}

// 随机提示
const tips = [
  '越具体的提示词，生成的内容越精准',
  '可以在提示中加入情感色彩和氛围描述',
  '尝试不同的创意度设置来获得不同风格的内容',
  '生成的内容可以作为灵感来源，再进行修改完善',
  '结合项目背景信息能生成更符合设定的内容'
]

const getRandomTip = () => {
  return tips[Math.floor(Math.random() * tips.length)]
}

// 生成内容
const generateContent = async () => {
  if (!form.value.prompt.trim()) {
    appStore.showError('请输入生成提示')
    return
  }
  
  loading.value = true
  generatedContent.value = ''
  
  try {
    // 使用统一AI助手API
    const { unifiedAIAssistantApi } = await import('@/api/unified-ai-assistant')

    const response = await unifiedAIAssistantApi.chat(props.projectId, {
      message: form.value.prompt,
      mode: 'generation',
      target_length: form.value.maxLength,
      generation_mode: form.value.generationType,
      context: {
        temperature: form.value.temperature,
        creativity: form.value.creativity
      }
    })

    const result = response.data

    if (result.success) {
      if (result.generated_content) {
        generatedContent.value = result.generated_content
      } else if (result.response_text) {
        generatedContent.value = result.response_text
      } else {
        throw new Error('未收到有效的生成内容')
      }
      appStore.showSuccess('内容生成成功！')
    } else {
      throw new Error('生成失败')
    }
  } catch (error) {
    console.error('AI生成失败:', error)
    appStore.showError('AI生成失败: ' + (error.response?.data?.detail || error.message))
  } finally {
    loading.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(generatedContent.value)
    appStore.showSuccess('内容已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    appStore.showError('复制失败')
  }
}

// 使用内容
const insertContent = () => {
  emit('generated', {
    type: form.value.generationType,
    content: generatedContent.value
  })
  emit('close')
}

// 处理遮罩点击
const handleOverlayClick = () => {
  if (!loading.value) {
    emit('close')
  }
}
</script>

<style scoped>
.ai-generation-modal {
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.generation-params {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.5rem;
}

.param-group label {
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.form-range {
  width: 100%;
  margin-top: 0.5rem;
}

.generated-content {
  margin-top: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.content-header h4 {
  margin: 0;
  color: #374151;
}

.content-actions {
  display: flex;
  gap: 0.5rem;
}

.content-text {
  padding: 1rem;
  white-space: pre-wrap;
  line-height: 1.6;
  color: #374151;
  max-height: 300px;
  overflow-y: auto;
}

.loading-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.loading-tips {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border-radius: 0.5rem;
  border-left: 4px solid #3b82f6;
}

.loading-tips p {
  margin: 0;
  font-style: italic;
  color: #1e40af;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-outline {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background: #f9fafb;
}
</style>
