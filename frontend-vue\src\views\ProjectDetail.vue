<template>
  <div class="project-detail">
    <div v-if="loading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-3 text-muted">加载项目详情...</p>
    </div>
    
    <div v-else-if="!project" class="text-center py-5">
      <i class="bi bi-exclamation-triangle display-1 text-warning mb-3"></i>
      <h4 class="text-muted">项目不存在</h4>
      <router-link to="/projects" class="btn btn-primary">
        <i class="bi bi-arrow-left me-2"></i>返回项目列表
      </router-link>
    </div>
    
    <div v-else>
      <!-- 项目头部 -->
      <div class="project-header mb-4">
        <div class="d-flex justify-content-between align-items-start">
          <div>
            <nav aria-label="breadcrumb">
              <ol class="breadcrumb">
                <li class="breadcrumb-item">
                  <router-link to="/projects">项目管理</router-link>
                </li>
                <li class="breadcrumb-item active">{{ project.name }}</li>
              </ol>
            </nav>
            <h1 class="h3 mb-2">{{ project.name }}</h1>
            <p class="text-muted mb-3">{{ project.description || '暂无描述' }}</p>
            <div class="d-flex gap-2">
              <span class="badge bg-primary">{{ getGenreLabel(project.genre) }}</span>
              <span class="badge bg-success">{{ getStatusLabel(project.status) }}</span>
            </div>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-primary">
              <i class="bi bi-pencil me-2"></i>编辑项目
            </button>
            <div class="dropdown">
              <button 
                class="btn btn-outline-secondary dropdown-toggle"
                data-bs-toggle="dropdown"
              >
                更多操作
              </button>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item" href="#">
                    <i class="bi bi-download me-2"></i>导出项目
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="#">
                    <i class="bi bi-share me-2"></i>分享项目
                  </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item text-danger" href="#">
                    <i class="bi bi-trash me-2"></i>删除项目
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div class="row mb-4">
        <div class="col-md-3 col-6 mb-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="bi bi-file-text display-4 text-primary mb-2"></i>
              <h5 class="card-title">{{ project.chapter_count || 0 }}</h5>
              <p class="card-text text-muted">章节数</p>
            </div>
          </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="bi bi-fonts display-4 text-success mb-2"></i>
              <h5 class="card-title">{{ formatNumber(project.word_count || 0) }}</h5>
              <p class="card-text text-muted">总字数</p>
            </div>
          </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="bi bi-people display-4 text-info mb-2"></i>
              <h5 class="card-title">{{ project.characters?.length || 0 }}</h5>
              <p class="card-text text-muted">角色数</p>
            </div>
          </div>
        </div>
        <div class="col-md-3 col-6 mb-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="bi bi-clock-history display-4 text-warning mb-2"></i>
              <h5 class="card-title">{{ formatDate(project.updated_at) }}</h5>
              <p class="card-text text-muted">最后更新</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 主要内容区域 -->
      <div class="row mb-4">
        <!-- 左侧：快速操作 -->
        <div class="col-lg-8">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">
                <i class="bi bi-lightning me-2"></i>快速操作
              </h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6 col-12 mb-3">
                  <router-link
                    :to="`/projects/${project.id}/worldbuilding`"
                    class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                    style="min-height: 100px;"
                  >
                    <i class="bi bi-globe display-6 mb-2"></i>
                    <span>世界观设定</span>
                  </router-link>
                </div>
                <div class="col-md-6 col-12 mb-3">
                  <router-link
                    :to="`/projects/${project.id}/writing`"
                    class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                    style="min-height: 100px;"
                  >
                    <i class="bi bi-pencil-square display-6 mb-2"></i>
                    <span>内容创作</span>
                  </router-link>
                </div>
                <div class="col-md-6 col-12 mb-3">
                  <router-link
                    :to="`/projects/${project.id}/characters`"
                    class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                    style="min-height: 100px;"
                  >
                    <i class="bi bi-people display-6 mb-2"></i>
                    <span>角色管理</span>
                  </router-link>
                </div>
                <div class="col-md-6 col-12 mb-3">
                  <router-link
                    :to="`/projects/${project.id}/timeline`"
                    class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                    style="min-height: 100px;"
                  >
                    <i class="bi bi-clock-history display-6 mb-2"></i>
                    <span>情节时间线</span>
                  </router-link>
                </div>
                <div class="col-md-6 col-12 mb-3">
                  <router-link
                    :to="`/projects/${project.id}/ai-assistant`"
                    class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                    style="min-height: 100px;"
                  >
                    <i class="bi bi-robot display-6 mb-2"></i>
                    <span>AI创作助手</span>
                    <small class="text-muted mt-1">智能分析与生成</small>
                  </router-link>
                </div>
                <div class="col-md-6 col-12 mb-3">
                  <router-link
                    :to="`/projects/${project.id}/vectorization`"
                    class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center"
                    style="min-height: 100px;"
                  >
                    <i class="bi bi-diagram-3 display-6 mb-2"></i>
                    <span>增强向量化</span>
                  </router-link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：AI助手对话 -->
        <div class="col-lg-4">
          <div class="agent-chat-container">
            <AgentChat
              :project-id="project.id"
              @status-change="handleAgentStatusChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useProjectsStore } from '@/stores/projects'
import { useAppStore } from '@/stores/app'
import AgentChat from '@/components/AgentChat.vue'

export default {
  name: 'ProjectDetail',
  components: {
    AgentChat
  },
  setup() {
    const route = useRoute()
    const projectsStore = useProjectsStore()
    const appStore = useAppStore()
    
    const project = computed(() => projectsStore.currentProject)
    const loading = computed(() => projectsStore.loading)
    
    const getGenreLabel = (genre) => {
      const genreMap = {
        'fantasy': '奇幻',
        'scifi': '科幻',
        'romance': '言情',
        'mystery': '悬疑',
        'historical': '历史',
        'urban': '都市',
        'other': '其他'
      }
      return genreMap[genre] || genre
    }
    
    const getStatusLabel = (status) => {
      const statusMap = {
        'planning': '规划中',
        'writing': '创作中',
        'completed': '已完成',
        'paused': '暂停'
      }
      return statusMap[status] || status
    }
    
    const formatNumber = (num) => {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return new Intl.NumberFormat('zh-CN').format(num)
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleDateString('zh-CN')
    }
    
    const loadProject = async (projectId) => {
      try {
        await projectsStore.fetchProject(projectId)
      } catch (error) {
        console.error('加载项目详情失败:', error)
      }
    }

    const handleAgentStatusChange = (status) => {
      if (status.status === 'success') {
        appStore.showSuccess(status.message)
      } else if (status.status === 'error') {
        appStore.showError(status.message)
      } else {
        appStore.showInfo(status.message)
      }
    }
    
    // 监听路由参数变化
    watch(() => route.params.id, (newId) => {
      if (newId) {
        loadProject(newId)
      }
    }, { immediate: true })
    
    onMounted(() => {
      const projectId = route.params.id
      if (projectId) {
        loadProject(projectId)
      }
    })
    
    return {
      project,
      loading,
      getGenreLabel,
      getStatusLabel,
      formatNumber,
      formatDate,
      handleAgentStatusChange
    }
  }
}
</script>

<style scoped>
.project-detail {
  padding: 1rem;
}

.project-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
}

.project-header .breadcrumb {
  background: none;
  padding: 0;
  margin-bottom: 1rem;
}

.project-header .breadcrumb-item a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
}

.project-header .breadcrumb-item.active {
  color: white;
}

.project-header .badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: white;
}

.agent-chat-container {
  height: 600px;
  position: sticky;
  top: 1rem;
}

.card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 12px 12px 0 0 !important;
}

.btn {
  border-radius: 8px;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .agent-chat-container {
    height: 400px;
    position: static;
    margin-top: 1rem;
  }
}

@media (max-width: 768px) {
  .project-detail {
    padding: 0.5rem;
  }

  .project-header {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }

  .agent-chat-container {
    height: 300px;
  }
}
</style>

<style lang="scss" scoped>
.project-detail {
  padding: 1.5rem;
}

.project-header {
  .breadcrumb {
    margin-bottom: 0.5rem;
  }
  
  .breadcrumb-item {
    a {
      color: #6c757d;
      text-decoration: none;
      
      &:hover {
        color: #495057;
      }
    }
    
    &.active {
      color: #495057;
    }
  }
}

.card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.btn {
  &.w-100.h-100 {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

.display-6 {
  font-size: 2rem;
}

@media (max-width: 768px) {
  .project-detail {
    padding: 1rem;
  }
  
  .project-header {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }
  }
}
</style>
