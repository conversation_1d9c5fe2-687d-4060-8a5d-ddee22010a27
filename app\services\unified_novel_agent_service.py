#!/usr/bin/env python3
"""
统一小说创作Agent服务
整合分析建议和内容生成功能
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.services.novel_agent_service import get_novel_agent_service, AgentResponse
from app.services.enhanced_novel_agent_service import (
    get_enhanced_novel_agent_service, GenerationRequest, GenerationResult, GenerationMode
)
from app.models.three_dimensional_model import ConflictType, EmotionType, PleasureType

logger = logging.getLogger(__name__)


class AgentMode(Enum):
    """Agent模式"""
    ANALYSIS = "analysis"  # 分析模式
    GENERATION = "generation"  # 生成模式
    HYBRID = "hybrid"  # 混合模式
    AUTO = "auto"  # 自动模式


class IntentType(Enum):
    """意图类型"""
    # 分析类意图
    CHARACTER_QUERY = "character_query"
    PLOT_QUERY = "plot_query"
    SCENE_QUERY = "scene_query"
    ANALYSIS_REQUEST = "analysis_request"
    
    # 生成类意图
    CONTENT_GENERATION = "content_generation"
    CHAPTER_WRITING = "chapter_writing"
    DIALOGUE_CREATION = "dialogue_creation"
    SCENE_DESCRIPTION = "scene_description"
    
    # 混合类意图
    WRITING_HELP = "writing_help"
    CREATIVE_ASSISTANCE = "creative_assistance"
    STORY_DEVELOPMENT = "story_development"
    
    # 其他
    GENERAL_QUERY = "general_query"


@dataclass
class UnifiedAgentRequest:
    """统一Agent请求"""
    query: str
    project_id: str
    mode: AgentMode = AgentMode.AUTO
    
    # 生成相关参数
    target_length: int = 500
    generation_mode: Optional[GenerationMode] = None
    target_conflict_type: Optional[ConflictType] = None
    target_emotion_type: Optional[EmotionType] = None
    target_pleasure_type: Optional[PleasureType] = None
    
    # 分析相关参数
    analysis_depth: str = "standard"  # minimal, standard, deep
    include_suggestions: bool = True
    
    # 通用参数
    context: Optional[Dict[str, Any]] = None
    options: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "query": self.query,
            "project_id": self.project_id,
            "mode": self.mode.value,
            "target_length": self.target_length,
            "generation_mode": self.generation_mode.value if self.generation_mode else None,
            "target_conflict_type": self.target_conflict_type.value if self.target_conflict_type else None,
            "target_emotion_type": self.target_emotion_type.value if self.target_emotion_type else None,
            "target_pleasure_type": self.target_pleasure_type.value if self.target_pleasure_type else None,
            "analysis_depth": self.analysis_depth,
            "include_suggestions": self.include_suggestions,
            "context": self.context,
            "options": self.options
        }


@dataclass
class UnifiedAgentResponse:
    """统一Agent响应"""
    success: bool
    mode_used: AgentMode
    intent_detected: IntentType
    
    # 分析结果
    analysis_response: Optional[str] = None
    thinking_process: List[Dict[str, Any]] = field(default_factory=list)
    related_memories: List[Dict[str, Any]] = field(default_factory=list)
    
    # 生成结果
    generated_content: Optional[str] = None
    generation_metadata: Optional[Dict[str, Any]] = None
    quality_score: float = 0.0
    
    # 通用结果
    suggestions: List[str] = field(default_factory=list)
    next_actions: List[Dict[str, Any]] = field(default_factory=list)
    execution_time: float = 0.0
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "mode_used": self.mode_used.value,
            "intent_detected": self.intent_detected.value,
            "analysis_response": self.analysis_response,
            "thinking_process": self.thinking_process,
            "related_memories": self.related_memories,
            "generated_content": self.generated_content,
            "generation_metadata": self.generation_metadata,
            "quality_score": self.quality_score,
            "suggestions": self.suggestions,
            "next_actions": self.next_actions,
            "execution_time": self.execution_time,
            "metadata": self.metadata
        }


class UnifiedNovelAgentService:
    """统一小说创作Agent服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 服务依赖
        self.analysis_agent = get_novel_agent_service()
        self.generation_agent = get_enhanced_novel_agent_service()
        
        # 意图识别模式
        self.intent_patterns = {
            # 分析类意图
            IntentType.CHARACTER_QUERY: [
                r'.*角色.*是谁.*', r'.*人物.*介绍.*', r'.*角色.*性格.*',
                r'.*角色.*关系.*', r'.*主角.*', r'.*配角.*', r'.*角色.*分析.*'
            ],
            IntentType.PLOT_QUERY: [
                r'.*剧情.*', r'.*情节.*', r'.*故事.*发展.*',
                r'.*接下来.*发生.*', r'.*后续.*剧情.*', r'.*剧情.*分析.*'
            ],
            IntentType.SCENE_QUERY: [
                r'.*场景.*', r'.*地点.*', r'.*环境.*描述.*',
                r'.*背景.*设定.*', r'.*世界观.*', r'.*场景.*分析.*'
            ],
            IntentType.ANALYSIS_REQUEST: [
                r'.*分析.*', r'.*总结.*', r'.*梳理.*',
                r'.*整理.*', r'.*统计.*', r'.*概括.*'
            ],
            
            # 生成类意图
            IntentType.CONTENT_GENERATION: [
                r'.*生成.*', r'.*创作.*', r'.*写.*章节.*',
                r'.*续写.*', r'.*扩展.*', r'.*完善.*'
            ],
            IntentType.CHAPTER_WRITING: [
                r'.*写.*章.*', r'.*下一章.*', r'.*新章节.*',
                r'.*继续.*故事.*', r'.*章节.*创作.*'
            ],
            IntentType.DIALOGUE_CREATION: [
                r'.*对话.*', r'.*交流.*', r'.*谈话.*',
                r'.*说话.*', r'.*对白.*'
            ],
            IntentType.SCENE_DESCRIPTION: [
                r'.*描写.*场景.*', r'.*环境.*描写.*', r'.*场面.*描述.*',
                r'.*氛围.*营造.*', r'.*环境.*渲染.*'
            ],
            
            # 混合类意图
            IntentType.WRITING_HELP: [
                r'.*怎么写.*', r'.*如何.*描述.*', r'.*写作.*建议.*',
                r'.*创作.*灵感.*', r'.*写作.*技巧.*'
            ],
            IntentType.CREATIVE_ASSISTANCE: [
                r'.*创意.*', r'.*灵感.*', r'.*想法.*',
                r'.*创作.*帮助.*', r'.*创作.*指导.*'
            ],
            IntentType.STORY_DEVELOPMENT: [
                r'.*故事.*发展.*', r'.*情节.*推进.*', r'.*剧情.*设计.*',
                r'.*故事.*构思.*', r'.*情节.*安排.*'
            ]
        }
    
    async def process_request(self, request: UnifiedAgentRequest) -> UnifiedAgentResponse:
        """处理统一Agent请求"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"处理统一Agent请求: {request.query[:50]}...")
            
            # 1. 意图识别
            intent = self._identify_intent(request.query)
            
            # 2. 模式决策
            mode = self._decide_mode(request.mode, intent)
            
            # 3. 根据模式处理请求
            if mode == AgentMode.ANALYSIS:
                response = await self._handle_analysis_mode(request, intent)
            elif mode == AgentMode.GENERATION:
                response = await self._handle_generation_mode(request, intent)
            elif mode == AgentMode.HYBRID:
                response = await self._handle_hybrid_mode(request, intent)
            else:
                response = await self._handle_auto_mode(request, intent)
            
            # 4. 设置通用信息
            response.mode_used = mode
            response.intent_detected = intent
            response.execution_time = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(f"请求处理完成: 模式={mode.value}, 意图={intent.value}")
            return response
            
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return UnifiedAgentResponse(
                success=False,
                mode_used=AgentMode.AUTO,
                intent_detected=IntentType.GENERAL_QUERY,
                analysis_response=f"处理失败: {str(e)}",
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    def _identify_intent(self, query: str) -> IntentType:
        """识别用户意图"""
        query_lower = query.lower()
        
        # 按优先级检查意图模式
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.match(pattern, query_lower):
                    return intent_type
        
        return IntentType.GENERAL_QUERY
    
    def _decide_mode(self, requested_mode: AgentMode, intent: IntentType) -> AgentMode:
        """决定处理模式"""
        if requested_mode != AgentMode.AUTO:
            return requested_mode
        
        # 基于意图自动决定模式
        analysis_intents = {
            IntentType.CHARACTER_QUERY, IntentType.PLOT_QUERY, 
            IntentType.SCENE_QUERY, IntentType.ANALYSIS_REQUEST
        }
        
        generation_intents = {
            IntentType.CONTENT_GENERATION, IntentType.CHAPTER_WRITING,
            IntentType.DIALOGUE_CREATION, IntentType.SCENE_DESCRIPTION
        }
        
        hybrid_intents = {
            IntentType.WRITING_HELP, IntentType.CREATIVE_ASSISTANCE,
            IntentType.STORY_DEVELOPMENT
        }
        
        if intent in analysis_intents:
            return AgentMode.ANALYSIS
        elif intent in generation_intents:
            return AgentMode.GENERATION
        elif intent in hybrid_intents:
            return AgentMode.HYBRID
        else:
            return AgentMode.ANALYSIS  # 默认分析模式
    
    async def _handle_analysis_mode(
        self, 
        request: UnifiedAgentRequest, 
        intent: IntentType
    ) -> UnifiedAgentResponse:
        """处理分析模式"""
        try:
            # 调用分析Agent
            analysis_result = await self.analysis_agent.process_user_query(
                request.project_id, request.query
            )
            
            # 转换为统一响应格式
            response = UnifiedAgentResponse(
                success=analysis_result.success,
                mode_used=AgentMode.ANALYSIS,
                intent_detected=intent,
                analysis_response=analysis_result.response,
                thinking_process=[step.__dict__ for step in analysis_result.thinking_process or []],
                related_memories=analysis_result.related_memories or [],
                suggestions=analysis_result.suggestions or []
            )
            
            # 添加下一步行动建议
            response.next_actions = self._generate_next_actions_for_analysis(intent, analysis_result)
            
            return response
            
        except Exception as e:
            self.logger.error(f"分析模式处理失败: {e}")
            return UnifiedAgentResponse(
                success=False,
                mode_used=AgentMode.ANALYSIS,
                intent_detected=intent,
                analysis_response=f"分析失败: {str(e)}"
            )
    
    async def _handle_generation_mode(
        self, 
        request: UnifiedAgentRequest, 
        intent: IntentType
    ) -> UnifiedAgentResponse:
        """处理生成模式"""
        try:
            # 构建生成请求
            generation_request = GenerationRequest(
                project_id=request.project_id,
                user_prompt=request.query,
                generation_mode=request.generation_mode or self._map_intent_to_generation_mode(intent),
                target_length=request.target_length,
                target_conflict_type=request.target_conflict_type,
                target_emotion_type=request.target_emotion_type,
                target_pleasure_type=request.target_pleasure_type
            )
            
            # 调用生成Agent
            generation_result = await self.generation_agent.generate_content(generation_request)
            
            # 转换为统一响应格式
            response = UnifiedAgentResponse(
                success=generation_result.success,
                mode_used=AgentMode.GENERATION,
                intent_detected=intent,
                generated_content=generation_result.content,
                generation_metadata={
                    "word_count": generation_result.word_count,
                    "segments_generated": generation_result.segments_generated,
                    "session_id": generation_result.session_id
                },
                quality_score=generation_result.quality_score,
                suggestions=generation_result.suggestions
            )
            
            # 添加下一步行动建议
            response.next_actions = self._generate_next_actions_for_generation(intent, generation_result)
            
            return response
            
        except Exception as e:
            self.logger.error(f"生成模式处理失败: {e}")
            return UnifiedAgentResponse(
                success=False,
                mode_used=AgentMode.GENERATION,
                intent_detected=intent,
                analysis_response=f"生成失败: {str(e)}"
            )


    async def _handle_hybrid_mode(
        self,
        request: UnifiedAgentRequest,
        intent: IntentType
    ) -> UnifiedAgentResponse:
        """处理混合模式"""
        try:
            # 先进行分析
            analysis_result = await self.analysis_agent.process_user_query(
                request.project_id, request.query
            )

            # 基于分析结果决定是否需要生成
            should_generate = self._should_generate_content(request.query, analysis_result)

            response = UnifiedAgentResponse(
                success=analysis_result.success,
                mode_used=AgentMode.HYBRID,
                intent_detected=intent,
                analysis_response=analysis_result.response,
                thinking_process=[step.__dict__ for step in analysis_result.thinking_process or []],
                related_memories=analysis_result.related_memories or [],
                suggestions=analysis_result.suggestions or []
            )

            if should_generate and analysis_result.success:
                # 基于分析结果生成内容
                generation_prompt = self._create_generation_prompt_from_analysis(
                    request.query, analysis_result
                )

                generation_request = GenerationRequest(
                    project_id=request.project_id,
                    user_prompt=generation_prompt,
                    generation_mode=GenerationMode.SCENE_EXPANSION,
                    target_length=min(request.target_length, 400)  # 混合模式生成较短内容
                )

                generation_result = await self.generation_agent.generate_content(generation_request)

                if generation_result.success:
                    response.generated_content = generation_result.content
                    response.generation_metadata = {
                        "word_count": generation_result.word_count,
                        "quality_score": generation_result.quality_score
                    }
                    response.quality_score = generation_result.quality_score

            # 添加混合模式的下一步建议
            response.next_actions = self._generate_next_actions_for_hybrid(intent, response)

            return response

        except Exception as e:
            self.logger.error(f"混合模式处理失败: {e}")
            return UnifiedAgentResponse(
                success=False,
                mode_used=AgentMode.HYBRID,
                intent_detected=intent,
                analysis_response=f"混合模式处理失败: {str(e)}"
            )

    async def _handle_auto_mode(
        self,
        request: UnifiedAgentRequest,
        intent: IntentType
    ) -> UnifiedAgentResponse:
        """处理自动模式"""
        # 自动模式实际上是根据意图选择最佳模式
        optimal_mode = self._decide_mode(AgentMode.AUTO, intent)

        # 更新请求模式并递归调用
        request.mode = optimal_mode
        return await self.process_request(request)

    def _map_intent_to_generation_mode(self, intent: IntentType) -> GenerationMode:
        """将意图映射到生成模式"""
        mapping = {
            IntentType.CONTENT_GENERATION: GenerationMode.NEXT_CHAPTER,
            IntentType.CHAPTER_WRITING: GenerationMode.NEXT_CHAPTER,
            IntentType.DIALOGUE_CREATION: GenerationMode.DIALOGUE_SCENE,
            IntentType.SCENE_DESCRIPTION: GenerationMode.SCENE_EXPANSION,
            IntentType.WRITING_HELP: GenerationMode.SCENE_EXPANSION,
            IntentType.CREATIVE_ASSISTANCE: GenerationMode.CHARACTER_FOCUS,
            IntentType.STORY_DEVELOPMENT: GenerationMode.NEXT_CHAPTER
        }

        return mapping.get(intent, GenerationMode.SCENE_EXPANSION)

    def _should_generate_content(self, query: str, analysis_result: AgentResponse) -> bool:
        """判断是否应该生成内容"""
        # 检查查询中是否包含生成相关关键词
        generation_keywords = ['写', '生成', '创作', '续写', '扩展', '完善', '帮我', '给我']
        query_lower = query.lower()

        return any(keyword in query_lower for keyword in generation_keywords)

    def _create_generation_prompt_from_analysis(
        self,
        original_query: str,
        analysis_result: AgentResponse
    ) -> str:
        """基于分析结果创建生成提示"""
        prompt_parts = [original_query]

        if analysis_result.related_memories:
            # 添加相关记忆作为上下文
            memory_context = "相关背景信息：\n"
            for memory in analysis_result.related_memories[:3]:  # 最多3个相关记忆
                memory_context += f"- {memory.get('content', '')[:100]}...\n"
            prompt_parts.append(memory_context)

        if analysis_result.suggestions:
            # 添加建议作为指导
            suggestions_context = "创作建议：\n"
            for suggestion in analysis_result.suggestions[:2]:  # 最多2个建议
                suggestions_context += f"- {suggestion}\n"
            prompt_parts.append(suggestions_context)

        return "\n\n".join(prompt_parts)

    def _generate_next_actions_for_analysis(
        self,
        intent: IntentType,
        analysis_result: AgentResponse
    ) -> List[Dict[str, Any]]:
        """为分析结果生成下一步行动建议"""
        actions = []

        if intent == IntentType.CHARACTER_QUERY:
            actions.extend([
                {"action": "generate_character_scene", "title": "生成角色场景", "description": "基于角色信息创作相关场景"},
                {"action": "analyze_character_development", "title": "分析角色发展", "description": "深入分析角色成长轨迹"},
                {"action": "explore_character_relationships", "title": "探索角色关系", "description": "查看角色关系网络"}
            ])
        elif intent == IntentType.PLOT_QUERY:
            actions.extend([
                {"action": "continue_plot", "title": "继续剧情", "description": "基于当前剧情生成后续内容"},
                {"action": "analyze_plot_structure", "title": "分析剧情结构", "description": "深入分析故事结构"},
                {"action": "explore_plot_alternatives", "title": "探索剧情分支", "description": "查看其他可能的剧情发展"}
            ])

        # 通用行动
        actions.append({"action": "generate_content", "title": "生成相关内容", "description": "基于分析结果创作新内容"})

        return actions

    def _generate_next_actions_for_generation(
        self,
        intent: IntentType,
        generation_result: GenerationResult
    ) -> List[Dict[str, Any]]:
        """为生成结果生成下一步行动建议"""
        actions = []

        if generation_result.quality_score < 0.6:
            actions.append({"action": "improve_content", "title": "改进内容", "description": "基于质量评估改进生成的内容"})

        actions.extend([
            {"action": "continue_generation", "title": "继续生成", "description": "在当前内容基础上继续创作"},
            {"action": "analyze_generated", "title": "分析生成内容", "description": "深入分析生成的内容质量和特点"},
            {"action": "save_content", "title": "保存内容", "description": "将生成的内容保存到项目中"}
        ])

        return actions

    def _generate_next_actions_for_hybrid(
        self,
        intent: IntentType,
        response: UnifiedAgentResponse
    ) -> List[Dict[str, Any]]:
        """为混合模式生成下一步行动建议"""
        actions = []

        if response.generated_content:
            actions.extend([
                {"action": "expand_generated", "title": "扩展生成内容", "description": "进一步扩展生成的内容"},
                {"action": "refine_content", "title": "精炼内容", "description": "优化和精炼生成的内容"}
            ])
        else:
            actions.append({"action": "generate_based_on_analysis", "title": "基于分析生成", "description": "根据分析结果生成相关内容"})

        actions.extend([
            {"action": "deep_analysis", "title": "深度分析", "description": "进行更深入的内容分析"},
            {"action": "creative_expansion", "title": "创意扩展", "description": "基于当前结果进行创意扩展"}
        ])

        return actions

    def get_capabilities(self) -> Dict[str, Any]:
        """获取Agent能力说明"""
        return {
            "modes": [
                {
                    "name": "分析模式",
                    "value": AgentMode.ANALYSIS.value,
                    "description": "深入分析小说内容，提供角色、剧情、场景等方面的洞察"
                },
                {
                    "name": "生成模式",
                    "value": AgentMode.GENERATION.value,
                    "description": "基于三维动态模型创作高质量的小说内容"
                },
                {
                    "name": "混合模式",
                    "value": AgentMode.HYBRID.value,
                    "description": "结合分析和生成，提供全面的创作支持"
                },
                {
                    "name": "自动模式",
                    "value": AgentMode.AUTO.value,
                    "description": "智能识别意图，自动选择最佳处理模式"
                }
            ],
            "capabilities": [
                "智能意图识别和模式选择",
                "深度内容分析和洞察",
                "基于三维模型的内容生成",
                "角色、剧情、场景全方位支持",
                "连续对话和上下文理解",
                "质量评估和改进建议",
                "个性化创作指导"
            ],
            "supported_intents": [intent.value for intent in IntentType]
        }


# 全局实例
unified_novel_agent_service = UnifiedNovelAgentService()


def get_unified_novel_agent_service() -> UnifiedNovelAgentService:
    """获取统一小说创作Agent服务实例"""
    return unified_novel_agent_service
