#!/usr/bin/env python3
"""
内容获取服务
自动获取已有章节内容、角色信息、世界观设定等
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime

from app.services.chapter_service import chapter_service
from app.services.character_storage_service import character_storage_service
from app.services.ai_memory_service import get_ai_memory_service
from app.services.enhanced_project_service import get_enhanced_project_service
from app.models.memory import MemoryType

logger = logging.getLogger(__name__)


@dataclass
class ContentContext:
    """内容上下文"""
    project_id: str
    current_chapter_id: Optional[str] = None
    
    # 章节信息
    previous_chapters: List[Dict[str, Any]] = field(default_factory=list)
    current_chapter: Optional[Dict[str, Any]] = None
    chapter_summary: str = ""
    
    # 角色信息
    main_characters: List[Dict[str, Any]] = field(default_factory=list)
    active_characters: List[Dict[str, Any]] = field(default_factory=list)
    character_relationships: List[Dict[str, Any]] = field(default_factory=list)
    
    # 世界观设定
    world_settings: Dict[str, Any] = field(default_factory=dict)
    locations: List[Dict[str, Any]] = field(default_factory=list)
    timeline_events: List[Dict[str, Any]] = field(default_factory=list)
    
    # 记忆信息
    relevant_memories: List[Dict[str, Any]] = field(default_factory=list)
    plot_threads: List[Dict[str, Any]] = field(default_factory=list)
    
    # 元数据
    total_word_count: int = 0
    chapter_count: int = 0
    last_updated: datetime = field(default_factory=datetime.now)


class ContentRetrievalService:
    """内容获取服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ai_memory_service = get_ai_memory_service()
        self.project_service = get_enhanced_project_service()
    
    async def get_comprehensive_context(
        self, 
        project_id: str, 
        current_chapter_id: Optional[str] = None,
        context_depth: str = "full"  # "minimal", "standard", "full"
    ) -> ContentContext:
        """获取全面的内容上下文"""
        try:
            context = ContentContext(project_id=project_id, current_chapter_id=current_chapter_id)
            
            # 1. 获取章节信息
            await self._load_chapter_context(context, context_depth)
            
            # 2. 获取角色信息
            await self._load_character_context(context, context_depth)
            
            # 3. 获取世界观设定
            await self._load_world_context(context, context_depth)
            
            # 4. 获取相关记忆
            await self._load_memory_context(context, context_depth)
            
            # 5. 计算统计信息
            self._calculate_statistics(context)
            
            self.logger.info(f"获取内容上下文完成: {context.chapter_count}章节, {len(context.main_characters)}角色")
            return context
            
        except Exception as e:
            self.logger.error(f"获取内容上下文失败: {e}")
            return ContentContext(project_id=project_id)
    
    async def _load_chapter_context(self, context: ContentContext, depth: str):
        """加载章节上下文"""
        try:
            # 获取所有章节
            chapters = chapter_service.get_chapters(context.project_id)
            context.chapter_count = len(chapters)
            
            if depth == "minimal":
                # 只获取最近3章
                context.previous_chapters = chapters[-3:] if len(chapters) > 3 else chapters
            elif depth == "standard":
                # 获取最近5章
                context.previous_chapters = chapters[-5:] if len(chapters) > 5 else chapters
            else:  # full
                # 获取所有章节
                context.previous_chapters = chapters
            
            # 获取当前章节详情
            if context.current_chapter_id:
                current_chapter = chapter_service.get_chapter(context.project_id, context.current_chapter_id)
                if current_chapter:
                    context.current_chapter = {
                        "id": current_chapter.id,
                        "title": current_chapter.title,
                        "content": current_chapter.content,
                        "word_count": current_chapter.word_count,
                        "created_at": current_chapter.created_at
                    }
            
            # 生成章节摘要
            if context.previous_chapters:
                context.chapter_summary = self._generate_chapter_summary(context.previous_chapters)
                
        except Exception as e:
            self.logger.error(f"加载章节上下文失败: {e}")
    
    async def _load_character_context(self, context: ContentContext, depth: str):
        """加载角色上下文"""
        try:
            # 获取项目角色
            all_characters = character_storage_service.get_project_characters(context.project_id)
            
            # 分类角色
            main_chars = []
            active_chars = []
            
            for char in all_characters:
                char_info = {
                    "id": char.get("id", ""),
                    "name": char.get("name", ""),
                    "description": char.get("description", ""),
                    "personality_tags": char.get("personality_tags", []),
                    "appearance": char.get("appearance", ""),
                    "current_status": char.get("current_status", ""),
                    "goals": char.get("goals", []),
                    "abilities": char.get("abilities", []),
                    "weaknesses": char.get("weaknesses", [])
                }
                
                # 判断是否为主要角色（基于描述长度和出现频率）
                if len(char.get("description", "")) > 50:
                    main_chars.append(char_info)
                else:
                    active_chars.append(char_info)
            
            context.main_characters = main_chars
            context.active_characters = active_chars
            
            # 如果需要详细信息，获取角色关系
            if depth in ["standard", "full"]:
                context.character_relationships = await self._get_character_relationships(context.project_id)
                
        except Exception as e:
            self.logger.error(f"加载角色上下文失败: {e}")
    
    async def _load_world_context(self, context: ContentContext, depth: str):
        """加载世界观上下文"""
        try:
            # 搜索世界观相关的记忆
            world_memories = self.ai_memory_service.search_memories(
                query="世界观 设定 规则 魔法 科技",
                project_id=context.project_id,
                memory_types=[MemoryType.WORLD],
                limit=10
            )

            if world_memories:
                # 从记忆中提取世界观信息
                world_setting = ""
                for memory in world_memories:
                    world_setting += memory.memory.content + "\n"

                context.world_settings = {
                    "genre": "",
                    "setting": world_setting.strip(),
                    "theme": "",
                    "style": "",
                    "background": ""
                }
            
            # 获取场景记忆
            if depth in ["standard", "full"]:
                scene_memories = self.ai_memory_service.search_memories(
                    query="",
                    project_id=context.project_id,
                    memory_types=[MemoryType.SCENE],
                    limit=20
                )
                
                context.locations = [
                    {
                        "content": memory.memory.content,
                        "similarity": memory.similarity_score,
                        "metadata": memory.memory.metadata
                    }
                    for memory in scene_memories
                ]
                
        except Exception as e:
            self.logger.error(f"加载世界观上下文失败: {e}")
    
    async def _load_memory_context(self, context: ContentContext, depth: str):
        """加载记忆上下文"""
        try:
            # 获取相关记忆
            if context.current_chapter:
                # 基于当前章节内容搜索相关记忆
                query = context.current_chapter.get("content", "")[:200]
            elif context.previous_chapters:
                # 基于最后一章内容搜索
                last_chapter = context.previous_chapters[-1]
                # ChapterListItem对象需要使用属性访问，不是字典
                if hasattr(last_chapter, 'title'):
                    # 如果是ChapterListItem对象，使用title作为查询
                    query = getattr(last_chapter, 'title', '')
                else:
                    # 如果是字典，使用原来的方式
                    query = last_chapter.get("content", "")[:200]
            else:
                query = ""
            
            if query:
                relevant_memories = self.ai_memory_service.search_memories(
                    query=query,
                    project_id=context.project_id,
                    limit=15,
                    similarity_threshold=0.3
                )
                
                context.relevant_memories = [
                    {
                        "content": memory.memory.content,
                        "type": memory.memory.type,
                        "similarity": memory.similarity_score,
                        "metadata": memory.memory.metadata
                    }
                    for memory in relevant_memories
                ]
            
            # 获取剧情线索
            if depth == "full":
                plot_memories = self.ai_memory_service.search_memories(
                    query="",
                    project_id=context.project_id,
                    memory_types=[MemoryType.PLOT],
                    limit=10
                )
                
                context.plot_threads = [
                    {
                        "content": memory.memory.content,
                        "metadata": memory.memory.metadata
                    }
                    for memory in plot_memories
                ]
                
        except Exception as e:
            self.logger.error(f"加载记忆上下文失败: {e}")
    
    async def _get_character_relationships(self, project_id: str) -> List[Dict[str, Any]]:
        """获取角色关系"""
        try:
            # 搜索包含关系信息的记忆
            relationship_memories = self.ai_memory_service.search_memories(
                query="关系 交流 对话 互动",
                project_id=project_id,
                limit=10,
                similarity_threshold=0.2
            )
            
            relationships = []
            for memory in relationship_memories:
                relationships.append({
                    "content": memory.memory.content,
                    "type": memory.memory.type,
                    "metadata": memory.memory.metadata
                })
            
            return relationships
            
        except Exception as e:
            self.logger.error(f"获取角色关系失败: {e}")
            return []
    
    def _generate_chapter_summary(self, chapters) -> str:
        """生成章节摘要"""
        try:
            if not chapters:
                return ""

            # 处理ChapterListItem对象或字典
            total_words = 0
            chapter_titles = []

            for i, chapter in enumerate(chapters):
                if hasattr(chapter, 'word_count'):
                    # ChapterListItem对象
                    total_words += getattr(chapter, 'word_count', 0)
                    chapter_titles.append(getattr(chapter, 'title', f"第{i+1}章"))
                else:
                    # 字典对象
                    total_words += chapter.get("word_count", 0)
                    chapter_titles.append(chapter.get("title", f"第{i+1}章"))

            summary = f"共{len(chapters)}章，总计{total_words}字。"
            if len(chapter_titles) <= 5:
                summary += f"章节：{', '.join(chapter_titles)}"
            else:
                summary += f"从《{chapter_titles[0]}》到《{chapter_titles[-1]}》"

            return summary

        except Exception as e:
            self.logger.error(f"生成章节摘要失败: {e}")
            return "章节摘要生成失败"
    
    def _calculate_statistics(self, context: ContentContext):
        """计算统计信息"""
        try:
            # 计算总字数
            total_words = 0
            for chapter in context.previous_chapters:
                if hasattr(chapter, 'word_count'):
                    # ChapterListItem对象
                    total_words += getattr(chapter, 'word_count', 0)
                else:
                    # 字典对象
                    total_words += chapter.get("word_count", 0)

            context.total_word_count = total_words

            # 更新时间戳
            context.last_updated = datetime.now()

        except Exception as e:
            self.logger.error(f"计算统计信息失败: {e}")
    
    async def get_context_for_generation(
        self, 
        project_id: str, 
        generation_type: str = "next_chapter",
        specific_requirements: List[str] = None
    ) -> Dict[str, Any]:
        """为内容生成获取特定上下文"""
        try:
            context = await self.get_comprehensive_context(project_id, context_depth="standard")
            
            # 根据生成类型定制上下文
            if generation_type == "next_chapter":
                return self._prepare_next_chapter_context(context)
            elif generation_type == "character_scene":
                return self._prepare_character_scene_context(context, specific_requirements)
            elif generation_type == "dialogue":
                return self._prepare_dialogue_context(context, specific_requirements)
            else:
                return self._prepare_general_context(context)
                
        except Exception as e:
            self.logger.error(f"获取生成上下文失败: {e}")
            return {}
    
    def _prepare_next_chapter_context(self, context: ContentContext) -> Dict[str, Any]:
        """准备下一章生成上下文"""
        return {
            "type": "next_chapter",
            "previous_chapters": context.previous_chapters[-3:],  # 最近3章
            "main_characters": context.main_characters,
            "world_settings": context.world_settings,
            "plot_threads": context.plot_threads,
            "chapter_summary": context.chapter_summary,
            "total_chapters": context.chapter_count
        }
    
    def _prepare_character_scene_context(self, context: ContentContext, requirements: List[str]) -> Dict[str, Any]:
        """准备角色场景生成上下文"""
        # 根据需求筛选相关角色
        relevant_characters = context.main_characters
        if requirements:
            char_names = [req for req in requirements if any(char["name"] in req for char in context.main_characters)]
            if char_names:
                relevant_characters = [char for char in context.main_characters if char["name"] in str(char_names)]
        
        return {
            "type": "character_scene",
            "characters": relevant_characters,
            "relationships": context.character_relationships,
            "locations": context.locations,
            "world_settings": context.world_settings,
            "requirements": requirements or []
        }
    
    def _prepare_dialogue_context(self, context: ContentContext, requirements: List[str]) -> Dict[str, Any]:
        """准备对话生成上下文"""
        return {
            "type": "dialogue",
            "characters": context.main_characters,
            "relationships": context.character_relationships,
            "recent_content": context.previous_chapters[-1:] if context.previous_chapters else [],
            "requirements": requirements or []
        }
    
    def _prepare_general_context(self, context: ContentContext) -> Dict[str, Any]:
        """准备通用上下文"""
        return {
            "type": "general",
            "chapters": context.previous_chapters,
            "characters": context.main_characters,
            "world_settings": context.world_settings,
            "memories": context.relevant_memories
        }


# 全局实例
content_retrieval_service = ContentRetrievalService()


def get_content_retrieval_service() -> ContentRetrievalService:
    """获取内容获取服务实例"""
    return content_retrieval_service
