#!/usr/bin/env python3
"""
智能意图识别系统
使用AI和规则结合的方式识别用户意图
"""

import logging
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from app.services.ai_model_service import ai_model_service

logger = logging.getLogger(__name__)


class IntentCategory(Enum):
    """意图类别"""
    ANALYSIS = "analysis"  # 分析类
    GENERATION = "generation"  # 生成类
    HYBRID = "hybrid"  # 混合类
    QUERY = "query"  # 查询类
    HELP = "help"  # 帮助类


@dataclass
class IntentRecognitionResult:
    """意图识别结果"""
    primary_intent: str
    intent_category: IntentCategory
    confidence: float
    entities: Dict[str, List[str]] = field(default_factory=dict)
    keywords: List[str] = field(default_factory=list)
    context_clues: List[str] = field(default_factory=list)
    suggested_mode: str = "auto"
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "primary_intent": self.primary_intent,
            "intent_category": self.intent_category.value,
            "confidence": self.confidence,
            "entities": self.entities,
            "keywords": self.keywords,
            "context_clues": self.context_clues,
            "suggested_mode": self.suggested_mode
        }


class IntelligentIntentRecognizer:
    """智能意图识别器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 规则库
        self.intent_rules = self._initialize_intent_rules()
        self.entity_patterns = self._initialize_entity_patterns()
        self.context_indicators = self._initialize_context_indicators()
        
        # 关键词权重
        self.keyword_weights = {
            # 分析类关键词
            "分析": 0.9, "总结": 0.8, "梳理": 0.8, "整理": 0.7, "统计": 0.7,
            "概括": 0.8, "解释": 0.7, "说明": 0.6, "介绍": 0.6, "是什么": 0.8,
            "怎么样": 0.7, "什么样": 0.7, "特点": 0.6, "性格": 0.7, "关系": 0.7,
            
            # 生成类关键词
            "生成": 0.9, "创作": 0.9, "写": 0.8, "续写": 0.9, "扩展": 0.8,
            "完善": 0.7, "创造": 0.8, "编写": 0.8, "构思": 0.7, "设计": 0.7,
            "帮我写": 0.9, "给我写": 0.9, "帮我创作": 0.9,
            
            # 查询类关键词
            "查看": 0.6, "查找": 0.6, "搜索": 0.6, "找": 0.5, "看": 0.4,
            "显示": 0.5, "列出": 0.6, "获取": 0.5,
            
            # 帮助类关键词
            "怎么": 0.7, "如何": 0.7, "方法": 0.6, "技巧": 0.6, "建议": 0.7,
            "指导": 0.7, "帮助": 0.6, "教": 0.6, "学": 0.5
        }
    
    def _initialize_intent_rules(self) -> Dict[str, Dict[str, Any]]:
        """初始化意图规则"""
        return {
            # 角色相关意图
            "character_analysis": {
                "patterns": [
                    r'.*角色.*是.*', r'.*人物.*介绍.*', r'.*角色.*性格.*',
                    r'.*角色.*特点.*', r'.*主角.*怎么样.*', r'.*配角.*分析.*'
                ],
                "category": IntentCategory.ANALYSIS,
                "keywords": ["角色", "人物", "主角", "配角", "性格", "特点"],
                "confidence_boost": 0.2
            },
            "character_generation": {
                "patterns": [
                    r'.*写.*角色.*', r'.*创作.*人物.*', r'.*生成.*角色.*',
                    r'.*设计.*角色.*', r'.*角色.*对话.*'
                ],
                "category": IntentCategory.GENERATION,
                "keywords": ["写", "创作", "生成", "设计", "角色", "人物", "对话"],
                "confidence_boost": 0.2
            },
            
            # 剧情相关意图
            "plot_analysis": {
                "patterns": [
                    r'.*剧情.*分析.*', r'.*情节.*梳理.*', r'.*故事.*发展.*',
                    r'.*剧情.*怎么样.*', r'.*情节.*特点.*'
                ],
                "category": IntentCategory.ANALYSIS,
                "keywords": ["剧情", "情节", "故事", "发展", "分析", "梳理"],
                "confidence_boost": 0.2
            },
            "plot_generation": {
                "patterns": [
                    r'.*写.*剧情.*', r'.*续写.*故事.*', r'.*接下来.*发生.*',
                    r'.*后续.*剧情.*', r'.*继续.*故事.*'
                ],
                "category": IntentCategory.GENERATION,
                "keywords": ["写", "续写", "接下来", "后续", "继续", "剧情", "故事"],
                "confidence_boost": 0.2
            },
            
            # 场景相关意图
            "scene_analysis": {
                "patterns": [
                    r'.*场景.*分析.*', r'.*环境.*描述.*', r'.*地点.*介绍.*',
                    r'.*背景.*设定.*', r'.*世界观.*'
                ],
                "category": IntentCategory.ANALYSIS,
                "keywords": ["场景", "环境", "地点", "背景", "世界观", "分析", "描述"],
                "confidence_boost": 0.2
            },
            "scene_generation": {
                "patterns": [
                    r'.*描写.*场景.*', r'.*写.*环境.*', r'.*营造.*氛围.*',
                    r'.*场景.*描述.*', r'.*环境.*渲染.*'
                ],
                "category": IntentCategory.GENERATION,
                "keywords": ["描写", "写", "营造", "场景", "环境", "氛围", "渲染"],
                "confidence_boost": 0.2
            },
            
            # 写作帮助意图
            "writing_help": {
                "patterns": [
                    r'.*怎么写.*', r'.*如何.*描述.*', r'.*写作.*建议.*',
                    r'.*创作.*技巧.*', r'.*写作.*方法.*'
                ],
                "category": IntentCategory.HYBRID,
                "keywords": ["怎么写", "如何", "建议", "技巧", "方法", "写作", "创作"],
                "confidence_boost": 0.1
            },
            
            # 内容生成意图
            "content_generation": {
                "patterns": [
                    r'.*生成.*章节.*', r'.*写.*章.*', r'.*创作.*内容.*',
                    r'.*帮我写.*', r'.*给我写.*'
                ],
                "category": IntentCategory.GENERATION,
                "keywords": ["生成", "写", "创作", "章节", "内容", "帮我", "给我"],
                "confidence_boost": 0.3
            }
        }
    
    def _initialize_entity_patterns(self) -> Dict[str, str]:
        """初始化实体提取模式"""
        return {
            "character_name": r'(?:角色|人物|主角|配角)[：:]?([^，。！？\s]+)',
            "scene_name": r'(?:场景|地点|环境)[：:]?([^，。！？\s]+)',
            "chapter_ref": r'第?(\d+)章',
            "emotion": r'(开心|悲伤|愤怒|恐惧|惊讶|厌恶|期待|信任|紧张|兴奋|失望|满足)',
            "action": r'(战斗|对话|思考|回忆|描述|叙述|追逐|逃跑|相遇|分别)',
            "genre": r'(科幻|奇幻|现实|历史|悬疑|推理|爱情|冒险|武侠|都市)',
            "writing_element": r'(开头|结尾|高潮|转折|铺垫|伏笔|悬念|冲突)'
        }
    
    def _initialize_context_indicators(self) -> Dict[str, List[str]]:
        """初始化上下文指示器"""
        return {
            "analysis_context": [
                "分析一下", "总结一下", "梳理一下", "整理一下",
                "解释一下", "说明一下", "介绍一下", "概括一下"
            ],
            "generation_context": [
                "帮我写", "给我写", "创作一个", "生成一段",
                "续写", "扩展", "完善", "编写"
            ],
            "query_context": [
                "查看", "查找", "搜索", "找一下",
                "显示", "列出", "获取"
            ],
            "help_context": [
                "怎么写", "如何", "什么方法", "有什么技巧",
                "建议", "指导", "教我", "帮助"
            ]
        }
    
    async def recognize_intent(
        self, 
        query: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> IntentRecognitionResult:
        """识别用户意图"""
        try:
            self.logger.info(f"识别意图: {query[:50]}...")
            
            # 1. 规则匹配
            rule_result = self._rule_based_recognition(query)
            
            # 2. AI增强识别
            ai_result = await self._ai_enhanced_recognition(query, context)
            
            # 3. 实体提取
            entities = self._extract_entities(query)
            
            # 4. 关键词分析
            keywords = self._extract_keywords(query)
            
            # 5. 上下文分析
            context_clues = self._analyze_context(query)
            
            # 6. 综合决策
            final_result = self._combine_results(
                rule_result, ai_result, entities, keywords, context_clues
            )
            
            self.logger.info(f"意图识别完成: {final_result.primary_intent} (置信度: {final_result.confidence:.2f})")
            return final_result
            
        except Exception as e:
            self.logger.error(f"意图识别失败: {e}")
            return IntentRecognitionResult(
                primary_intent="general_query",
                intent_category=IntentCategory.QUERY,
                confidence=0.5
            )
    
    def _rule_based_recognition(self, query: str) -> Tuple[str, IntentCategory, float]:
        """基于规则的意图识别"""
        query_lower = query.lower()
        best_intent = "general_query"
        best_category = IntentCategory.QUERY
        best_score = 0.0
        
        for intent_name, rule in self.intent_rules.items():
            score = 0.0
            
            # 模式匹配
            for pattern in rule["patterns"]:
                if re.search(pattern, query_lower):
                    score += 0.5
                    break
            
            # 关键词匹配
            keyword_matches = 0
            for keyword in rule["keywords"]:
                if keyword in query_lower:
                    keyword_matches += 1
            
            if keyword_matches > 0:
                score += (keyword_matches / len(rule["keywords"])) * 0.4
            
            # 置信度提升
            score += rule.get("confidence_boost", 0.0)
            
            if score > best_score:
                best_score = score
                best_intent = intent_name
                best_category = rule["category"]
        
        return best_intent, best_category, min(best_score, 1.0)
    
    async def _ai_enhanced_recognition(
        self, 
        query: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[str, float]:
        """AI增强的意图识别"""
        try:
            prompt = f"""
请分析以下用户查询的意图类型：

用户查询：{query}

请从以下意图类型中选择最匹配的：
1. character_analysis - 分析角色信息
2. character_generation - 生成角色内容
3. plot_analysis - 分析剧情
4. plot_generation - 生成剧情内容
5. scene_analysis - 分析场景
6. scene_generation - 生成场景描述
7. writing_help - 写作帮助
8. content_generation - 内容生成
9. general_query - 一般查询

请返回JSON格式：
{{
    "intent": "意图类型",
    "confidence": 0.0-1.0的置信度,
    "reasoning": "判断理由"
}}

只返回JSON，不要其他内容：
"""
            
            response = ai_model_service.generate_text(
                prompt=prompt,
                max_tokens=200,
                temperature=0.3
            )
            
            # 解析AI响应
            try:
                json_start = response.find('{')
                json_end = response.rfind('}')
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end + 1]
                    result = json.loads(json_str)
                    return result.get("intent", "general_query"), result.get("confidence", 0.5)
            except:
                pass
            
            return "general_query", 0.5
            
        except Exception as e:
            self.logger.error(f"AI意图识别失败: {e}")
            return "general_query", 0.5
    
    def _extract_entities(self, query: str) -> Dict[str, List[str]]:
        """提取实体"""
        entities = {}
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, query)
            if matches:
                entities[entity_type] = matches
        
        return entities
    
    def _extract_keywords(self, query: str) -> List[str]:
        """提取关键词"""
        keywords = []
        query_lower = query.lower()
        
        for keyword, weight in self.keyword_weights.items():
            if keyword in query_lower:
                keywords.append(keyword)
        
        return keywords
    
    def _analyze_context(self, query: str) -> List[str]:
        """分析上下文线索"""
        context_clues = []
        query_lower = query.lower()
        
        for context_type, indicators in self.context_indicators.items():
            for indicator in indicators:
                if indicator in query_lower:
                    context_clues.append(f"{context_type}:{indicator}")
        
        return context_clues
    
    def _combine_results(
        self,
        rule_result: Tuple[str, IntentCategory, float],
        ai_result: Tuple[str, float],
        entities: Dict[str, List[str]],
        keywords: List[str],
        context_clues: List[str]
    ) -> IntentRecognitionResult:
        """综合各种结果"""
        rule_intent, rule_category, rule_confidence = rule_result
        ai_intent, ai_confidence = ai_result
        
        # 权重分配
        rule_weight = 0.6
        ai_weight = 0.4
        
        # 如果规则和AI结果一致，提升置信度
        if rule_intent == ai_intent:
            final_confidence = min(rule_confidence * rule_weight + ai_confidence * ai_weight + 0.2, 1.0)
            final_intent = rule_intent
            final_category = rule_category
        else:
            # 选择置信度更高的结果
            if rule_confidence >= ai_confidence:
                final_intent = rule_intent
                final_category = rule_category
                final_confidence = rule_confidence * rule_weight + ai_confidence * ai_weight * 0.5
            else:
                final_intent = ai_intent
                # 根据AI意图推断类别
                final_category = self._infer_category_from_intent(ai_intent)
                final_confidence = ai_confidence * ai_weight + rule_confidence * rule_weight * 0.5
        
        # 根据类别建议模式
        suggested_mode = self._suggest_mode_from_category(final_category)
        
        return IntentRecognitionResult(
            primary_intent=final_intent,
            intent_category=final_category,
            confidence=final_confidence,
            entities=entities,
            keywords=keywords,
            context_clues=context_clues,
            suggested_mode=suggested_mode
        )
    
    def _infer_category_from_intent(self, intent: str) -> IntentCategory:
        """从意图推断类别"""
        if "analysis" in intent:
            return IntentCategory.ANALYSIS
        elif "generation" in intent:
            return IntentCategory.GENERATION
        elif "help" in intent:
            return IntentCategory.HELP
        else:
            return IntentCategory.QUERY
    
    def _suggest_mode_from_category(self, category: IntentCategory) -> str:
        """从类别建议模式"""
        mapping = {
            IntentCategory.ANALYSIS: "analysis",
            IntentCategory.GENERATION: "generation",
            IntentCategory.HYBRID: "hybrid",
            IntentCategory.QUERY: "analysis",
            IntentCategory.HELP: "hybrid"
        }
        return mapping.get(category, "auto")


# 全局实例
intelligent_intent_recognizer = IntelligentIntentRecognizer()


def get_intelligent_intent_recognizer() -> IntelligentIntentRecognizer:
    """获取智能意图识别器实例"""
    return intelligent_intent_recognizer
