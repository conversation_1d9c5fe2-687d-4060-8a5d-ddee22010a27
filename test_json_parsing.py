#!/usr/bin/env python3
"""
测试JSON解析修复
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.memory_extraction_service import get_memory_extraction_service

def test_json_parsing():
    """测试JSON解析功能"""
    print("🧪 测试JSON解析修复")
    print("=" * 50)
    
    # 模拟有问题的AI响应
    problematic_responses = [
        # 1. 不完整的JSON（缺少结束括号）
        '''
        {
          "characters": [
            {
              "name": "林深",
              "identity": "记忆修复师",
              "description": "穿梭于赛博朋克都市第三区的记忆修复师",
              "personality_tags": ["冷静理性", "敏锐警觉"],
              "appearance": "身形矫健，眼神锐利",
              "background": "新东京第三区的顶尖记忆修复师",
              "current_status": "正在处理异常记忆碎片",
              "goals": ["修复客户记忆", "查明客户失踪真相"],
              "abilities": ["神经导管接入", "脑波数据解析"],
              "weaknesses": ["对未知科技恐惧"]
            }
          ]
        ''',
        
        # 2. 多余的逗号
        '''
        {
          "characters": [
            {
              "name": "林深",
              "identity": "记忆修复师",
              "description": "穿梭于赛博朋克都市第三区的记忆修复师",
              "personality_tags": ["冷静理性", "敏锐警觉",],
              "appearance": "身形矫健，眼神锐利",
              "background": "新东京第三区的顶尖记忆修复师",
              "current_status": "正在处理异常记忆碎片",
              "goals": ["修复客户记忆", "查明客户失踪真相"],
              "abilities": ["神经导管接入", "脑波数据解析"],
              "weaknesses": ["对未知科技恐惧"],
            },
          ]
        }
        ''',
        
        # 3. 正常的JSON
        '''
        {
          "characters": [
            {
              "name": "林深",
              "identity": "记忆修复师",
              "description": "穿梭于赛博朋克都市第三区的记忆修复师",
              "personality_tags": ["冷静理性", "敏锐警觉"],
              "appearance": "身形矫健，眼神锐利",
              "background": "新东京第三区的顶尖记忆修复师",
              "current_status": "正在处理异常记忆碎片",
              "goals": ["修复客户记忆", "查明客户失踪真相"],
              "abilities": ["神经导管接入", "脑波数据解析"],
              "weaknesses": ["对未知科技恐惧"]
            }
          ]
        }
        '''
    ]
    
    service = get_memory_extraction_service()
    
    for i, response in enumerate(problematic_responses, 1):
        print(f"\n{i}. 测试响应 {i}:")
        print(f"   原始长度: {len(response)} 字符")
        
        try:
            # 测试JSON提取
            json_str = service._extract_json_from_response(response)
            print(f"   提取成功: {len(json_str)} 字符")
            
            # 测试JSON解析
            data = json.loads(json_str)
            characters = data.get('characters', [])
            print(f"   解析成功: {len(characters)} 个角色")
            
            for char in characters:
                print(f"     - {char.get('name', 'Unknown')}")
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
    
    return True

def test_character_extraction_with_real_content():
    """使用真实内容测试角色提取"""
    print("\n🧪 使用真实内容测试角色提取")
    print("=" * 50)
    
    # 使用您提供的内容
    real_content = """
    林深穿梭在新东京第三区的霓虹灯海中，他的金属义眼在蓝光中闪烁。作为一名记忆修复师，
    他专门处理那些被病毒侵蚀的记忆碎片。今天的客户是一个虚拟性爱程序成瘾者，脑区已经
    严重损伤。
    
    "Zero，开始扫描，"林深通过后颈的神经接口与AI助手连接。
    
    Zero是与林深大脑皮层直接连接的AI助手，它用机械音回应："扫描开始，检测到异常协议入侵。"
    
    突然，一股未知的数据流冲击了林深的神经系统，Zero的声音戛然而止。林深意识到，
    这次的案子比想象中更加危险。
    """
    
    try:
        service = get_memory_extraction_service()
        
        print("1. 开始提取角色信息...")
        characters = service._extract_characters(real_content)
        
        print(f"   提取结果: {len(characters)} 个角色")
        
        if characters:
            for i, char in enumerate(characters, 1):
                print(f"\n   角色 {i}: {char.name}")
                print(f"     身份: {char.identity}")
                print(f"     描述: {char.description[:100]}...")
                print(f"     性格: {char.personality_tags}")
                print(f"     外貌: {char.appearance}")
        else:
            print("   ⚠️  没有提取到角色信息")
        
        return len(characters) > 0
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 JSON解析修复测试")
    print("=" * 60)
    
    success = True
    
    # 测试JSON解析修复
    success &= test_json_parsing()
    
    # 测试真实内容的角色提取
    success &= test_character_extraction_with_real_content()
    
    if success:
        print("\n✅ 所有测试通过！JSON解析修复成功。")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
