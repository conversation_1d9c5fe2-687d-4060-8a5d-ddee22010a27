"""
记忆提取服务
使用Ollama从小说内容中提取结构化信息
"""

import json
import logging
import requests
from typing import List, Dict, Any, Optional
from datetime import datetime

from app.models.memory import (
    MemoryEntry, MemoryType, MemoryExtractionRequest, MemoryExtractionResult,
    CharacterMemory, SceneMemory, WorldMemory, PlotMemory, TaskMemory, RelationshipMemory
)

logger = logging.getLogger(__name__)


class MemoryExtractionService:
    """记忆提取服务"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434/api/generate"
        self.model_name = "qwen3:4b"  # 使用项目配置的模型
    
    def extract_memories(self, request: MemoryExtractionRequest) -> MemoryExtractionResult:
        """从内容中提取记忆"""
        try:
            logger.info(f"开始提取记忆，项目: {request.project_id}, 章节: {request.chapter_id}")
            
            result = MemoryExtractionResult(success=True)
            
            # 根据请求的类型提取不同的记忆
            logger.info(f"请求的提取类型: {request.extract_types}")
            for memory_type in request.extract_types:
                logger.info(f"处理记忆类型: {memory_type}")
                if memory_type == MemoryType.CHARACTER:
                    logger.info("开始提取角色信息...")
                    characters = self._extract_characters(request.content)
                    logger.info(f"提取到 {len(characters)} 个角色")
                    result.characters.extend(characters)
                    
                elif memory_type == MemoryType.SCENE:
                    scenes = self._extract_scenes(request.content)
                    result.scenes.extend(scenes)
                    
                elif memory_type == MemoryType.WORLD:
                    world_info = self._extract_world_info(request.content)
                    result.world_info.extend(world_info)
                    
                elif memory_type == MemoryType.PLOT:
                    plots = self._extract_plots(request.content)
                    result.plots.extend(plots)
                    
                elif memory_type == MemoryType.TASK:
                    tasks = self._extract_tasks(request.content)
                    result.tasks.extend(tasks)
                    
                elif memory_type == MemoryType.RELATIONSHIP:
                    relationships = self._extract_relationships(request.content)
                    result.relationships.extend(relationships)
            
            # 转换为通用记忆条目
            result.extracted_memories = self._convert_to_memory_entries(
                request, result
            )
            
            logger.info(f"记忆提取完成，共提取 {len(result.extracted_memories)} 条记忆")
            return result
            
        except Exception as e:
            logger.error(f"记忆提取失败: {e}")
            return MemoryExtractionResult(
                success=False,
                error_message=str(e)
            )
    
    def _call_ollama(self, prompt: str, max_tokens: int = 1000) -> Optional[str]:
        """调用Ollama API"""
        try:
            response = requests.post(
                self.ollama_url,
                json={
                    'model': self.model_name,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': 0.3,  # 较低温度确保结构化输出
                        'num_predict': max_tokens
                    }
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                logger.error(f"Ollama API错误: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"调用Ollama失败: {e}")
            return None
    
    def _extract_characters(self, content: str) -> List[CharacterMemory]:
        """提取角色信息"""
        logger.info("开始提取角色信息...")

        prompt = f"""分析以下小说内容，提取角色信息。

内容：
{content}

要求：
1. 只输出JSON格式，不要任何其他文字
2. 不要使用<think>标签或思考过程
3. 直接输出完整的JSON

JSON格式：
{{
  "characters": [
    {{
      "name": "角色名",
      "identity": "身份/职业",
      "description": "角色描述",
      "personality_tags": ["性格1", "性格2"],
      "appearance": "外貌描述",
      "background": "背景信息",
      "current_status": "当前状态",
      "goals": ["目标1", "目标2"],
      "abilities": ["能力1", "能力2"],
      "weaknesses": ["弱点1", "弱点2"]
    }}
  ]
}}"""

        logger.info("调用AI模型提取角色信息...")
        response = self._call_ollama(prompt)
        if not response:
            logger.warning("AI模型没有返回响应")
            return []

        logger.info(f"AI响应长度: {len(response)} 字符")
        logger.debug(f"AI响应内容: {response[:500]}...")

        try:
            # 提取JSON部分
            json_str = self._extract_json_from_response(response)
            logger.info(f"提取的JSON长度: {len(json_str)} 字符")
            logger.debug(f"提取的JSON内容: {json_str}")

            # 尝试解析JSON
            try:
                data = json.loads(json_str)
            except json.JSONDecodeError as e:
                logger.warning(f"首次JSON解析失败: {e}")
                logger.info("尝试使用更宽松的JSON解析...")

                # 尝试使用ast.literal_eval作为备选方案
                try:
                    import ast
                    # 将JSON转换为Python字典格式
                    python_str = json_str.replace('true', 'True').replace('false', 'False').replace('null', 'None')
                    data = ast.literal_eval(python_str)
                except:
                    logger.error(f"备选解析方案也失败，原始JSON: {json_str}")
                    raise e

            logger.info(f"解析JSON成功，包含 {len(data.get('characters', []))} 个角色")

            characters = []
            for i, char_data in enumerate(data.get('characters', [])):
                try:
                    char_name = char_data.get('name', f'Unknown_{i}')
                    logger.info(f"处理角色 {i+1}: {char_name}")

                    character = CharacterMemory(
                        name=char_name,
                        identity=char_data.get('identity', ''),
                        description=char_data.get('description', ''),
                        personality_tags=char_data.get('personality_tags', []),
                        appearance=char_data.get('appearance', ''),
                        background=char_data.get('background', ''),
                        current_status=char_data.get('current_status', ''),
                        goals=char_data.get('goals', []),
                        abilities=char_data.get('abilities', []),
                        weaknesses=char_data.get('weaknesses', [])
                    )
                    characters.append(character)
                    logger.info(f"成功创建角色对象: {char_name}")

                except Exception as char_error:
                    logger.error(f"创建角色 {i+1} 对象失败: {char_error}")
                    logger.error(f"角色数据: {char_data}")
                    continue

            logger.info(f"成功创建 {len(characters)} 个角色对象")
            return characters

        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            logger.error(f"错误位置: 行 {e.lineno}, 列 {e.colno}")
            logger.error(f"原始响应: {response}")
            logger.error(f"提取的JSON: {json_str if 'json_str' in locals() else 'N/A'}")
            return []
        except Exception as e:
            logger.error(f"解析角色信息失败: {e}")
            logger.error(f"原始响应: {response}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _extract_scenes(self, content: str) -> List[SceneMemory]:
        """提取场景信息"""
        prompt = f"""请分析以下小说内容，提取其中的场景信息。

内容：
{content}

请以JSON格式输出场景信息，格式如下：
{{
  "scenes": [
    {{
      "name": "场景名称",
      "location": "地点",
      "description": "场景描述",
      "atmosphere": "氛围",
      "geography": "地理信息",
      "climate": "气候",
      "culture": "文化背景",
      "rules": ["规则1", "规则2"],
      "dangers": ["危险1", "危险2"],
      "resources": ["资源1", "资源2"]
    }}
  ]
}}

只输出JSON，不要其他内容："""
        
        response = self._call_ollama(prompt)
        if not response:
            return []
        
        try:
            json_str = self._extract_json_from_response(response)
            data = json.loads(json_str)
            
            scenes = []
            for scene_data in data.get('scenes', []):
                scene = SceneMemory(
                    name=scene_data.get('name', ''),
                    location=scene_data.get('location', ''),
                    description=scene_data.get('description', ''),
                    atmosphere=scene_data.get('atmosphere', ''),
                    geography=scene_data.get('geography', ''),
                    climate=scene_data.get('climate', ''),
                    culture=scene_data.get('culture', ''),
                    rules=scene_data.get('rules', []),
                    dangers=scene_data.get('dangers', []),
                    resources=scene_data.get('resources', [])
                )
                scenes.append(scene)
            
            return scenes
            
        except Exception as e:
            logger.error(f"解析场景信息失败: {e}")
            return []
    
    def _extract_world_info(self, content: str) -> List[WorldMemory]:
        """提取世界观信息"""
        prompt = f"""请分析以下小说内容，提取其中的世界观设定。

内容：
{content}

请以JSON格式输出世界观信息，格式如下：
{{
  "world_info": [
    {{
      "name": "设定名称",
      "type": "设定类型",
      "description": "详细描述",
      "power_system": "能力体系",
      "magic_rules": ["魔法规则1", "魔法规则2"],
      "technology_level": "科技水平",
      "factions": [{{"name": "派系名", "description": "描述"}}],
      "races": [{{"name": "种族名", "description": "描述"}}],
      "history": "历史背景",
      "current_era": "当前时代",
      "taboos": ["禁忌1", "禁忌2"],
      "legends": ["传说1", "传说2"]
    }}
  ]
}}

只输出JSON，不要其他内容："""
        
        response = self._call_ollama(prompt)
        if not response:
            return []
        
        try:
            json_str = self._extract_json_from_response(response)
            data = json.loads(json_str)
            
            world_infos = []
            for world_data in data.get('world_info', []):
                world_info = WorldMemory(
                    name=world_data.get('name', ''),
                    type=world_data.get('type', ''),
                    description=world_data.get('description', ''),
                    power_system=world_data.get('power_system', ''),
                    magic_rules=world_data.get('magic_rules', []),
                    technology_level=world_data.get('technology_level', ''),
                    factions=world_data.get('factions', []),
                    races=world_data.get('races', []),
                    history=world_data.get('history', ''),
                    current_era=world_data.get('current_era', ''),
                    taboos=world_data.get('taboos', []),
                    legends=world_data.get('legends', [])
                )
                world_infos.append(world_info)
            
            return world_infos
            
        except Exception as e:
            logger.error(f"解析世界观信息失败: {e}")
            return []
    
    def _extract_plots(self, content: str) -> List[PlotMemory]:
        """提取剧情信息"""
        prompt = f"""请分析以下小说内容，提取其中的剧情线索。

内容：
{content}

请以JSON格式输出剧情信息，格式如下：
{{
  "plots": [
    {{
      "title": "剧情标题",
      "summary": "剧情概要",
      "type": "剧情类型",
      "conflict": "冲突描述",
      "resolution": "解决方案",
      "turning_points": ["转折点1", "转折点2"],
      "status": "进行状态",
      "progress": 0.5,
      "involved_characters": ["角色1", "角色2"],
      "related_scenes": ["场景1", "场景2"],
      "mysteries": ["悬念1", "悬念2"],
      "foreshadowing": ["伏笔1", "伏笔2"]
    }}
  ]
}}

只输出JSON，不要其他内容："""
        
        response = self._call_ollama(prompt)
        if not response:
            return []
        
        try:
            json_str = self._extract_json_from_response(response)
            data = json.loads(json_str)
            
            plots = []
            for plot_data in data.get('plots', []):
                plot = PlotMemory(
                    title=plot_data.get('title', ''),
                    summary=plot_data.get('summary', ''),
                    type=plot_data.get('type', ''),
                    conflict=plot_data.get('conflict', ''),
                    resolution=plot_data.get('resolution', ''),
                    turning_points=plot_data.get('turning_points', []),
                    status=plot_data.get('status', 'ongoing'),
                    progress=plot_data.get('progress', 0.0),
                    involved_characters=plot_data.get('involved_characters', []),
                    related_scenes=plot_data.get('related_scenes', []),
                    mysteries=plot_data.get('mysteries', []),
                    foreshadowing=plot_data.get('foreshadowing', [])
                )
                plots.append(plot)
            
            return plots

        except Exception as e:
            logger.error(f"解析剧情信息失败: {e}")
            return []

    def _extract_tasks(self, content: str) -> List[TaskMemory]:
        """提取任务目标信息"""
        prompt = f"""请分析以下小说内容，提取其中的任务和目标。

内容：
{content}

请以JSON格式输出任务信息，格式如下：
{{
  "tasks": [
    {{
      "title": "任务标题",
      "description": "任务描述",
      "type": "任务类型",
      "status": "任务状态",
      "priority": 3,
      "objective": "目标",
      "obstacles": ["障碍1", "障碍2"],
      "resources_needed": ["资源1", "资源2"],
      "assigned_to": ["角色1", "角色2"],
      "deadline": "截止时间",
      "rewards": ["奖励1", "奖励2"],
      "consequences": ["后果1", "后果2"]
    }}
  ]
}}

只输出JSON，不要其他内容："""

        response = self._call_ollama(prompt)
        if not response:
            return []

        try:
            json_str = self._extract_json_from_response(response)
            data = json.loads(json_str)

            tasks = []
            for task_data in data.get('tasks', []):
                task = TaskMemory(
                    title=task_data.get('title', ''),
                    description=task_data.get('description', ''),
                    type=task_data.get('type', ''),
                    status=task_data.get('status', 'active'),
                    priority=task_data.get('priority', 1),
                    objective=task_data.get('objective', ''),
                    obstacles=task_data.get('obstacles', []),
                    resources_needed=task_data.get('resources_needed', []),
                    assigned_to=task_data.get('assigned_to', []),
                    deadline=task_data.get('deadline'),
                    rewards=task_data.get('rewards', []),
                    consequences=task_data.get('consequences', [])
                )
                tasks.append(task)

            return tasks

        except Exception as e:
            logger.error(f"解析任务信息失败: {e}")
            return []

    def _extract_relationships(self, content: str) -> List[RelationshipMemory]:
        """提取角色关系信息"""
        prompt = f"""请分析以下小说内容，提取其中的角色关系。

内容：
{content}

请以JSON格式输出关系信息，格式如下：
{{
  "relationships": [
    {{
      "character_a": "角色A",
      "character_b": "角色B",
      "relationship_type": "关系类型",
      "description": "关系描述",
      "strength": 0.8,
      "current_status": "当前状态",
      "conflicts": ["冲突1", "冲突2"],
      "shared_goals": ["共同目标1", "共同目标2"],
      "key_moments": ["关键时刻1", "关键时刻2"]
    }}
  ]
}}

只输出JSON，不要其他内容："""

        response = self._call_ollama(prompt)
        if not response:
            return []

        try:
            json_str = self._extract_json_from_response(response)
            data = json.loads(json_str)

            relationships = []
            for rel_data in data.get('relationships', []):
                relationship = RelationshipMemory(
                    character_a=rel_data.get('character_a', ''),
                    character_b=rel_data.get('character_b', ''),
                    relationship_type=rel_data.get('relationship_type', ''),
                    description=rel_data.get('description', ''),
                    strength=rel_data.get('strength', 0.0),
                    current_status=rel_data.get('current_status', ''),
                    conflicts=rel_data.get('conflicts', []),
                    shared_goals=rel_data.get('shared_goals', []),
                    key_moments=rel_data.get('key_moments', [])
                )
                relationships.append(relationship)

            return relationships

        except Exception as e:
            logger.error(f"解析关系信息失败: {e}")
            return []

    def _extract_json_from_response(self, response: str) -> str:
        """从AI响应中提取JSON部分"""
        # 移除可能的思考标签
        if '<think>' in response:
            think_end = response.find('</think>')
            if think_end != -1:
                response = response[think_end + 8:]  # 跳过</think>

        # 查找JSON开始位置
        start_idx = response.find('{')
        if start_idx == -1:
            raise ValueError("响应中没有找到JSON")

        # 使用栈来匹配括号，找到完整的JSON
        brace_count = 0
        end_idx = len(response) - 1
        in_string = False
        escape_next = False

        for i in range(start_idx, len(response)):
            char = response[i]

            # 处理字符串内的字符
            if escape_next:
                escape_next = False
                continue

            if char == '\\':
                escape_next = True
                continue

            if char == '"':
                in_string = not in_string
                continue

            # 只在字符串外处理括号
            if not in_string:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i
                        break

        # 如果没有找到完整的JSON，尝试修复
        if brace_count != 0:
            logger.warning(f"JSON不完整，缺少 {brace_count} 个右括号，尝试修复...")
            json_str = response[start_idx:end_idx + 1]

            # 智能修复JSON结构
            json_str = self._smart_fix_json(json_str, brace_count)

            logger.info(f"修复后的JSON: {json_str[-100:]}")
        else:
            json_str = response[start_idx:end_idx + 1]

        # 尝试修复常见的JSON格式问题
        json_str = self._fix_json_format(json_str)

        return json_str

    def _fix_json_format(self, json_str: str) -> str:
        """修复常见的JSON格式问题"""
        import re

        # 检查是否在字符串中间截断
        if json_str.count('"') % 2 == 1:
            # 奇数个引号，说明在字符串中间截断
            # 找到最后一个未闭合的字符串
            last_quote = json_str.rfind('"')
            if last_quote != -1:
                # 检查这个引号前面是否有转义符
                if last_quote == 0 or json_str[last_quote - 1] != '\\':
                    json_str += '"'
                    logger.info("修复了截断的字符串")

        # 修复特定的问题模式：["文本" ]] -> ["文本"]
        json_str = re.sub(r'(\["[^"]*")\s+\]\]', r'\1]', json_str)

        # 修复缺少逗号的问题：] "key" -> ], "key"
        json_str = re.sub(r'(\])\s+("[\w]+"\s*:)', r'\1, \2', json_str)

        # 移除多余的逗号（在}或]前的逗号）
        json_str = re.sub(r',\s*([}\]])', r'\1', json_str)

        # 修复可能的换行问题
        json_str = json_str.replace('\n', ' ').replace('\r', ' ')

        # 移除多余的空格
        json_str = re.sub(r'\s+', ' ', json_str)

        # 尝试修复不完整的数组
        if json_str.count('[') > json_str.count(']'):
            missing_brackets = json_str.count('[') - json_str.count(']')
            json_str += ']' * missing_brackets
            logger.info(f"添加了 {missing_brackets} 个缺失的右方括号")

        # 检查并修复常见的截断模式
        # 如果以 "key": [ 结尾，添加 ]
        if re.search(r'"\s*:\s*\[\s*$', json_str):
            json_str += ']'
            logger.info("修复了截断的空数组")

        # 如果以 "key": "value 结尾（没有闭合引号），添加引号
        if re.search(r'"\s*:\s*"[^"]*$', json_str):
            json_str += '"'
            logger.info("修复了截断的字符串值")

        return json_str

    def _smart_fix_json(self, json_str: str, missing_braces: int) -> str:
        """智能修复JSON结构"""

        # 检查是否在字符串中间截断
        if json_str.count('"') % 2 == 1:
            # 奇数个引号，说明在字符串中间截断
            logger.info("检测到字符串截断，添加闭合引号")
            json_str += '"'

        # 检查是否有多余的方括号
        left_brackets = json_str.count('[')
        right_brackets = json_str.count(']')

        if right_brackets > left_brackets:
            # 有多余的右方括号，移除多余的
            excess_brackets = right_brackets - left_brackets
            logger.info(f"检测到 {excess_brackets} 个多余的右方括号，尝试移除")

            # 从末尾移除多余的右方括号
            for _ in range(excess_brackets):
                last_bracket = json_str.rfind(']')
                if last_bracket != -1:
                    json_str = json_str[:last_bracket] + json_str[last_bracket + 1:]

        elif left_brackets > right_brackets:
            # 缺少右方括号
            missing_brackets = left_brackets - right_brackets
            logger.info(f"添加 {missing_brackets} 个缺失的右方括号")
            json_str += ']' * missing_brackets

        # 添加缺失的右括号
        for _ in range(missing_braces):
            json_str += '}'

        return json_str

    def _convert_to_memory_entries(
        self,
        request: MemoryExtractionRequest,
        result: MemoryExtractionResult
    ) -> List[MemoryEntry]:
        """将提取的信息转换为通用记忆条目"""
        memories = []
        now = datetime.now()

        # 转换角色记忆
        for i, character in enumerate(result.characters):
            memory = MemoryEntry(
                id=f"{request.project_id}_{request.chapter_id}_char_{i}",
                project_id=request.project_id,
                chapter_id=request.chapter_id,
                type=MemoryType.CHARACTER,
                title=f"角色: {character.name}",
                content=f"{character.description}\n身份: {character.identity}\n背景: {character.background}",
                summary=f"{character.name}的角色信息",
                characters=[character.name],
                scenes=[],
                tags=character.personality_tags,
                created_at=now,
                updated_at=now,
                metadata=character.dict()
            )
            memories.append(memory)

        # 转换场景记忆
        for i, scene in enumerate(result.scenes):
            memory = MemoryEntry(
                id=f"{request.project_id}_{request.chapter_id}_scene_{i}",
                project_id=request.project_id,
                chapter_id=request.chapter_id,
                type=MemoryType.SCENE,
                title=f"场景: {scene.name}",
                content=f"{scene.description}\n地点: {scene.location}\n氛围: {scene.atmosphere}",
                summary=f"{scene.name}的场景信息",
                characters=[],
                scenes=[scene.name],
                tags=[],
                created_at=now,
                updated_at=now,
                metadata=scene.dict()
            )
            memories.append(memory)

        # 转换剧情记忆
        for i, plot in enumerate(result.plots):
            memory = MemoryEntry(
                id=f"{request.project_id}_{request.chapter_id}_plot_{i}",
                project_id=request.project_id,
                chapter_id=request.chapter_id,
                type=MemoryType.PLOT,
                title=f"剧情: {plot.title}",
                content=f"{plot.summary}\n冲突: {plot.conflict}",
                summary=plot.summary,
                characters=plot.involved_characters,
                scenes=plot.related_scenes,
                tags=[plot.type, plot.status],
                created_at=now,
                updated_at=now,
                metadata=plot.dict()
            )
            memories.append(memory)

        return memories


# 全局实例
memory_extraction_service = MemoryExtractionService()


def get_memory_extraction_service() -> MemoryExtractionService:
    """获取记忆提取服务实例"""
    return memory_extraction_service
