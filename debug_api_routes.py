#!/usr/bin/env python3
"""
调试API路由
"""

import requests
import json

def test_api_routes():
    """测试API路由"""
    print("🔍 调试API路由")
    print("=" * 50)
    
    base_url = "http://localhost:12088"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    # 测试不同的路由
    routes_to_test = [
        f"/api/v1/characters/{project_id}/advanced",
        f"/api/v1/characters/{project_id}",
        f"/characters/{project_id}/advanced",
        f"/characters/{project_id}",
        "/api/v1/characters",
        "/characters"
    ]
    
    for route in routes_to_test:
        url = f"{base_url}{route}"
        print(f"\n测试路由: {route}")
        print(f"完整URL: {url}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 成功! 数据类型: {type(data)}")
                    if isinstance(data, list):
                        print(f"   列表长度: {len(data)}")
                    elif isinstance(data, dict):
                        print(f"   字典键: {list(data.keys())}")
                except:
                    print(f"✅ 成功! 响应: {response.text[:100]}...")
            elif response.status_code == 404:
                print("❌ 路由不存在")
            elif response.status_code == 400:
                print(f"❌ 请求错误: {response.text}")
            else:
                print(f"❌ 其他错误: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接失败")
        except Exception as e:
            print(f"❌ 异常: {e}")

def test_root_endpoints():
    """测试根端点"""
    print("\n🔍 测试根端点")
    print("=" * 50)
    
    base_url = "http://localhost:12088"
    
    endpoints = [
        "/",
        "/api",
        "/api/v1",
        "/docs",
        "/openapi.json"
    ]
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n测试端点: {endpoint}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                print(f"✅ 成功! Content-Type: {content_type}")
                
                if 'json' in content_type:
                    try:
                        data = response.json()
                        print(f"   JSON数据: {str(data)[:100]}...")
                    except:
                        pass
                else:
                    print(f"   响应长度: {len(response.text)} 字符")
            else:
                print(f"❌ 错误: {response.text[:100]}...")
                
        except Exception as e:
            print(f"❌ 异常: {e}")

def check_character_data():
    """检查角色数据"""
    print("\n🔍 检查角色数据")
    print("=" * 50)
    
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from app.services.character_storage_service import character_storage_service
        
        project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
        
        # 获取角色数据
        characters = character_storage_service.get_project_characters(project_id)
        print(f"项目角色数量: {len(characters)}")
        
        if characters:
            print("角色列表:")
            for i, char in enumerate(characters, 1):
                name = char.get('name', 'Unknown')
                print(f"  {i}. {name}")
                print(f"     ID: {char.get('id', 'N/A')}")
                print(f"     描述: {char.get('description', 'N/A')[:50]}...")
        
        return len(characters) > 0
        
    except Exception as e:
        print(f"❌ 检查角色数据失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 API路由调试工具")
    print("=" * 60)
    
    # 检查角色数据
    has_data = check_character_data()
    
    # 测试根端点
    test_root_endpoints()
    
    # 测试API路由
    test_api_routes()
    
    if has_data:
        print("\n✅ 角色数据存在，问题可能在路由配置")
    else:
        print("\n❌ 角色数据不存在，需要先创建角色数据")
