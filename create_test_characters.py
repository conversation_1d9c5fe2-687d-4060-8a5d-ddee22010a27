#!/usr/bin/env python3
"""
创建测试角色数据
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"
TEST_PROJECT_ID = "63ae07ad-2bc6-4354-92a9-db4be8160215"

# 测试角色数据
TEST_CHARACTERS = [
    {
        "name": "李明",
        "description": "25岁的程序员，因为一次意外穿越到了魔法世界。性格内向但坚韧，对新事物充满好奇心。在魔法世界中展现出惊人的符文理解能力，成为了墨老的弟子。",
        "appearance": "中等身材，戴眼镜，看起来文质彬彬。穿越后逐渐适应了魔法世界的服装。",
        "personality_tags": ["内向", "坚韧", "好奇", "聪明", "谨慎"],
        "role": "protagonist",
        "status": "alive"
    },
    {
        "name": "墨老",
        "description": "神秘的魔法导师，拥有深厚的魔法造诣。表面慈祥，但隐藏着复杂的过去。曾经历过一场重大的背叛，导致他对信任变得谨慎。",
        "appearance": "白发苍苍的老者，身穿深蓝色法师袍，手持古老的法杖。眼神深邃，仿佛能看透人心。",
        "personality_tags": ["智慧", "慈祥", "神秘", "谨慎", "强大"],
        "role": "supporting",
        "status": "alive"
    },
    {
        "name": "暗影",
        "description": "神秘的黑衣刺客，墨老的宿敌。因为师父被墨老杀死而心怀仇恨，一直在寻找报仇的机会。实力强大，行踪诡秘。",
        "appearance": "全身黑衣，面戴面具，只露出一双充满仇恨的眼睛。身材修长，动作敏捷如影。",
        "personality_tags": ["冷酷", "仇恨", "坚定", "孤独", "痛苦"],
        "role": "antagonist",
        "status": "alive"
    },
    {
        "name": "艾莉娅",
        "description": "魔法学院的天才学生，出身贵族家庭。性格高傲但内心善良，对李明的快速进步既惊讶又好奇。",
        "appearance": "金发碧眼的美丽少女，总是穿着华丽的魔法袍。举止优雅，气质高贵。",
        "personality_tags": ["高傲", "天才", "善良", "好胜", "优雅"],
        "role": "supporting",
        "status": "alive"
    },
    {
        "name": "雷恩",
        "description": "李明在魔法学院的室友，性格开朗活泼。虽然魔法天赋一般，但对朋友非常忠诚。",
        "appearance": "棕发少年，脸上总是挂着笑容。身材结实，喜欢运动。",
        "personality_tags": ["开朗", "忠诚", "活泼", "普通", "友善"],
        "role": "minor",
        "status": "alive"
    }
]

def create_character(character_data):
    """创建单个角色"""
    endpoint = f"{BASE_URL}/projects/{TEST_PROJECT_ID}/characters"
    
    try:
        response = requests.post(endpoint, json=character_data, timeout=10)
        
        if response.status_code == 201:
            print(f"✅ 成功创建角色: {character_data['name']}")
            return True
        else:
            print(f"❌ 创建角色失败: {character_data['name']}")
            print(f"   状态码: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 创建角色时发生错误: {e}")
        return False

def check_existing_characters():
    """检查现有角色"""
    endpoint = f"{BASE_URL}/projects/{TEST_PROJECT_ID}/characters"
    
    try:
        response = requests.get(endpoint, timeout=10)
        
        if response.status_code == 200:
            characters = response.json()
            print(f"📊 项目中现有 {len(characters)} 个角色")
            
            if characters:
                print("📝 现有角色列表:")
                for char in characters:
                    print(f"   - {char.get('name', 'Unknown')}")
            
            return characters
        else:
            print(f"❌ 获取角色列表失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 检查现有角色时发生错误: {e}")
        return []

def main():
    """主函数"""
    print("🚀 开始创建测试角色数据")
    print(f"🎯 目标项目ID: {TEST_PROJECT_ID}")
    
    # 检查现有角色
    existing_characters = check_existing_characters()
    existing_names = [char.get('name') for char in existing_characters]
    
    # 创建新角色
    created_count = 0
    skipped_count = 0
    
    for character_data in TEST_CHARACTERS:
        character_name = character_data['name']
        
        if character_name in existing_names:
            print(f"⏭️  跳过已存在的角色: {character_name}")
            skipped_count += 1
            continue
        
        if create_character(character_data):
            created_count += 1
    
    # 结果统计
    print(f"\n📊 创建结果:")
    print(f"   ✅ 新创建: {created_count} 个角色")
    print(f"   ⏭️  跳过: {skipped_count} 个角色")
    print(f"   📝 总计: {len(existing_characters) + created_count} 个角色")
    
    if created_count > 0:
        print(f"\n🎉 成功创建 {created_count} 个测试角色!")
        print("💡 现在可以测试高级角色管理功能了")
    else:
        print(f"\n📝 没有创建新角色（可能已存在）")
    
    # 验证高级API
    print(f"\n🧪 测试高级角色管理API...")
    try:
        advanced_endpoint = f"{BASE_URL}/characters/{TEST_PROJECT_ID}"
        response = requests.get(advanced_endpoint, timeout=10)
        
        if response.status_code == 200:
            advanced_characters = response.json()
            print(f"✅ 高级API正常! 返回 {len(advanced_characters)} 个角色")
            
            if advanced_characters:
                print("📋 高级角色数据示例:")
                first_char = advanced_characters[0]
                print(f"   名称: {first_char.get('name')}")
                print(f"   重要性分数: {first_char.get('importance_score', 'N/A')}")
                print(f"   性格标签: {first_char.get('personality_tags', [])}")
        else:
            print(f"❌ 高级API测试失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 高级API测试时发生错误: {e}")

if __name__ == "__main__":
    main()
