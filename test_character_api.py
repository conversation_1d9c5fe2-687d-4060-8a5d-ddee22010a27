#!/usr/bin/env python3
"""
测试角色管理API的脚本
"""

import requests
import json
import sys

def test_character_api():
    """测试角色管理API"""
    base_url = "http://localhost:8000"
    project_id = "test_project"
    
    print("🧪 测试角色管理API")
    print("=" * 50)
    
    # 1. 测试获取角色列表
    print("1. 测试获取角色列表...")
    try:
        response = requests.get(f"{base_url}/api/v1/characters/{project_id}")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   角色数量: {len(data)}")
            if data:
                print(f"   第一个角色: {data[0].get('name', 'N/A')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 2. 测试获取角色详情
    print("\n2. 测试获取角色详情...")
    try:
        character_name = "测试角色"
        response = requests.get(f"{base_url}/api/v1/characters/{project_id}/{character_name}/detail")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   角色名称: {data.get('basic_info', {}).get('name', 'N/A')}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    # 3. 测试获取关系网络
    print("\n3. 测试获取关系网络...")
    try:
        response = requests.get(f"{base_url}/api/v1/characters/{project_id}/network")
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   节点数量: {len(data.get('nodes', []))}")
            print(f"   边数量: {len(data.get('edges', []))}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {e}")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_character_api()
