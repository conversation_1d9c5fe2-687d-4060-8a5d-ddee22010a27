import axios from 'axios'

// 统一AI创作助手API客户端
const apiClient = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 120000  // AI生成可能需要更长时间
})

// 统一AI创作助手API方法
export const unifiedAIAssistantApi = {
  // 与AI助手对话
  chat: (projectId, data) =>
    apiClient.post(`/projects/${projectId}/ai-assistant/chat`, data),

  // 分析用户意图
  analyzeIntent: (projectId, data) =>
    apiClient.post(`/projects/${projectId}/ai-assistant/analyze-intent`, data),

  // 获取AI助手能力
  getCapabilities: (projectId) =>
    apiClient.get(`/projects/${projectId}/ai-assistant/capabilities`),

  // 获取对话启动器
  getConversationStarters: (projectId) =>
    apiClient.get(`/projects/${projectId}/ai-assistant/conversation-starters`),

  // 执行快速操作
  executeQuickAction: (projectId, data) =>
    apiClient.post(`/projects/${projectId}/ai-assistant/quick-actions`, data),

  // 获取模型选项
  getModelOptions: () =>
    apiClient.get(`/projects/model-options`),

  // 流式聊天（支持实时响应）
  chatStream: async (projectId, data, onMessage, onError) => {
    try {
      const response = await fetch(`http://localhost:8000/api/v1/projects/${projectId}/ai-assistant/chat-stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      let buffer = ''
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() // 保留不完整的行

        for (const line of lines) {
          if (line.trim() === '') continue
          
          try {
            if (line.startsWith('data: ')) {
              const data = JSON.parse(line.slice(6))
              onMessage(data)
            }
          } catch (e) {
            console.warn('解析流数据失败:', e)
          }
        }
      }
    } catch (error) {
      onError(error)
    }
  }
}

// 对话会话管理API
export const conversationApi = {
  // 开始新对话
  startConversation: (projectId, initialMessage) =>
    apiClient.post(`/projects/${projectId}/ai-assistant/conversation/start`, {
      initial_message: initialMessage
    }),

  // 继续对话
  continueConversation: (projectId, sessionId, message) =>
    apiClient.post(`/projects/${projectId}/ai-assistant/conversation/${sessionId}/continue`, {
      message
    }),

  // 获取对话会话
  getConversation: (projectId, sessionId) =>
    apiClient.get(`/projects/${projectId}/ai-assistant/conversation/${sessionId}`),

  // 获取会话摘要
  getConversationSummary: (projectId, sessionId) =>
    apiClient.get(`/projects/${projectId}/ai-assistant/conversation/${sessionId}/summary`),

  // 结束对话
  endConversation: (projectId, sessionId) =>
    apiClient.post(`/projects/${projectId}/ai-assistant/conversation/${sessionId}/end`),

  // 获取对话建议
  getConversationSuggestions: (projectId, sessionId) =>
    apiClient.get(`/projects/${projectId}/ai-assistant/conversation/${sessionId}/suggestions`)
}

// AI助手工具类
export class AIAssistantHelper {
  constructor(projectId) {
    this.projectId = projectId
    this.currentSession = null
  }

  // 开始智能对话
  async startSmartConversation(initialMessage) {
    try {
      const response = await conversationApi.startConversation(this.projectId, initialMessage)
      this.currentSession = response.data.session_id
      return response.data
    } catch (error) {
      console.error('开始对话失败:', error)
      throw error
    }
  }

  // 发送消息
  async sendMessage(message, options = {}) {
    try {
      if (this.currentSession) {
        // 继续现有对话
        return await conversationApi.continueConversation(this.currentSession, message)
      } else {
        // 单次对话
        return await unifiedAIAssistantApi.chat(this.projectId, {
          message,
          mode: options.mode || 'auto',
          ...options
        })
      }
    } catch (error) {
      console.error('发送消息失败:', error)
      throw error
    }
  }

  // 分析意图
  async analyzeIntent(message) {
    try {
      return await unifiedAIAssistantApi.analyzeIntent(this.projectId, { message })
    } catch (error) {
      console.error('意图分析失败:', error)
      throw error
    }
  }

  // 获取快速操作建议
  async getQuickActions() {
    try {
      const response = await unifiedAIAssistantApi.getConversationStarters(this.projectId)
      return response.data.starters || []
    } catch (error) {
      console.error('获取快速操作失败:', error)
      return []
    }
  }

  // 执行快速操作
  async executeQuickAction(actionType, options = {}) {
    try {
      return await unifiedAIAssistantApi.executeQuickAction(this.projectId, {
        action: actionType,
        ...options
      })
    } catch (error) {
      console.error('执行快速操作失败:', error)
      throw error
    }
  }

  // 结束当前会话
  async endCurrentSession() {
    if (this.currentSession) {
      try {
        const result = await conversationApi.endConversation(this.projectId, this.currentSession)
        this.currentSession = null
        return result.data
      } catch (error) {
        console.error('结束会话失败:', error)
        this.currentSession = null
      }
    }
  }

  // 获取会话摘要
  async getSessionSummary() {
    if (this.currentSession) {
      try {
        const response = await conversationApi.getConversationSummary(this.projectId, this.currentSession)
        return response.data
      } catch (error) {
        console.error('获取会话摘要失败:', error)
        return null
      }
    }
    return null
  }
}

// 消息类型枚举
export const MessageType = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  ERROR: 'error'
}

// Agent模式枚举
export const AgentMode = {
  AUTO: 'auto',
  ANALYSIS: 'analysis',
  GENERATION: 'generation',
  HYBRID: 'hybrid'
}

// 意图类型枚举
export const IntentType = {
  CHARACTER_QUERY: 'character_query',
  PLOT_QUERY: 'plot_query',
  SCENE_QUERY: 'scene_query',
  CONTENT_GENERATION: 'content_generation',
  WRITING_HELP: 'writing_help',
  ANALYSIS_REQUEST: 'analysis_request'
}

// 响应式AI助手状态管理
export function useAIAssistant(projectId) {
  const assistant = new AIAssistantHelper(projectId)
  
  return {
    assistant,
    MessageType,
    AgentMode,
    IntentType,
    
    // 便捷方法
    async chat(message, mode = AgentMode.AUTO) {
      return assistant.sendMessage(message, { mode })
    },
    
    async analyze(message) {
      return assistant.sendMessage(message, { mode: AgentMode.ANALYSIS })
    },
    
    async generate(message, options = {}) {
      return assistant.sendMessage(message, { 
        mode: AgentMode.GENERATION,
        ...options
      })
    },
    
    async getHelp(message) {
      return assistant.sendMessage(message, { mode: AgentMode.HYBRID })
    }
  }
}
