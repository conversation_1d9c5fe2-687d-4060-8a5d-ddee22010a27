#!/usr/bin/env python3
"""
统一小说创作Agent集成测试
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.unified_novel_agent_service import (
    get_unified_novel_agent_service, UnifiedAgentRequest, AgentMode
)
from app.services.intelligent_intent_recognizer import get_intelligent_intent_recognizer
from app.services.intelligent_conversation_service import get_intelligent_conversation_service


async def test_intent_recognition():
    """测试意图识别"""
    print("🧪 测试智能意图识别")
    print("=" * 50)
    
    try:
        intent_recognizer = get_intelligent_intent_recognizer()
        
        test_queries = [
            "林深这个角色是什么性格？",
            "帮我写下一章的内容",
            "分析一下当前的剧情发展",
            "怎么写好对话场景？",
            "生成一段紧张的追逐场面"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n测试 {i}: {query}")
            
            result = await intent_recognizer.recognize_intent(query)
            
            print(f"  意图: {result.primary_intent}")
            print(f"  类别: {result.intent_category.value}")
            print(f"  置信度: {result.confidence:.2f}")
            print(f"  建议模式: {result.suggested_mode}")
            
            if result.keywords:
                print(f"  关键词: {', '.join(result.keywords)}")
            
            if result.entities:
                print(f"  实体: {result.entities}")
        
        return True
        
    except Exception as e:
        print(f"❌ 意图识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_unified_agent():
    """测试统一Agent"""
    print("\n🧪 测试统一Agent")
    print("=" * 50)
    
    try:
        agent_service = get_unified_novel_agent_service()
        project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
        
        test_cases = [
            {
                "query": "分析一下林深这个角色的性格特点",
                "mode": AgentMode.ANALYSIS,
                "expected_mode": "analysis"
            },
            {
                "query": "帮我写一段林深与神秘客户的对话",
                "mode": AgentMode.GENERATION,
                "expected_mode": "generation"
            },
            {
                "query": "怎么写好科幻小说的环境描写？",
                "mode": AgentMode.HYBRID,
                "expected_mode": "hybrid"
            },
            {
                "query": "给我一些剧情发展的建议",
                "mode": AgentMode.AUTO,
                "expected_mode": "auto"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试案例 {i}: {test_case['query']}")
            print(f"请求模式: {test_case['mode'].value}")
            
            request = UnifiedAgentRequest(
                query=test_case["query"],
                project_id=project_id,
                mode=test_case["mode"],
                target_length=300
            )
            
            result = await agent_service.process_request(request)
            
            print(f"  成功: {'是' if result.success else '否'}")
            print(f"  使用模式: {result.mode_used.value}")
            print(f"  检测意图: {result.intent_detected.value}")
            print(f"  执行时间: {result.execution_time:.2f}秒")
            
            if result.analysis_response:
                print(f"  分析响应: {result.analysis_response[:100]}...")
            
            if result.generated_content:
                print(f"  生成内容: {result.generated_content[:100]}...")
                print(f"  质量分数: {result.quality_score:.2f}")
            
            if result.suggestions:
                print(f"  建议数量: {len(result.suggestions)}")
                for j, suggestion in enumerate(result.suggestions[:2], 1):
                    print(f"    {j}. {suggestion}")
            
            if result.next_actions:
                print(f"  下一步行动: {len(result.next_actions)}个")
                for action in result.next_actions[:2]:
                    print(f"    - {action.get('title', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一Agent测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_conversation_flow():
    """测试对话流程"""
    print("\n🧪 测试智能对话流程")
    print("=" * 50)
    
    try:
        conversation_service = get_intelligent_conversation_service()
        project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
        
        # 开始对话
        initial_message = "我想分析一下林深这个角色，然后基于分析结果写一段内容"
        session = await conversation_service.start_conversation(project_id, initial_message)
        
        print(f"对话会话ID: {session.session_id}")
        print(f"初始状态: {session.state.value}")
        print(f"轮次数: {session.total_turns}")
        
        if session.turns:
            first_turn = session.turns[0]
            print(f"首轮响应成功: {'是' if first_turn.agent_response.success else '否'}")
            print(f"响应模式: {first_turn.agent_response.mode_used.value}")
            
            if first_turn.agent_response.analysis_response:
                print(f"分析内容: {first_turn.agent_response.analysis_response[:150]}...")
            
            if first_turn.agent_response.next_actions:
                print(f"建议行动: {len(first_turn.agent_response.next_actions)}个")
        
        # 继续对话
        follow_up_messages = [
            "基于这个分析，帮我写一段林深的内心独白",
            "这段内容写得怎么样？有什么可以改进的？"
        ]
        
        for i, message in enumerate(follow_up_messages, 2):
            print(f"\n第{i}轮对话: {message}")
            
            session = await conversation_service.continue_conversation(session.session_id, message)
            
            print(f"  当前状态: {session.state.value}")
            print(f"  总轮次: {session.total_turns}")
            
            if session.turns:
                latest_turn = session.turns[-1]
                print(f"  响应成功: {'是' if latest_turn.agent_response.success else '否'}")
                
                if latest_turn.agent_response.generated_content:
                    print(f"  生成内容: {latest_turn.agent_response.generated_content[:100]}...")
                    print(f"  质量分数: {latest_turn.agent_response.quality_score:.2f}")
        
        # 获取会话摘要
        summary = conversation_service.get_session_summary(session.session_id)
        print(f"\n会话摘要:")
        print(f"  总轮次: {summary['total_turns']}")
        print(f"  累积内容长度: {summary['accumulated_content_length']}")
        print(f"  最近意图: {summary.get('recent_intents', [])}")
        
        # 结束会话
        end_result = conversation_service.end_session(session.session_id)
        print(f"\n会话结束:")
        print(f"  持续时间: {end_result['duration']:.2f}秒")
        print(f"  最终状态: {end_result['final_state']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_agent_capabilities():
    """测试Agent能力"""
    print("\n🧪 测试Agent能力")
    print("=" * 50)
    
    try:
        agent_service = get_unified_novel_agent_service()
        
        capabilities = agent_service.get_capabilities()
        
        print("支持的模式:")
        for mode in capabilities["modes"]:
            print(f"  - {mode['name']} ({mode['value']}): {mode['description']}")
        
        print(f"\n核心能力:")
        for capability in capabilities["capabilities"]:
            print(f"  - {capability}")
        
        print(f"\n支持的意图类型: {len(capabilities['supported_intents'])}种")
        for intent in capabilities["supported_intents"][:5]:  # 显示前5种
            print(f"  - {intent}")
        
        return True
        
    except Exception as e:
        print(f"❌ 能力测试失败: {e}")
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 统一小说创作Agent集成测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试意图识别
    result1 = await test_intent_recognition()
    test_results.append(("意图识别", result1))
    
    # 测试统一Agent
    result2 = await test_unified_agent()
    test_results.append(("统一Agent", result2))
    
    # 测试对话流程
    result3 = await test_conversation_flow()
    test_results.append(("对话流程", result3))
    
    # 测试Agent能力
    result4 = test_agent_capabilities()
    test_results.append(("Agent能力", result4))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！统一AI创作助手功能正常。")
        print("\n📝 功能特性:")
        print("✅ 智能意图识别和模式选择")
        print("✅ 分析建议和内容生成统一")
        print("✅ 连续对话和上下文理解")
        print("✅ 质量评估和改进建议")
        print("✅ 个性化创作指导")
        
        print("\n🔧 API使用:")
        print("POST /api/v1/projects/{project_id}/ai-assistant/chat")
        print("POST /api/v1/projects/{project_id}/ai-assistant/analyze-intent")
        print("GET /api/v1/projects/{project_id}/ai-assistant/capabilities")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total


if __name__ == "__main__":
    print("🔧 统一AI创作助手测试工具")
    print("=" * 60)
    
    # 运行异步测试
    success = asyncio.run(run_all_tests())
    
    if success:
        print("\n✅ 测试完成，统一AI创作助手功能正常！")
        print("\n🎯 现在您可以:")
        print("1. 通过聊天界面与AI助手对话")
        print("2. 获得智能的分析建议和内容生成")
        print("3. 享受连续的创作支持体验")
        print("4. 基于三维模型的高质量创作")
    else:
        print("\n❌ 测试失败，请检查系统配置和依赖。")
    
    sys.exit(0 if success else 1)
