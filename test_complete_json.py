#!/usr/bin/env python3
"""
测试完整JSON解析
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.memory_extraction_service import get_memory_extraction_service

def test_complete_json():
    """测试完整的JSON解析"""
    print("🧪 测试完整JSON解析")
    print("=" * 50)
    
    # 完整的JSON响应
    complete_json = '''
    {
      "characters": [
        {
          "name": "林深",
          "identity": "记忆修复师",
          "description": "穿梭在新东京第三区的霓虹灯海中，通过神经接口与AI助手Zero连接，处理被病毒侵蚀的记忆碎片",
          "personality_tags": ["专业", "冷静"],
          "appearance": "金属义眼，身穿暗色战术服",
          "background": "专门处理被病毒侵蚀的记忆碎片，擅长修复虚拟性爱程序成瘾者的脑区损伤",
          "current_status": "正在处理异常危险的案件，遭遇未知数据流攻击",
          "goals": ["修复客户受损记忆", "查明数据流来源"],
          "abilities": ["记忆修复技术", "神经接口操作"],
          "weaknesses": ["对病毒数据敏感", "依赖AI助手配合"]
        },
        {
          "name": "Zero",
          "identity": "AI助手",
          "description": "与林深大脑皮层直接连接的AI，通过神经接口提供技术支持",
          "personality_tags": ["高效", "可靠"],
          "appearance": "机械音声线，无实体形态",
          "background": "林深的智能助手，负责检测异常协议和数据威胁",
          "current_status": "检测到异常协议入侵，协助林深应对危险",
          "goals": ["保护林深神经系统", "定位数据流源头"],
          "abilities": ["异常检测", "数据流分析"],
          "weaknesses": ["易受强力病毒攻击", "依赖神经接口稳定性"]
        }
      ]
    }
    '''
    
    try:
        service = get_memory_extraction_service()
        
        print("1. 测试JSON提取...")
        json_str = service._extract_json_from_response(complete_json)
        print(f"   提取成功: {len(json_str)} 字符")
        
        print("2. 测试JSON解析...")
        data = json.loads(json_str)
        characters_data = data.get('characters', [])
        print(f"   解析成功: {len(characters_data)} 个角色")
        
        print("3. 测试角色对象创建...")
        from app.models.memory import CharacterMemory
        
        characters = []
        for i, char_data in enumerate(characters_data):
            character = CharacterMemory(
                name=char_data.get('name', ''),
                identity=char_data.get('identity', ''),
                description=char_data.get('description', ''),
                personality_tags=char_data.get('personality_tags', []),
                appearance=char_data.get('appearance', ''),
                background=char_data.get('background', ''),
                current_status=char_data.get('current_status', ''),
                goals=char_data.get('goals', []),
                abilities=char_data.get('abilities', []),
                weaknesses=char_data.get('weaknesses', [])
            )
            characters.append(character)
            print(f"   角色 {i+1}: {character.name} - {character.identity}")
        
        print(f"\n✅ 测试成功！创建了 {len(characters)} 个角色对象")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_truncated_json_repair():
    """测试截断JSON的修复"""
    print("\n🧪 测试截断JSON修复")
    print("=" * 50)
    
    # 模拟截断的JSON
    truncated_json = '''
    {
      "characters": [
        {
          "name": "林深",
          "identity": "记忆修复师",
          "description": "穿梭在新东京第三区的霓虹灯海中",
          "personality_tags": ["专业", "冷静"],
          "appearance": "金属义眼",
          "background": "专门处理被病毒侵蚀的记忆碎片",
          "current_status": "正在处理异常危险的案件",
          "goals": ["修复客户受损记忆"],
          "abilities": ["记忆修复技术"],
          "weaknesses": ["对病毒数据敏感"
    '''
    
    try:
        service = get_memory_extraction_service()
        
        print("1. 测试截断JSON修复...")
        json_str = service._extract_json_from_response(truncated_json)
        print(f"   修复后长度: {len(json_str)} 字符")
        print(f"   修复后JSON: {json_str}")
        
        print("2. 测试修复后的JSON解析...")
        data = json.loads(json_str)
        characters_data = data.get('characters', [])
        print(f"   解析成功: {len(characters_data)} 个角色")
        
        for char_data in characters_data:
            print(f"   角色: {char_data.get('name', 'Unknown')}")
        
        print("✅ 截断JSON修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 截断JSON修复失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 JSON解析完整测试")
    print("=" * 60)
    
    success = True
    
    # 测试完整JSON
    success &= test_complete_json()
    
    # 测试截断JSON修复
    success &= test_truncated_json_repair()
    
    if success:
        print("\n✅ 所有测试通过！JSON解析功能正常。")
        print("\n💡 建议：")
        print("1. 问题可能出现在AI模型输出被截断")
        print("2. 可以尝试调整Ollama的参数，增加输出长度限制")
        print("3. 或者改进提示词，要求AI输出更简洁的JSON")
    else:
        print("\n❌ 部分测试失败，需要进一步调试。")
