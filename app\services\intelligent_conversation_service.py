#!/usr/bin/env python3
"""
智能对话流程服务
实现连续的分析-建议-生成循环对话
"""

import logging
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.services.unified_novel_agent_service import (
    get_unified_novel_agent_service, UnifiedAgentRequest, UnifiedAgentResponse, AgentMode
)
from app.services.intelligent_intent_recognizer import get_intelligent_intent_recognizer

logger = logging.getLogger(__name__)


class ConversationState(Enum):
    """对话状态"""
    INITIAL = "initial"  # 初始状态
    ANALYZING = "analyzing"  # 分析中
    SUGGESTING = "suggesting"  # 建议中
    GENERATING = "generating"  # 生成中
    REFINING = "refining"  # 精炼中
    COMPLETED = "completed"  # 完成


@dataclass
class ConversationTurn:
    """对话轮次"""
    turn_id: str
    user_message: str
    agent_response: UnifiedAgentResponse
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "turn_id": self.turn_id,
            "user_message": self.user_message,
            "agent_response": self.agent_response.to_dict(),
            "timestamp": self.timestamp.isoformat()
        }


@dataclass
class ConversationSession:
    """对话会话"""
    session_id: str
    project_id: str
    state: ConversationState = ConversationState.INITIAL
    
    # 对话历史
    turns: List[ConversationTurn] = field(default_factory=list)
    
    # 会话上下文
    context: Dict[str, Any] = field(default_factory=dict)
    accumulated_content: str = ""
    
    # 会话元数据
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    total_turns: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "project_id": self.project_id,
            "state": self.state.value,
            "turns": [turn.to_dict() for turn in self.turns],
            "context": self.context,
            "accumulated_content": self.accumulated_content,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": self.total_turns
        }


class IntelligentConversationService:
    """智能对话流程服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 服务依赖
        self.unified_agent = get_unified_novel_agent_service()
        self.intent_recognizer = get_intelligent_intent_recognizer()
        
        # 活跃会话
        self.active_sessions: Dict[str, ConversationSession] = {}
        
        # 对话流程模板
        self.conversation_flows = {
            "character_development": {
                "steps": [
                    {"action": "analyze", "prompt": "分析角色特点"},
                    {"action": "suggest", "prompt": "提供发展建议"},
                    {"action": "generate", "prompt": "生成角色内容"}
                ]
            },
            "plot_development": {
                "steps": [
                    {"action": "analyze", "prompt": "分析当前剧情"},
                    {"action": "suggest", "prompt": "提供发展方向"},
                    {"action": "generate", "prompt": "生成剧情内容"}
                ]
            },
            "scene_creation": {
                "steps": [
                    {"action": "analyze", "prompt": "分析场景需求"},
                    {"action": "suggest", "prompt": "提供描写建议"},
                    {"action": "generate", "prompt": "生成场景描述"}
                ]
            }
        }
    
    async def start_conversation(self, project_id: str, initial_message: str) -> ConversationSession:
        """开始新对话"""
        try:
            import uuid
            session_id = str(uuid.uuid4())
            
            session = ConversationSession(
                session_id=session_id,
                project_id=project_id
            )
            
            # 处理初始消息
            await self._process_message(session, initial_message)
            
            # 保存会话
            self.active_sessions[session_id] = session
            
            self.logger.info(f"开始新对话: {session_id}")
            return session
            
        except Exception as e:
            self.logger.error(f"开始对话失败: {e}")
            raise
    
    async def continue_conversation(
        self, 
        session_id: str, 
        message: str
    ) -> ConversationSession:
        """继续对话"""
        try:
            session = self.active_sessions.get(session_id)
            if not session:
                raise ValueError(f"会话不存在: {session_id}")
            
            # 处理消息
            await self._process_message(session, message)
            
            # 更新会话状态
            session.last_activity = datetime.now()
            
            self.logger.info(f"继续对话: {session_id}, 轮次: {session.total_turns}")
            return session
            
        except Exception as e:
            self.logger.error(f"继续对话失败: {e}")
            raise
    
    async def _process_message(self, session: ConversationSession, message: str):
        """处理消息"""
        try:
            # 1. 意图识别
            intent_result = await self.intent_recognizer.recognize_intent(
                message, session.context
            )
            
            # 2. 更新上下文
            self._update_context(session, message, intent_result)
            
            # 3. 决定处理策略
            strategy = self._decide_strategy(session, intent_result)
            
            # 4. 构建Agent请求
            agent_request = self._build_agent_request(session, message, strategy)
            
            # 5. 调用统一Agent
            agent_response = await self.unified_agent.process_request(agent_request)
            
            # 6. 后处理响应
            enhanced_response = await self._enhance_response(session, agent_response, strategy)
            
            # 7. 记录对话轮次
            turn = ConversationTurn(
                turn_id=f"{session.session_id}_{session.total_turns + 1}",
                user_message=message,
                agent_response=enhanced_response
            )
            
            session.turns.append(turn)
            session.total_turns += 1
            
            # 8. 更新会话状态
            self._update_session_state(session, enhanced_response)
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            raise
    
    def _update_context(self, session: ConversationSession, message: str, intent_result: Any):
        """更新会话上下文"""
        # 更新意图历史
        if "intent_history" not in session.context:
            session.context["intent_history"] = []
        
        session.context["intent_history"].append({
            "message": message,
            "intent": intent_result.primary_intent,
            "confidence": intent_result.confidence,
            "timestamp": datetime.now().isoformat()
        })
        
        # 保留最近10次意图
        session.context["intent_history"] = session.context["intent_history"][-10:]
        
        # 更新实体信息
        if intent_result.entities:
            if "entities" not in session.context:
                session.context["entities"] = {}
            
            for entity_type, values in intent_result.entities.items():
                if entity_type not in session.context["entities"]:
                    session.context["entities"][entity_type] = []
                session.context["entities"][entity_type].extend(values)
                # 去重
                session.context["entities"][entity_type] = list(set(session.context["entities"][entity_type]))
        
        # 更新关键词
        if intent_result.keywords:
            if "keywords" not in session.context:
                session.context["keywords"] = []
            session.context["keywords"].extend(intent_result.keywords)
            session.context["keywords"] = list(set(session.context["keywords"]))[-20:]  # 保留最近20个
    
    def _decide_strategy(self, session: ConversationSession, intent_result: Any) -> Dict[str, Any]:
        """决定处理策略"""
        strategy = {
            "mode": AgentMode.AUTO,
            "follow_up": False,
            "flow_type": None
        }
        
        # 基于意图决定策略
        intent = intent_result.primary_intent
        
        if "character" in intent:
            strategy["flow_type"] = "character_development"
        elif "plot" in intent:
            strategy["flow_type"] = "plot_development"
        elif "scene" in intent:
            strategy["flow_type"] = "scene_creation"
        
        # 基于对话历史决定是否需要跟进
        if len(session.turns) > 0:
            last_response = session.turns[-1].agent_response
            if last_response.suggestions and not last_response.generated_content:
                strategy["follow_up"] = True
                strategy["mode"] = AgentMode.GENERATION
        
        return strategy
    
    def _build_agent_request(
        self, 
        session: ConversationSession, 
        message: str, 
        strategy: Dict[str, Any]
    ) -> UnifiedAgentRequest:
        """构建Agent请求"""
        # 基础请求
        request = UnifiedAgentRequest(
            query=message,
            project_id=session.project_id,
            mode=strategy["mode"],
            context=session.context.copy()
        )
        
        # 如果是跟进，调整提示词
        if strategy["follow_up"] and session.turns:
            last_turn = session.turns[-1]
            if last_turn.agent_response.suggestions:
                enhanced_query = f"{message}\n\n基于之前的建议：{'; '.join(last_turn.agent_response.suggestions[:2])}"
                request.query = enhanced_query
        
        return request
    
    async def _enhance_response(
        self, 
        session: ConversationSession, 
        response: UnifiedAgentResponse, 
        strategy: Dict[str, Any]
    ) -> UnifiedAgentResponse:
        """增强响应"""
        # 添加对话相关的下一步建议
        conversation_actions = self._generate_conversation_actions(session, response, strategy)
        response.next_actions.extend(conversation_actions)
        
        # 如果生成了内容，累积到会话中
        if response.generated_content:
            if session.accumulated_content:
                session.accumulated_content += "\n\n" + response.generated_content
            else:
                session.accumulated_content = response.generated_content
        
        return response
    
    def _generate_conversation_actions(
        self, 
        session: ConversationSession, 
        response: UnifiedAgentResponse, 
        strategy: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """生成对话相关的行动建议"""
        actions = []
        
        # 基于响应类型添加建议
        if response.analysis_response and not response.generated_content:
            actions.append({
                "action": "generate_based_on_analysis",
                "title": "基于分析生成内容",
                "description": "根据分析结果创作相关内容"
            })
        
        if response.generated_content:
            actions.extend([
                {"action": "continue_story", "title": "继续故事", "description": "在当前内容基础上继续创作"},
                {"action": "refine_content", "title": "优化内容", "description": "改进和完善生成的内容"},
                {"action": "analyze_generated", "title": "分析内容", "description": "分析生成内容的质量和特点"}
            ])
        
        # 基于对话流程添加建议
        flow_type = strategy.get("flow_type")
        if flow_type and flow_type in self.conversation_flows:
            flow = self.conversation_flows[flow_type]
            current_step = len(session.turns) % len(flow["steps"])
            if current_step < len(flow["steps"]) - 1:
                next_step = flow["steps"][current_step + 1]
                actions.append({
                    "action": f"flow_{next_step['action']}",
                    "title": f"下一步：{next_step['action']}",
                    "description": next_step["prompt"]
                })
        
        return actions
    
    def _update_session_state(self, session: ConversationSession, response: UnifiedAgentResponse):
        """更新会话状态"""
        if response.analysis_response and not response.generated_content:
            session.state = ConversationState.ANALYZING
        elif response.suggestions and not response.generated_content:
            session.state = ConversationState.SUGGESTING
        elif response.generated_content:
            session.state = ConversationState.GENERATING
        else:
            session.state = ConversationState.COMPLETED
    
    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """获取会话"""
        return self.active_sessions.get(session_id)
    
    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """获取会话摘要"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {"error": "会话不存在"}
        
        return {
            "session_id": session_id,
            "state": session.state.value,
            "total_turns": session.total_turns,
            "accumulated_content_length": len(session.accumulated_content),
            "last_activity": session.last_activity.isoformat(),
            "key_entities": session.context.get("entities", {}),
            "recent_intents": [
                h["intent"] for h in session.context.get("intent_history", [])[-3:]
            ]
        }
    
    def end_session(self, session_id: str) -> Dict[str, Any]:
        """结束会话"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {"error": "会话不存在"}
        
        session.state = ConversationState.COMPLETED
        
        summary = {
            "session_id": session_id,
            "total_turns": session.total_turns,
            "accumulated_content": session.accumulated_content,
            "final_state": session.state.value,
            "duration": (session.last_activity - session.created_at).total_seconds()
        }
        
        # 可选：清理会话
        # del self.active_sessions[session_id]
        
        return summary
    
    def get_conversation_suggestions(self, session_id: str) -> List[str]:
        """获取对话建议"""
        session = self.active_sessions.get(session_id)
        if not session:
            return []
        
        suggestions = []
        
        # 基于会话状态提供建议
        if session.state == ConversationState.ANALYZING:
            suggestions.extend([
                "请基于分析结果生成相关内容",
                "我想了解更多细节",
                "给我一些创作建议"
            ])
        elif session.state == ConversationState.GENERATING:
            suggestions.extend([
                "继续这个故事",
                "优化刚才的内容",
                "分析一下生成的内容"
            ])
        else:
            suggestions.extend([
                "分析角色特点",
                "帮我写一段内容",
                "给我一些创作灵感"
            ])
        
        return suggestions


# 全局实例
intelligent_conversation_service = IntelligentConversationService()


def get_intelligent_conversation_service() -> IntelligentConversationService:
    """获取智能对话流程服务实例"""
    return intelligent_conversation_service
