export as namespace predicates;

export function orient2d(
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number): number;

export function orient2dfast(
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number): number;

export function incircle(
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number,
    dx: number, dy: number): number;

export function incirclefast(
    ax: number, ay: number,
    bx: number, by: number,
    cx: number, cy: number,
    dx: number, dy: number): number;

export function orient3d(
    ax: number, ay: number, az: number,
    bx: number, by: number, bz: number,
    cx: number, cy: number, cz: number,
    dx: number, dy: number, dz: number): number;

export function orient3dfast(
    ax: number, ay: number, az: number,
    bx: number, by: number, bz: number,
    cx: number, cy: number, cz: number,
    dx: number, dy: number, dz: number): number;

export function insphere(
    ax: number, ay: number, az: number,
    bx: number, by: number, bz: number,
    cx: number, cy: number, cz: number,
    dx: number, dy: number, dz: number,
    ex: number, ey: number, ez: number): number;

export function inspherefast(
    ax: number, ay: number, az: number,
    bx: number, by: number, bz: number,
    cx: number, cy: number, cz: number,
    dx: number, dy: number, dz: number,
    ex: number, ey: number, ez: number): number;
