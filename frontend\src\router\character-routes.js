/**
 * 角色管理相关路由配置
 */

import CharacterManagement from '@/components/CharacterManagement.vue'

export const characterRoutes = [
  {
    path: '/projects/:projectId/characters',
    name: 'CharacterManagement',
    component: CharacterManagement,
    props: true,
    meta: {
      title: '角色管理',
      requiresAuth: true,
      breadcrumb: [
        { text: '项目', to: '/projects' },
        { text: '角色管理', to: '' }
      ]
    }
  },
  {
    path: '/projects/:projectId/characters/:characterName',
    name: 'CharacterDetail',
    component: CharacterManagement,
    props: route => ({
      projectId: route.params.projectId,
      selectedCharacterName: route.params.characterName
    }),
    meta: {
      title: '角色详情',
      requiresAuth: true,
      breadcrumb: [
        { text: '项目', to: '/projects' },
        { text: '角色管理', to: `/projects/${route.params.projectId}/characters` },
        { text: '角色详情', to: '' }
      ]
    }
  }
]

export default characterRoutes
