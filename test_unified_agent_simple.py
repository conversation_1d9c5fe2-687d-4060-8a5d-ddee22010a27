#!/usr/bin/env python3
"""
统一小说创作Agent简化测试
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.intelligent_intent_recognizer import get_intelligent_intent_recognizer


async def test_intent_recognition_only():
    """仅测试意图识别功能"""
    print("🧪 测试智能意图识别")
    print("=" * 50)
    
    try:
        intent_recognizer = get_intelligent_intent_recognizer()
        
        test_queries = [
            "林深这个角色是什么性格？",
            "帮我写下一章的内容",
            "分析一下当前的剧情发展",
            "怎么写好对话场景？",
            "生成一段紧张的追逐场面"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n测试 {i}: {query}")
            
            result = await intent_recognizer.recognize_intent(query)
            
            print(f"  意图: {result.primary_intent}")
            print(f"  类别: {result.intent_category.value}")
            print(f"  置信度: {result.confidence:.2f}")
            print(f"  建议模式: {result.suggested_mode}")
            
            if result.keywords:
                print(f"  关键词: {', '.join(result.keywords)}")
            
            if result.entities:
                print(f"  实体: {result.entities}")
        
        return True
        
    except Exception as e:
        print(f"❌ 意图识别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_agent_capabilities():
    """测试Agent能力"""
    print("\n🧪 测试Agent能力")
    print("=" * 50)
    
    try:
        from app.services.unified_novel_agent_service import get_unified_novel_agent_service
        
        agent_service = get_unified_novel_agent_service()
        
        capabilities = agent_service.get_capabilities()
        
        print("支持的模式:")
        for mode in capabilities["modes"]:
            print(f"  - {mode['name']} ({mode['value']}): {mode['description']}")
        
        print(f"\n核心能力:")
        for capability in capabilities["capabilities"]:
            print(f"  - {capability}")
        
        print(f"\n支持的意图类型: {len(capabilities['supported_intents'])}种")
        for intent in capabilities["supported_intents"][:5]:  # 显示前5种
            print(f"  - {intent}")
        
        return True
        
    except Exception as e:
        print(f"❌ 能力测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_structure():
    """测试API结构"""
    print("\n🧪 测试API结构")
    print("=" * 50)
    
    try:
        from app.routers.unified_novel_agent import router
        
        # 获取路由信息
        routes = []
        for route in router.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append({
                    'path': route.path,
                    'methods': list(route.methods),
                    'name': getattr(route, 'name', 'unknown')
                })
        
        print("API路由:")
        for route in routes:
            methods = ', '.join(route['methods'])
            print(f"  {methods} {route['path']} ({route['name']})")
        
        return True
        
    except Exception as e:
        print(f"❌ API结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_models():
    """测试数据模型"""
    print("\n🧪 测试数据模型")
    print("=" * 50)
    
    try:
        from app.services.unified_novel_agent_service import (
            UnifiedAgentRequest, UnifiedAgentResponse, AgentMode, IntentType
        )
        
        # 测试请求模型
        request = UnifiedAgentRequest(
            query="测试查询",
            project_id="test-project",
            mode=AgentMode.AUTO
        )
        
        print("✅ UnifiedAgentRequest 创建成功")
        print(f"  查询: {request.query}")
        print(f"  模式: {request.mode.value}")
        
        # 测试响应模型
        response = UnifiedAgentResponse(
            success=True,
            mode_used=AgentMode.ANALYSIS,
            intent_detected=IntentType.CHARACTER_QUERY
        )
        
        print("✅ UnifiedAgentResponse 创建成功")
        print(f"  成功: {response.success}")
        print(f"  使用模式: {response.mode_used.value}")
        print(f"  检测意图: {response.intent_detected.value}")
        
        # 测试序列化
        request_dict = request.to_dict()
        response_dict = response.to_dict()
        
        print("✅ 模型序列化成功")
        print(f"  请求字段: {len(request_dict)}个")
        print(f"  响应字段: {len(response_dict)}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_conversation_service():
    """测试对话服务"""
    print("\n🧪 测试对话服务")
    print("=" * 50)
    
    try:
        from app.services.intelligent_conversation_service import get_intelligent_conversation_service
        
        conversation_service = get_intelligent_conversation_service()
        
        # 测试对话流程模板
        flows = conversation_service.conversation_flows
        print(f"对话流程模板: {len(flows)}种")
        for flow_name, flow_data in flows.items():
            print(f"  - {flow_name}: {len(flow_data['steps'])}步骤")
        
        # 测试建议生成
        suggestions = conversation_service.get_conversation_suggestions("test-session")
        print(f"对话建议: {len(suggestions)}条")
        for suggestion in suggestions[:3]:
            print(f"  - {suggestion}")
        
        return True
        
    except Exception as e:
        print(f"❌ 对话服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_simple_tests():
    """运行简化测试"""
    print("🚀 统一小说创作Agent简化测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试意图识别
    result1 = await test_intent_recognition_only()
    test_results.append(("意图识别", result1))
    
    # 测试Agent能力
    result2 = test_agent_capabilities()
    test_results.append(("Agent能力", result2))
    
    # 测试API结构
    result3 = test_api_structure()
    test_results.append(("API结构", result3))
    
    # 测试数据模型
    result4 = test_data_models()
    test_results.append(("数据模型", result4))
    
    # 测试对话服务
    result5 = test_conversation_service()
    test_results.append(("对话服务", result5))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有核心功能测试通过！")
        print("\n📝 统一AI创作助手特性:")
        print("✅ 智能意图识别系统")
        print("✅ 统一Agent接口设计")
        print("✅ 完整的API路由结构")
        print("✅ 标准化数据模型")
        print("✅ 智能对话流程管理")
        
        print("\n🔧 主要API端点:")
        print("POST /api/v1/projects/{project_id}/ai-assistant/chat")
        print("POST /api/v1/projects/{project_id}/ai-assistant/analyze-intent")
        print("GET /api/v1/projects/{project_id}/ai-assistant/capabilities")
        print("GET /api/v1/projects/{project_id}/ai-assistant/conversation-starters")
        
        print("\n🎯 集成完成，可以开始使用统一AI创作助手！")
    else:
        print("⚠️  部分核心功能测试失败，但基础架构已就绪。")
    
    return passed == total


if __name__ == "__main__":
    print("🔧 统一AI创作助手核心功能测试")
    print("=" * 60)
    
    # 运行异步测试
    success = asyncio.run(run_simple_tests())
    
    if success:
        print("\n✅ 核心功能测试完成，统一AI创作助手架构正常！")
        print("\n🎯 下一步:")
        print("1. 启动FastAPI服务器")
        print("2. 通过前端界面测试完整功能")
        print("3. 根据实际使用情况优化性能")
    else:
        print("\n❌ 部分测试失败，请检查相关组件。")
    
    sys.exit(0 if success else 1)
