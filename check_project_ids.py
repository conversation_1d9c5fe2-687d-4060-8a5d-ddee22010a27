#!/usr/bin/env python3
"""
检查项目ID问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.storage.memory_storage import storage

def check_project_ids():
    """检查项目ID"""
    print("🔍 检查项目ID问题")
    print("=" * 50)
    
    target_project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    try:
        # 1. 查看所有角色的项目ID
        print("1. 所有角色的项目ID:")
        all_characters = storage.get_all('characters')
        
        for i, char in enumerate(all_characters, 1):
            char_name = char.get('name', 'Unknown')
            char_project_id = char.get('project_id', 'N/A')
            print(f"   角色 {i}: {char_name}")
            print(f"     项目ID: '{char_project_id}'")
            print(f"     目标ID: '{target_project_id}'")
            print(f"     匹配: {char_project_id == target_project_id}")
            print(f"     ID长度: {len(char_project_id)} vs {len(target_project_id)}")
            print()
        
        # 2. 查看所有项目
        print("2. 所有项目:")
        all_projects = storage.get_all('projects')
        
        for i, project in enumerate(all_projects, 1):
            project_id = project.get('id', 'N/A')
            project_name = project.get('name', 'Unknown')
            print(f"   项目 {i}: {project_name}")
            print(f"     ID: '{project_id}'")
            print(f"     匹配: {project_id == target_project_id}")
            print()
        
        # 3. 查看章节的项目ID
        print("3. 章节的项目ID:")
        all_chapters = storage.get_all('chapters')
        
        project_chapters = [c for c in all_chapters if c.get('project_id') == target_project_id]
        print(f"   目标项目的章节数: {len(project_chapters)}")
        
        if project_chapters:
            for i, chapter in enumerate(project_chapters[:3], 1):  # 只显示前3个
                chapter_id = chapter.get('id', 'N/A')
                chapter_title = chapter.get('title', 'Unknown')
                print(f"     章节 {i}: {chapter_title} (ID: {chapter_id})")
        
        # 4. 查看向量化数据
        print("\n4. 向量化数据:")
        vectorization_files = []
        
        # 检查项目目录
        project_dir = f"data/projects/{target_project_id}"
        if os.path.exists(project_dir):
            files = os.listdir(project_dir)
            vectorization_files = [f for f in files if f.startswith('vectorization_')]
            print(f"   项目目录存在: {project_dir}")
            print(f"   向量化文件: {vectorization_files}")
        else:
            print(f"   项目目录不存在: {project_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_project_id():
    """修复项目ID问题"""
    print("\n🔧 修复项目ID问题")
    print("=" * 50)
    
    target_project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    try:
        # 获取所有角色
        all_characters = storage.get_all('characters')
        
        for char in all_characters:
            char_id = char.get('id')
            char_name = char.get('name', 'Unknown')
            current_project_id = char.get('project_id', '')
            
            if current_project_id != target_project_id:
                print(f"修复角色 '{char_name}' 的项目ID:")
                print(f"  当前: '{current_project_id}'")
                print(f"  目标: '{target_project_id}'")
                
                # 更新项目ID
                update_data = {'project_id': target_project_id}
                updated = storage.update('characters', char_id, update_data)
                
                if updated:
                    print(f"  ✅ 修复成功")
                else:
                    print(f"  ❌ 修复失败")
        
        # 验证修复结果
        print("\n验证修复结果:")
        from app.services.character_storage_service import character_storage_service
        
        characters = character_storage_service.get_project_characters(target_project_id)
        print(f"修复后项目角色数: {len(characters)}")
        
        if characters:
            for char in characters:
                print(f"  - {char.get('name', 'Unknown')}")
        
        return len(characters) > 0
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 项目ID问题诊断和修复")
    print("=" * 60)
    
    # 检查问题
    check_project_ids()
    
    # 修复问题
    success = fix_project_id()
    
    if success:
        print("\n✅ 项目ID问题已修复！")
        print("现在可以正常访问角色详情了。")
    else:
        print("\n❌ 项目ID修复失败，需要进一步调试。")
