import axios from 'axios'

// 模型API
const modelApi = axios.create({
  baseURL: 'http://localhost:8000/api/v1',
  timeout: 60000  // 增加超时时间，因为AI生成可能需要更长时间
})

export const modelsApi = {
  // 获取所有AI模型
  getAllModels: () => modelApi.get('/ai-models'),

  // 获取Ollama模型
  getOllamaModels: () => modelApi.get('/ai-models/ollama'),

  // 获取Hugging Face模型
  getHuggingFaceModels: () => modelApi.get('/ai-models/huggingface'),

  // 获取模型详情
  getModel: (modelId) => modelApi.get(`/ai-models/${modelId}`),

  // 测试模型连接
  testModel: (modelId, testData = { prompt: "Hello, this is a test." }) =>
    modelApi.post(`/ai-models/${modelId}/test`, testData),

  // 刷新模型列表
  refreshModels: () => modelApi.post('/ai-models/refresh'),

  // AI内容生成
  generateContent: (generationData) => modelApi.post('/ai-models/generate', generationData),

  // 为项目生成AI内容
  generateProjectContent: (projectId, generationData) =>
    modelApi.post(`/ai-models/projects/${projectId}/generate`, generationData),

  // 获取AI模型统计
  getModelStats: () => modelApi.get('/ai-models/stats'),

  // 远程服务器相关API
  getRemoteServers: () => modelApi.get('/ai-models/remote-servers'),
  getRemoteModels: () => modelApi.get('/ai-models/remote-models'),
  testRemoteServer: (serverConfig) => modelApi.post('/ai-models/test-remote-server', serverConfig)
}
