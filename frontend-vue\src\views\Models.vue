<template>
  <div class="models">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <h1 class="h3 mb-0">
        <i class="bi bi-cpu me-2"></i>
        AI模型管理
      </h1>
      <div class="d-flex gap-2">
        <button
          class="btn btn-outline-primary"
          @click="refreshModels"
          :disabled="loading"
        >
          <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
          <i v-else class="bi bi-arrow-clockwise me-2"></i>
          {{ loading ? '刷新中...' : '刷新模型' }}
        </button>
        <button
          class="btn btn-outline-success"
          @click="showAddModelModal = true"
        >
          <i class="bi bi-plus-circle me-2"></i>
          添加模型
        </button>
      </div>
    </div>

    <!-- 模型类型标签 -->
    <ul class="nav nav-tabs mb-4">
      <li class="nav-item">
        <button
          class="nav-link"
          :class="{ active: activeTab === 'ollama' }"
          @click="activeTab = 'ollama'"
        >
          <i class="bi bi-hdd me-2"></i>
          Ollama模型
          <span class="badge bg-primary ms-2">{{ ollamaModels.length }}</span>
        </button>
      </li>
      <li class="nav-item">
        <button
          class="nav-link"
          :class="{ active: activeTab === 'huggingface' }"
          @click="activeTab = 'huggingface'"
        >
          <i class="bi bi-cloud me-2"></i>
          Hugging Face
          <span class="badge bg-success ms-2">{{ huggingFaceModels.length }}</span>
        </button>
      </li>
      <li class="nav-item">
        <button
          class="nav-link"
          :class="{ active: activeTab === 'remote' }"
          @click="activeTab = 'remote'"
        >
          <i class="bi bi-cloud-arrow-up me-2"></i>
          远程服务器
          <span class="badge bg-info ms-2">{{ remoteModels.length }}</span>
        </button>
      </li>
      <li class="nav-item">
        <button
          class="nav-link"
          :class="{ active: activeTab === 'custom' }"
          @click="activeTab = 'custom'"
        >
          <i class="bi bi-gear me-2"></i>
          自定义模型
          <span class="badge bg-warning ms-2">{{ customModels.length }}</span>
        </button>
      </li>
    </ul>

    <!-- 模型列表 -->
    <div class="tab-content">
      <!-- Ollama模型 -->
      <div v-if="activeTab === 'ollama'" class="tab-pane active">
        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-3 text-muted">加载Ollama模型...</p>
        </div>

        <div v-else-if="ollamaModels.length === 0" class="text-center py-5">
          <i class="bi bi-hdd display-1 text-muted mb-3"></i>
          <h4 class="text-muted">暂无Ollama模型</h4>
          <p class="text-muted">请确保Ollama服务正在运行，并已安装模型</p>
          <button class="btn btn-primary" @click="refreshModels">
            <i class="bi bi-arrow-clockwise me-2"></i>
            重新检测
          </button>
        </div>

        <div v-else class="row">
          <div
            v-for="model in ollamaModels"
            :key="model.name"
            class="col-lg-4 col-md-6 mb-4"
          >
            <ModelCard
              :model="model"
              type="ollama"
              @test="testModel"
              @configure="configureModel"
              @remove="removeModel"
            />
          </div>
        </div>
      </div>

      <!-- Hugging Face模型 -->
      <div v-if="activeTab === 'huggingface'" class="tab-pane active">
        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-3 text-muted">加载Hugging Face模型...</p>
        </div>

        <div v-else class="row">
          <div
            v-for="model in huggingFaceModels"
            :key="model.name"
            class="col-lg-4 col-md-6 mb-4"
          >
            <ModelCard
              :model="model"
              type="huggingface"
              @test="testModel"
              @configure="configureModel"
              @remove="removeModel"
            />
          </div>
        </div>
      </div>

      <!-- 远程服务器模型 -->
      <div v-if="activeTab === 'remote'" class="tab-pane active">
        <!-- 服务器状态卡片 -->
        <div class="row mb-4">
          <div v-for="server in remoteServers" :key="server.name" class="col-lg-6 mb-3">
            <div class="card border-info">
              <div class="card-header bg-info text-white">
                <div class="d-flex justify-content-between align-items-center">
                  <h6 class="mb-0">
                    <i class="bi bi-server me-2"></i>
                    {{ server.name }}
                  </h6>
                  <div class="d-flex gap-2">
                    <button
                      @click="testServer(server)"
                      class="btn btn-sm btn-light"
                      :disabled="testingServer === server.name"
                    >
                      <span v-if="testingServer === server.name" class="spinner-border spinner-border-sm me-1"></span>
                      <i v-else class="bi bi-wifi me-1"></i>
                      测试连接
                    </button>
                    <span class="badge" :class="getServerStatusBadge(server)">
                      {{ getServerStatusText(server) }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <p class="card-text small text-muted mb-2">{{ server.description }}</p>
                <div class="row text-center">
                  <div class="col-4">
                    <div class="small text-muted">地址</div>
                    <div class="fw-bold">{{ server.host }}:{{ server.port }}</div>
                  </div>
                  <div class="col-4">
                    <div class="small text-muted">状态</div>
                    <div class="fw-bold" :class="getServerStatusColor(server)">
                      {{ server.enabled ? '启用' : '禁用' }}
                    </div>
                  </div>
                  <div class="col-4">
                    <div class="small text-muted">模型数</div>
                    <div class="fw-bold">{{ getServerModelCount(server) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 远程模型列表 -->
        <div v-if="loading" class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
          <p class="mt-3 text-muted">加载远程服务器模型...</p>
        </div>

        <div v-else-if="remoteModels.length === 0" class="text-center py-5">
          <i class="bi bi-cloud-slash display-1 text-muted mb-3"></i>
          <h4 class="text-muted">暂无远程模型</h4>
          <p class="text-muted">请检查远程服务器连接状态</p>
          <button class="btn btn-primary" @click="refreshModels">
            <i class="bi bi-arrow-clockwise me-2"></i>
            重新检测
          </button>
        </div>

        <div v-else class="row">
          <div
            v-for="model in remoteModels"
            :key="model.id"
            class="col-lg-4 col-md-6 mb-4"
          >
            <ModelCard
              :model="model"
              type="remote"
              @test="testModel"
              @configure="configureModel"
              @remove="removeModel"
              @use-for-project="useModelForProject"
            />
          </div>
        </div>
      </div>

      <!-- 自定义模型 -->
      <div v-if="activeTab === 'custom'" class="tab-pane active">
        <div v-if="customModels.length === 0" class="text-center py-5">
          <i class="bi bi-gear display-1 text-muted mb-3"></i>
          <h4 class="text-muted">暂无自定义模型</h4>
          <p class="text-muted">点击上方按钮添加自定义AI模型配置</p>
        </div>

        <div v-else class="row">
          <div
            v-for="model in customModels"
            :key="model.id"
            class="col-lg-4 col-md-6 mb-4"
          >
            <ModelCard
              :model="model"
              type="custom"
              @test="testModel"
              @configure="configureModel"
              @remove="removeModel"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 模型统计 -->
    <div class="row mt-5">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h5 class="card-title mb-0">
              <i class="bi bi-bar-chart me-2"></i>
              模型统计
            </h5>
          </div>
          <div class="card-body">
            <div class="row text-center">
              <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                  <div class="stat-value">{{ totalModels }}</div>
                  <div class="stat-label">总模型数</div>
                </div>
              </div>
              <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                  <div class="stat-value">{{ availableModels }}</div>
                  <div class="stat-label">可用模型</div>
                </div>
              </div>
              <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                  <div class="stat-value">{{ activeModels }}</div>
                  <div class="stat-label">活跃模型</div>
                </div>
              </div>
              <div class="col-md-3 col-6 mb-3">
                <div class="stat-item">
                  <div class="stat-value">{{ formatSize(totalSize) }}</div>
                  <div class="stat-label">总大小</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加模型模态框 -->
    <AddModelModal
      v-model:show="showAddModelModal"
      @added="handleModelAdded"
    />

    <!-- 模型配置模态框 -->
    <ModelConfigModal
      v-model:show="showConfigModal"
      :model="selectedModel"
      @updated="handleModelUpdated"
    />

    <!-- 测试结果模态框 -->
    <ModelTestModal
      v-model:show="showTestModal"
      :test-result="testResult"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import { modelsApi } from '@/api/models'
import ModelCard from '@/components/models/ModelCard.vue'
import AddModelModal from '@/components/modals/AddModelModal.vue'
import ModelConfigModal from '@/components/modals/ModelConfigModal.vue'
import ModelTestModal from '@/components/modals/ModelTestModal.vue'

export default {
  name: 'Models',
  components: {
    ModelCard,
    AddModelModal,
    ModelConfigModal,
    ModelTestModal
  },
  setup() {
    const appStore = useAppStore()

    const loading = ref(false)
    const activeTab = ref('ollama')

    const ollamaModels = ref([])
    const huggingFaceModels = ref([])
    const remoteModels = ref([])
    const customModels = ref([])
    const remoteServers = ref([])
    const testingServer = ref(null)

    const selectedModel = ref(null)
    const testResult = ref(null)
    const showAddModelModal = ref(false)
    const showConfigModal = ref(false)
    const showTestModal = ref(false)

    const totalModels = computed(() => {
      return ollamaModels.value.length + huggingFaceModels.value.length + remoteModels.value.length + customModels.value.length
    })

    const availableModels = computed(() => {
      return [...ollamaModels.value, ...huggingFaceModels.value, ...remoteModels.value, ...customModels.value]
        .filter(model => model.status === 'available').length
    })

    const activeModels = computed(() => {
      return [...ollamaModels.value, ...huggingFaceModels.value, ...remoteModels.value, ...customModels.value]
        .filter(model => model.active).length
    })

    const totalSize = computed(() => {
      return [...ollamaModels.value, ...huggingFaceModels.value, ...remoteModels.value, ...customModels.value]
        .reduce((total, model) => total + (model.size || 0), 0)
    })

    const formatSize = (bytes) => {
      if (!bytes) return '0 B'
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
    }

    const loadOllamaModels = async () => {
      try {
        const response = await modelsApi.getOllamaModels()
        ollamaModels.value = response.data || []
      } catch (error) {
        console.error('加载Ollama模型失败:', error)
        ollamaModels.value = []
      }
    }

    const loadHuggingFaceModels = async () => {
      try {
        const response = await modelsApi.getHuggingFaceModels()
        huggingFaceModels.value = response.data || []
      } catch (error) {
        console.error('加载Hugging Face模型失败:', error)
        huggingFaceModels.value = []
      }
    }

    const loadRemoteServers = async () => {
      try {
        const response = await modelsApi.getRemoteServers()
        remoteServers.value = response.data || []
      } catch (error) {
        console.error('加载远程服务器失败:', error)
        remoteServers.value = []
      }
    }

    const loadRemoteModels = async () => {
      try {
        const response = await modelsApi.getRemoteModels()
        remoteModels.value = response.data || []
      } catch (error) {
        console.error('加载远程模型失败:', error)
        remoteModels.value = []
      }
    }

    const loadCustomModels = async () => {
      // TODO: 实现自定义模型加载
      customModels.value = []
    }

    const refreshModels = async () => {
      loading.value = true
      try {
        await Promise.all([
          loadOllamaModels(),
          loadHuggingFaceModels(),
          loadRemoteServers(),
          loadRemoteModels(),
          loadCustomModels()
        ])
        appStore.showSuccess('模型列表刷新成功')
      } catch (error) {
        console.error('刷新模型失败:', error)
        appStore.showError('刷新模型失败')
      } finally {
        loading.value = false
      }
    }

    const testModel = async (model, type) => {
      try {
        appStore.setLoading(true)
        const response = await modelsApi.testModel(type, model.name)
        testResult.value = {
          model: model,
          type: type,
          result: response.data
        }
        showTestModal.value = true
        appStore.showSuccess(`模型 ${model.name} 测试完成`)
      } catch (error) {
        console.error('测试模型失败:', error)
        appStore.showError(`模型 ${model.name} 测试失败`)
      } finally {
        appStore.setLoading(false)
      }
    }

    const configureModel = (model) => {
      selectedModel.value = model
      showConfigModal.value = true
    }

    const removeModel = async (model, type) => {
      if (!confirm(`确定要移除模型 "${model.name}" 吗？`)) {
        return
      }

      try {
        // TODO: 实现模型移除功能
        appStore.showSuccess(`模型 ${model.name} 已移除`)
        refreshModels()
      } catch (error) {
        console.error('移除模型失败:', error)
        appStore.showError(`移除模型 ${model.name} 失败`)
      }
    }

    const handleModelAdded = (model) => {
      showAddModelModal.value = false
      appStore.showSuccess(`模型 ${model.name} 添加成功`)
      refreshModels()
    }

    const handleModelUpdated = (model) => {
      showConfigModal.value = false
      appStore.showSuccess(`模型 ${model.name} 配置更新成功`)
      refreshModels()
    }

    // 服务器相关方法
    const testServer = async (server) => {
      testingServer.value = server.name
      try {
        const response = await modelsApi.testRemoteServer({
          base_url: server.base_url,
          name: server.name
        })

        if (response.data.success) {
          appStore.showSuccess(`服务器 ${server.name} 连接成功！模型数: ${response.data.models_count}`)
          // 重新加载远程模型
          await loadRemoteModels()
        } else {
          appStore.showError(`服务器 ${server.name} 连接失败: ${response.data.message}`)
        }
      } catch (error) {
        console.error('测试服务器失败:', error)
        appStore.showError(`服务器 ${server.name} 连接失败`)
      } finally {
        testingServer.value = null
      }
    }

    const getServerStatusBadge = (server) => {
      return server.enabled ? 'bg-success' : 'bg-secondary'
    }

    const getServerStatusText = (server) => {
      return server.enabled ? '在线' : '离线'
    }

    const getServerStatusColor = (server) => {
      return server.enabled ? 'text-success' : 'text-muted'
    }

    const getServerModelCount = (server) => {
      return remoteModels.value.filter(model =>
        model.name.includes(`[${server.name}]`)
      ).length
    }

    const useModelForProject = async (model) => {
      try {
        // TODO: 实现为项目选择模型的功能
        appStore.showSuccess(`已选择模型 ${model.name} 用于项目`)
      } catch (error) {
        console.error('选择模型失败:', error)
        appStore.showError('选择模型失败')
      }
    }

    onMounted(() => {
      refreshModels()
    })

    return {
      loading,
      activeTab,
      ollamaModels,
      huggingFaceModels,
      remoteModels,
      customModels,
      remoteServers,
      testingServer,
      selectedModel,
      testResult,
      showAddModelModal,
      showConfigModal,
      showTestModal,
      totalModels,
      availableModels,
      activeModels,
      totalSize,
      formatSize,
      refreshModels,
      testModel,
      configureModel,
      removeModel,
      handleModelAdded,
      handleModelUpdated,
      testServer,
      getServerStatusBadge,
      getServerStatusText,
      getServerStatusColor,
      getServerModelCount,
      useModelForProject
    }
  }
}
</script>

<style lang="scss" scoped>
.models {
  padding: 1.5rem;
}

.nav-tabs {
  border-bottom: 2px solid #dee2e6;

  .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;

    &:hover {
      border-color: transparent;
      color: #495057;
    }

    &.active {
      color: #495057;
      background-color: transparent;
      border-bottom: 2px solid #667eea;
    }
  }
}

.tab-pane {
  min-height: 400px;
}

.stat-item {
  .stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: #495057;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
}

.card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .models {
    padding: 1rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }

  .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}
</style>
