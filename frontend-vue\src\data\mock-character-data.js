/**
 * 模拟角色数据 - 用于演示高级角色管理功能
 */

export const mockCharacterList = [
  {
    id: "char_001",
    name: "李明",
    description: "25岁的程序员，因为一次意外穿越到了魔法世界。性格内向但坚韧，对新事物充满好奇心。在魔法世界中展现出惊人的符文理解能力，成为了墨老的弟子。",
    personality_tags: ["内向", "坚韧", "好奇", "聪明", "谨慎"],
    appearance: "中等身材，戴眼镜，看起来文质彬彬。穿越后逐渐适应了魔法世界的服装。",
    first_appearance_chapter: "chapter-1",
    total_chapters: 5,
    relationship_count: 3,
    event_count: 12,
    importance_score: 9.2
  },
  {
    id: "char_002", 
    name: "墨老",
    description: "神秘的魔法导师，拥有深厚的魔法造诣。表面慈祥，但隐藏着复杂的过去。曾经历过一场重大的背叛，导致他对信任变得谨慎。",
    personality_tags: ["智慧", "慈祥", "神秘", "谨慎", "强大"],
    appearance: "白发苍苍的老者，身穿深蓝色法师袍，手持古老的法杖。眼神深邃，仿佛能看透人心。",
    first_appearance_chapter: "chapter-1",
    total_chapters: 4,
    relationship_count: 4,
    event_count: 8,
    importance_score: 8.5
  },
  {
    id: "char_003",
    name: "暗影",
    description: "神秘的黑衣刺客，墨老的宿敌。因为师父被墨老杀死而心怀仇恨，一直在寻找报仇的机会。实力强大，行踪诡秘。",
    personality_tags: ["冷酷", "仇恨", "坚定", "孤独", "痛苦"],
    appearance: "全身黑衣，面戴面具，只露出一双充满仇恨的眼睛。身材修长，动作敏捷如影。",
    first_appearance_chapter: "chapter-2",
    total_chapters: 2,
    relationship_count: 2,
    event_count: 5,
    importance_score: 7.8
  },
  {
    id: "char_004",
    name: "艾莉娅",
    description: "魔法学院的天才学生，出身贵族家庭。性格高傲但内心善良，对李明的快速进步既惊讶又好奇。",
    personality_tags: ["高傲", "天才", "善良", "好胜", "优雅"],
    appearance: "金发碧眼的美丽少女，总是穿着华丽的魔法袍。举止优雅，气质高贵。",
    first_appearance_chapter: "chapter-3",
    total_chapters: 3,
    relationship_count: 2,
    event_count: 6,
    importance_score: 6.2
  },
  {
    id: "char_005",
    name: "雷恩",
    description: "李明在魔法学院的室友，性格开朗活泼。虽然魔法天赋一般，但对朋友非常忠诚。",
    personality_tags: ["开朗", "忠诚", "活泼", "普通", "友善"],
    appearance: "棕发少年，脸上总是挂着笑容。身材结实，喜欢运动。",
    first_appearance_chapter: "chapter-3",
    total_chapters: 2,
    relationship_count: 1,
    event_count: 4,
    importance_score: 4.1
  },
  {
    id: "char_006",
    name: "院长",
    description: "魔法学院的院长，德高望重的大魔法师。对学生要求严格但关爱有加。",
    personality_tags: ["严格", "智慧", "公正", "权威", "关爱"],
    appearance: "威严的中年男子，穿着华丽的院长袍。眼神锐利，气场强大。",
    first_appearance_chapter: "chapter-3",
    total_chapters: 1,
    relationship_count: 1,
    event_count: 2,
    importance_score: 3.5
  }
]

export const mockCharacterDetail = {
  basic_info: {
    id: "char_001",
    name: "李明",
    description: "25岁的程序员，因为一次意外穿越到了魔法世界。性格内向但坚韧，对新事物充满好奇心。在魔法世界中展现出惊人的符文理解能力，成为了墨老的弟子。",
    appearance: "中等身材，戴眼镜，看起来文质彬彬。穿越后逐渐适应了魔法世界的服装。",
    current_version: 3,
    first_appearance: "chapter-1",
    latest_chapter: "chapter-5",
    created_at: "2024-01-01T00:00:00Z",
    last_updated: "2024-01-05T12:30:00Z"
  },
  personality: {
    traits: ["内向", "坚韧", "好奇", "聪明", "谨慎", "怀疑", "思辨"],
    trait_evolution: [
      {
        chapter_id: "chapter-2",
        description: "对师父的信任开始动摇，开始思考正义与邪恶",
        old_traits: ["内向", "好奇", "坚韧"],
        new_traits: ["内向", "好奇", "坚韧", "怀疑", "思辨"],
        timestamp: "2024-01-02T10:15:00Z"
      }
    ],
    personality_analysis: {
      total_changes: 5,
      personality_changes: 2,
      development_trend: "evolving",
      key_turning_points: ["chapter-2"]
    }
  },
  relationships: [
    {
      character: "墨老",
      bond_type: "mentor",
      bond_type_display: "师徒",
      strength: 0.7,
      intensity: "strong",
      description: "师徒关系，但弟子开始产生怀疑",
      origin_chapter: "chapter-1",
      current_chapter: "chapter-2",
      is_positive: true,
      is_negative: false,
      evolution_count: 1
    },
    {
      character: "暗影",
      bond_type: "complicated",
      bond_type_display: "复杂关系",
      strength: 0.4,
      intensity: "moderate",
      description: "复杂关系，李明对暗影的话产生思考",
      origin_chapter: "chapter-2",
      current_chapter: "chapter-2",
      is_positive: false,
      is_negative: false,
      evolution_count: 0
    },
    {
      character: "艾莉娅",
      bond_type: "friendship",
      bond_type_display: "友情",
      strength: 0.6,
      intensity: "moderate",
      description: "同学关系，互相欣赏对方的才能",
      origin_chapter: "chapter-3",
      current_chapter: "chapter-4",
      is_positive: true,
      is_negative: false,
      evolution_count: 1
    }
  ],
  timeline: [
    {
      chapter_id: "chapter-1",
      event_type: "APPEARANCE",
      event_type_display: "登场",
      description: "穿越到魔法世界，遇到墨老",
      importance: "CRITICAL",
      involved_characters: ["墨老"],
      location: "魔法森林",
      emotional_impact: "震惊和困惑",
      timestamp: "2024-01-01T08:00:00Z"
    },
    {
      chapter_id: "chapter-1",
      event_type: "DECISION",
      event_type_display: "决定",
      description: "决定跟随墨老学习魔法",
      importance: "HIGH",
      involved_characters: ["墨老"],
      location: "魔法森林",
      emotional_impact: "决心和期待",
      timestamp: "2024-01-01T09:30:00Z"
    },
    {
      chapter_id: "chapter-2",
      event_type: "EMOTION",
      event_type_display: "情感",
      description: "对师父的信任开始动摇",
      importance: "HIGH",
      involved_characters: ["墨老", "暗影"],
      location: "训练场",
      emotional_impact: "困惑和怀疑",
      timestamp: "2024-01-02T10:15:00Z"
    },
    {
      chapter_id: "chapter-2",
      event_type: "DISCOVERY",
      event_type_display: "发现",
      description: "发现自己对符文有惊人的理解力",
      importance: "CRITICAL",
      involved_characters: ["墨老"],
      location: "训练场",
      emotional_impact: "惊喜和自信",
      timestamp: "2024-01-02T14:20:00Z"
    }
  ],
  development_analysis: {
    character_arc: "从普通程序员到魔法学徒的转变",
    growth_stages: [
      {
        stage: "初入异世界",
        chapters: ["chapter-1"],
        description: "震惊、适应、接受新现实"
      },
      {
        stage: "学习与怀疑",
        chapters: ["chapter-2"],
        description: "开始学习魔法，但对师父产生怀疑"
      },
      {
        stage: "天赋觉醒",
        chapters: ["chapter-3", "chapter-4"],
        description: "展现魔法天赋，建立新的人际关系"
      }
    ],
    key_relationships: ["墨老", "暗影", "艾莉娅"],
    personality_development: "从单纯信任到理性思考的转变"
  },
  statistics: {
    total_events: 12,
    total_relationships: 3,
    positive_relationships: 2,
    negative_relationships: 0,
    version_count: 3,
    change_count: 5,
    chapters_appeared: 5,
    importance_score: 9.2
  }
}

export const mockRelationshipNetwork = {
  nodes: [
    {
      id: "李明",
      name: "李明",
      description: "25岁的程序员，穿越到魔法世界...",
      personality_tags: ["内向", "坚韧", "好奇"],
      node_size: 75,
      node_color: "#FF6B6B"
    },
    {
      id: "墨老",
      name: "墨老",
      description: "神秘的魔法导师，拥有深厚的魔法造诣...",
      personality_tags: ["智慧", "慈祥", "神秘"],
      node_size: 70,
      node_color: "#FF6B6B"
    },
    {
      id: "暗影",
      name: "暗影",
      description: "神秘的黑衣刺客，墨老的宿敌...",
      personality_tags: ["冷酷", "仇恨", "坚定"],
      node_size: 65,
      node_color: "#4ECDC4"
    },
    {
      id: "艾莉娅",
      name: "艾莉娅",
      description: "魔法学院的天才学生，出身贵族家庭...",
      personality_tags: ["高傲", "天才", "善良"],
      node_size: 55,
      node_color: "#4ECDC4"
    }
  ],
  edges: [
    {
      source: "李明",
      target: "墨老",
      bond_type: "mentor",
      bond_type_display: "师徒",
      strength: 0.7,
      intensity: "strong",
      description: "师徒关系，但弟子开始产生怀疑",
      is_positive: true,
      is_negative: false,
      edge_color: "#4CAF50",
      edge_width: 3.5
    },
    {
      source: "墨老",
      target: "暗影",
      bond_type: "hatred",
      bond_type_display: "仇恨",
      strength: 0.9,
      intensity: "intense",
      description: "深仇大恨，源于过去的恩怨",
      is_positive: false,
      is_negative: true,
      edge_color: "#F44336",
      edge_width: 4.5
    },
    {
      source: "李明",
      target: "艾莉娅",
      bond_type: "friendship",
      bond_type_display: "友情",
      strength: 0.6,
      intensity: "moderate",
      description: "同学关系，互相欣赏对方的才能",
      is_positive: true,
      is_negative: false,
      edge_color: "#4CAF50",
      edge_width: 3.0
    }
  ],
  stats: {
    total_characters: 4,
    total_relationships: 3,
    positive_relationships: 2,
    negative_relationships: 1,
    average_strength: 0.73,
    network_density: 0.5
  }
}
