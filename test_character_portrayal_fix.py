#!/usr/bin/env python3
"""
测试人物刻画服务修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_character_portrayal_service():
    """测试人物刻画服务"""
    print("🧪 测试人物刻画服务修复")
    print("=" * 50)
    
    try:
        # 测试导入
        from app.services.character_portrayal_service import (
            get_character_portrayal_service, 
            PersonalityTrait, 
            PortrayalType
        )
        
        print("✅ 导入成功")
        
        # 测试枚举
        print(f"PersonalityTrait.NERVOUS: {PersonalityTrait.NERVOUS.value}")
        print(f"PersonalityTrait.CONFIDENT: {PersonalityTrait.CONFIDENT.value}")
        print(f"PersonalityTrait.SHY: {PersonalityTrait.SHY.value}")
        
        print("✅ 枚举测试成功")
        
        # 测试服务初始化
        service = get_character_portrayal_service()
        print("✅ 服务初始化成功")
        
        # 测试模板
        templates = service.templates
        print(f"✅ 模板数量: {len(templates)}")
        
        # 测试对话模式
        dialogue_patterns = service.dialogue_patterns
        print(f"✅ 对话模式数量: {len(dialogue_patterns)}")
        
        # 测试动作模式
        action_patterns = service.action_patterns
        print(f"✅ 动作模式数量: {len(action_patterns)}")
        
        # 测试情绪表达
        emotion_expressions = service.emotion_expressions
        print(f"✅ 情绪表达数量: {len(emotion_expressions)}")
        
        # 测试角色刻画生成
        character_info = {
            "name": "测试角色",
            "personality_tags": ["confident", "nervous"]
        }
        
        portrayal = service.generate_character_portrayal(
            character_info,
            PortrayalType.DIALOGUE,
            {"dialogue_content": "这是一个测试对话"}
        )
        
        print(f"✅ 角色刻画生成成功: {portrayal}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_character_portrayal_service()
    
    if success:
        print("\n🎉 人物刻画服务修复成功！")
    else:
        print("\n💥 修复失败，需要进一步调试。")
    
    sys.exit(0 if success else 1)
