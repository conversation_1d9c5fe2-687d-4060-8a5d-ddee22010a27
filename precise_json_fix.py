#!/usr/bin/env python3
"""
精确的JSON修复
"""

import json
import re

def precise_json_fix():
    """精确修复JSON"""
    print("🔧 精确JSON修复")
    print("=" * 50)
    
    # 问题JSON
    problematic_json = '''{ "characters": [ { "name": "林深", "identity": "记忆修复师", "description": "穿梭在新东京第三区的霓虹灯海中", "personality_tags": ["专业", "冷静"], "appearance": "金属义眼", "background": "专门处理被病毒侵蚀的记忆碎片", "current_status": "正在处理异常危险的案件", "goals": ["修复客户受损记忆"], "abilities": ["记忆修复技术"], "weaknesses": ["对病毒数据敏感" ]]}}'''
    
    print("原始JSON:")
    print(problematic_json)
    print()
    
    # 分析问题位置
    print("分析问题:")
    
    # 查找 "weaknesses": ["对病毒数据敏感" ]] 这个模式
    problem_pos = problematic_json.find('"weaknesses": ["对病毒数据敏感" ]]')
    if problem_pos != -1:
        print(f"找到问题位置: {problem_pos}")
        
        # 提取问题部分
        problem_part = '"weaknesses": ["对病毒数据敏感" ]]'
        print(f"问题部分: {problem_part}")
        
        # 正确的格式应该是
        correct_part = '"weaknesses": ["对病毒数据敏感"]'
        print(f"正确格式: {correct_part}")
        
        # 执行替换
        fixed_json = problematic_json.replace(problem_part, correct_part)
        print(f"\n修复后JSON:")
        print(fixed_json)
        
        # 测试解析
        try:
            data = json.loads(fixed_json)
            print("\n✅ 修复成功！")
            print(f"角色数量: {len(data.get('characters', []))}")
            
            for char in data.get('characters', []):
                print(f"  角色: {char.get('name', 'Unknown')}")
                print(f"  身份: {char.get('identity', 'Unknown')}")
                print(f"  弱点: {char.get('weaknesses', [])}")
                
            return fixed_json
            
        except Exception as e:
            print(f"❌ 修复失败: {e}")
    
    # 通用修复方法
    print("\n通用修复方法:")
    
    def fix_truncated_array_string(json_str):
        """修复截断的数组字符串"""
        # 查找模式：["文本" ]] 
        # 应该修复为：["文本"]
        pattern = r'(\["[^"]*")\s+\]\]'
        
        def replace_func(match):
            return match.group(1) + ']'
        
        fixed = re.sub(pattern, replace_func, json_str)
        return fixed
    
    fixed_json_2 = fix_truncated_array_string(problematic_json)
    print(f"通用修复结果: {fixed_json_2}")
    
    try:
        data = json.loads(fixed_json_2)
        print("✅ 通用修复成功！")
        return fixed_json_2
    except Exception as e:
        print(f"❌ 通用修复失败: {e}")
    
    return None

def test_with_memory_service():
    """使用记忆服务测试修复"""
    print("\n🧪 使用记忆服务测试")
    print("=" * 50)
    
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    from app.services.memory_extraction_service import get_memory_extraction_service
    
    # 模拟AI响应
    ai_response = '''{ "characters": [ { "name": "林深", "identity": "记忆修复师", "description": "穿梭在新东京第三区的霓虹灯海中", "personality_tags": ["专业", "冷静"], "appearance": "金属义眼", "background": "专门处理被病毒侵蚀的记忆碎片", "current_status": "正在处理异常危险的案件", "goals": ["修复客户受损记忆"], "abilities": ["记忆修复技术"], "weaknesses": ["对病毒数据敏感" ]]}}'''
    
    service = get_memory_extraction_service()
    
    try:
        # 使用服务的JSON提取方法
        json_str = service._extract_json_from_response(ai_response)
        print(f"提取的JSON: {json_str}")
        
        # 尝试解析
        data = json.loads(json_str)
        print("✅ 服务修复成功！")
        print(f"角色数量: {len(data.get('characters', []))}")
        
    except Exception as e:
        print(f"❌ 服务修复失败: {e}")

if __name__ == "__main__":
    fixed_json = precise_json_fix()
    
    if fixed_json:
        test_with_memory_service()
