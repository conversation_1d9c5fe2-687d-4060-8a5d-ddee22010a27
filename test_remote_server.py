#!/usr/bin/env python3
"""
测试腾讯云远程服务器连接
"""

import requests
import json
import sys
from datetime import datetime

def test_remote_ollama_server():
    """测试远程Ollama服务器"""
    print("🧪 测试腾讯云远程Ollama服务器")
    print("=" * 50)
    
    server_config = {
        'name': '腾讯云服务器',
        'host': '*************',
        'port': 6399,
        'base_url': 'http://*************:6399'
    }
    
    print(f"服务器: {server_config['name']}")
    print(f"地址: {server_config['host']}:{server_config['port']}")
    print(f"基础URL: {server_config['base_url']}")
    print()
    
    # 测试版本信息
    print("1. 测试版本信息...")
    try:
        version_url = f"{server_config['base_url']}/api/version"
        response = requests.get(version_url, timeout=10)
        
        if response.status_code == 200:
            version_info = response.json()
            print(f"  ✅ 版本信息获取成功")
            print(f"  📊 版本: {version_info.get('version', 'unknown')}")
        else:
            print(f"  ❌ 版本信息获取失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 连接失败: {e}")
        return False
    
    # 测试模型列表
    print("\n2. 测试模型列表...")
    try:
        tags_url = f"{server_config['base_url']}/api/tags"
        response = requests.get(tags_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"  ✅ 模型列表获取成功")
            print(f"  📊 模型数量: {len(models)}")
            
            if models:
                print("  📋 可用模型:")
                for i, model in enumerate(models[:5], 1):  # 只显示前5个
                    name = model.get('name', 'unknown')
                    size = model.get('size', 0)
                    size_gb = round(size / (1024**3), 1) if size > 0 else 0
                    print(f"    {i}. {name} ({size_gb}GB)")
                
                if len(models) > 5:
                    print(f"    ... 还有 {len(models) - 5} 个模型")
            else:
                print("  ⚠️  没有可用模型")
                
        else:
            print(f"  ❌ 模型列表获取失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"  ❌ 获取模型列表失败: {e}")
        return False
    
    # 测试模型生成（如果有模型的话）
    if models:
        print("\n3. 测试模型生成...")
        test_model = models[0]['name']  # 使用第一个模型
        
        try:
            generate_url = f"{server_config['base_url']}/api/generate"
            payload = {
                'model': test_model,
                'prompt': '你好，请简单介绍一下自己。',
                'stream': False,
                'options': {
                    'temperature': 0.7,
                    'num_predict': 100
                }
            }
            
            print(f"  🤖 使用模型: {test_model}")
            print(f"  💬 测试提示: {payload['prompt']}")
            
            response = requests.post(generate_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '')
                
                print(f"  ✅ 生成成功")
                print(f"  📝 生成内容: {generated_text[:100]}...")
                
                # 显示生成统计
                if 'eval_count' in result:
                    print(f"  📊 生成token数: {result.get('eval_count', 0)}")
                if 'eval_duration' in result:
                    duration_ms = result.get('eval_duration', 0) / 1000000  # 纳秒转毫秒
                    print(f"  ⏱️  生成时间: {duration_ms:.0f}ms")
                    
            else:
                print(f"  ❌ 生成失败: HTTP {response.status_code}")
                print(f"  📄 错误信息: {response.text[:200]}")
                
        except Exception as e:
            print(f"  ❌ 生成测试失败: {e}")
    
    print("\n✅ 远程服务器测试完成")
    return True


def test_local_api_integration():
    """测试本地API集成"""
    print("\n🧪 测试本地API集成")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 测试获取远程服务器列表
    print("1. 测试获取远程服务器列表...")
    try:
        response = requests.get(f"{base_url}/api/v1/ai-models/remote-servers", timeout=10)
        
        if response.status_code == 200:
            servers = response.data if hasattr(response, 'data') else response.json()
            print(f"  ✅ 获取成功")
            print(f"  📊 服务器数量: {len(servers)}")
            
            for server in servers:
                print(f"    - {server.get('name', 'unknown')}: {server.get('base_url', 'unknown')}")
        else:
            print(f"  ❌ 获取失败: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print(f"  ⚠️  本地API服务未启动")
        return False
    except Exception as e:
        print(f"  ❌ 获取失败: {e}")
        return False
    
    # 测试获取远程模型
    print("\n2. 测试获取远程模型...")
    try:
        response = requests.get(f"{base_url}/api/v1/ai-models/remote-models", timeout=30)
        
        if response.status_code == 200:
            models = response.data if hasattr(response, 'data') else response.json()
            print(f"  ✅ 获取成功")
            print(f"  📊 远程模型数量: {len(models)}")
            
            for model in models[:3]:  # 只显示前3个
                name = model.get('name', 'unknown')
                status = model.get('status', 'unknown')
                print(f"    - {name}: {status}")
        else:
            print(f"  ❌ 获取失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 获取失败: {e}")
        return False
    
    # 测试服务器连接测试
    print("\n3. 测试服务器连接测试...")
    try:
        test_payload = {
            'base_url': 'http://*************:6399',
            'name': '腾讯云服务器'
        }
        
        response = requests.post(f"{base_url}/api/v1/ai-models/test-remote-server", 
                               json=test_payload, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ 测试完成")
            print(f"  📊 连接成功: {result.get('success', False)}")
            print(f"  💬 消息: {result.get('message', 'unknown')}")
            
            if result.get('success'):
                print(f"  📊 模型数量: {result.get('models_count', 0)}")
                print(f"  📊 版本: {result.get('version', 'unknown')}")
        else:
            print(f"  ❌ 测试失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False
    
    print("\n✅ 本地API集成测试完成")
    return True


def main():
    """主函数"""
    print("🚀 腾讯云远程服务器集成测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试远程服务器
    remote_success = test_remote_ollama_server()
    
    # 测试本地API集成
    local_success = test_local_api_integration()
    
    # 总结
    print("\n📊 测试总结")
    print("=" * 60)
    print(f"远程服务器连接: {'✅ 成功' if remote_success else '❌ 失败'}")
    print(f"本地API集成: {'✅ 成功' if local_success else '❌ 失败'}")
    
    if remote_success and local_success:
        print("\n🎉 所有测试通过！远程服务器集成成功。")
        print("\n🎯 现在您可以:")
        print("1. 在AI模型管理页面查看远程服务器状态")
        print("2. 使用远程服务器的模型进行AI生成")
        print("3. 为项目选择远程服务器模型")
    else:
        print("\n⚠️  部分测试失败，请检查:")
        if not remote_success:
            print("- 远程服务器连接和配置")
        if not local_success:
            print("- 本地API服务状态")
    
    return remote_success and local_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
