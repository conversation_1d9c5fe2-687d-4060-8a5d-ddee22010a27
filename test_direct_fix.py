#!/usr/bin/env python3
"""
直接测试JSON修复功能
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.memory_extraction_service import get_memory_extraction_service

def test_direct_fix():
    """直接测试JSON修复"""
    print("🧪 直接测试JSON修复")
    print("=" * 50)
    
    service = get_memory_extraction_service()
    
    # 测试案例1：多余的右方括号
    test_json_1 = '''{ "characters": [ { "name": "林深", "weaknesses": ["对病毒数据敏感" ]}'''
    
    print("1. 测试多余右方括号的修复:")
    print(f"   原始: {test_json_1}")
    
    try:
        fixed = service._smart_fix_json(test_json_1, 0)
        print(f"   修复后: {fixed}")
        
        data = json.loads(fixed)
        print("   ✅ 修复成功！")
        print(f"   角色数量: {len(data.get('characters', []))}")
        
    except Exception as e:
        print(f"   ❌ 修复失败: {e}")
    
    # 测试案例2：缺少引号的字符串
    test_json_2 = '''{ "characters": [ { "name": "林深", "weaknesses": ["对病毒数据敏感 ]}}'''
    
    print("\n2. 测试缺少引号的修复:")
    print(f"   原始: {test_json_2}")
    
    try:
        fixed = service._smart_fix_json(test_json_2, 0)
        print(f"   修复后: {fixed}")
        
        data = json.loads(fixed)
        print("   ✅ 修复成功！")
        print(f"   角色数量: {len(data.get('characters', []))}")
        
    except Exception as e:
        print(f"   ❌ 修复失败: {e}")
    
    # 测试案例3：缺少右括号
    test_json_3 = '''{ "characters": [ { "name": "林深", "weaknesses": ["对病毒数据敏感"] }'''
    
    print("\n3. 测试缺少右括号的修复:")
    print(f"   原始: {test_json_3}")
    
    try:
        fixed = service._smart_fix_json(test_json_3, 1)  # 缺少1个右括号
        print(f"   修复后: {fixed}")
        
        data = json.loads(fixed)
        print("   ✅ 修复成功！")
        print(f"   角色数量: {len(data.get('characters', []))}")
        
    except Exception as e:
        print(f"   ❌ 修复失败: {e}")

def test_real_problematic_json():
    """测试真实的问题JSON"""
    print("\n🧪 测试真实问题JSON")
    print("=" * 50)
    
    # 这是从实际错误中提取的JSON
    problematic_json = '''{ "characters": [ { "name": "林深", "identity": "记忆修复师", "description": "穿梭在新东京第三区的霓虹灯海中", "personality_tags": ["专业", "冷静"], "appearance": "金属义眼", "background": "专门处理被病毒侵蚀的记忆碎片", "current_status": "正在处理异常危险的案件", "goals": ["修复客户受损记忆"], "abilities": ["记忆修复技术"], "weaknesses": ["对病毒数据敏感" ]]}}'''
    
    service = get_memory_extraction_service()
    
    print("分析问题:")
    left_brackets = problematic_json.count('[')
    right_brackets = problematic_json.count(']')
    left_braces = problematic_json.count('{')
    right_braces = problematic_json.count('}')
    quotes = problematic_json.count('"')
    
    print(f"   左方括号: {left_brackets}")
    print(f"   右方括号: {right_brackets}")
    print(f"   左大括号: {left_braces}")
    print(f"   右大括号: {right_braces}")
    print(f"   引号数量: {quotes}")
    
    # 手动修复：移除多余的右方括号
    print("\n手动修复:")
    
    # 找到问题位置："weaknesses": ["对病毒数据敏感" ]]
    # 应该是："weaknesses": ["对病毒数据敏感"]}
    
    # 方法1：直接替换
    fixed_json = problematic_json.replace('["对病毒数据敏感" ]]', '["对病毒数据敏感"]}')
    print(f"   修复后: {fixed_json}")
    
    try:
        data = json.loads(fixed_json)
        print("   ✅ 手动修复成功！")
        print(f"   角色数量: {len(data.get('characters', []))}")
        
        for char in data.get('characters', []):
            print(f"     - {char.get('name', 'Unknown')}: {char.get('weaknesses', [])}")
            
    except Exception as e:
        print(f"   ❌ 手动修复失败: {e}")
    
    # 方法2：使用智能修复
    print("\n使用智能修复:")
    try:
        # 先移除多余的右方括号
        if right_brackets > left_brackets:
            excess = right_brackets - left_brackets
            print(f"   检测到 {excess} 个多余的右方括号")
            
            # 移除多余的右方括号
            temp_json = problematic_json
            for _ in range(excess):
                last_bracket = temp_json.rfind(']')
                if last_bracket != -1:
                    temp_json = temp_json[:last_bracket] + temp_json[last_bracket + 1:]
            
            print(f"   移除多余方括号后: {temp_json}")
            
            data = json.loads(temp_json)
            print("   ✅ 智能修复成功！")
            print(f"   角色数量: {len(data.get('characters', []))}")
            
    except Exception as e:
        print(f"   ❌ 智能修复失败: {e}")

if __name__ == "__main__":
    print("🔧 JSON修复功能直接测试")
    print("=" * 60)
    
    test_direct_fix()
    test_real_problematic_json()
