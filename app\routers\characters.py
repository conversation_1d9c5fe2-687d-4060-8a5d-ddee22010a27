"""
角色管理API路由
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, status, Query
from pydantic import BaseModel
import logging

from app.models.character_new import (
    CharacterCreate, CharacterUpdate, CharacterResponse,
    CharacterListItem, CharacterRelationship
)
from app.services.character_service import character_service
from app.services.character_data_converter import get_character_data_converter

# 创建两个路由器：一个用于传统路径，一个用于高级功能
router = APIRouter(prefix="/projects/{project_id}/characters", tags=["characters"])
advanced_router = APIRouter(prefix="/characters", tags=["advanced-characters"])

logger = logging.getLogger(__name__)

# 响应模型
class CharacterListItemAdvanced(BaseModel):
    id: str
    name: str
    description: str
    personality_tags: List[str]
    appearance: str
    first_appearance_chapter: str
    total_chapters: int
    relationship_count: int
    event_count: int
    importance_score: float

class CharacterDetailAdvanced(BaseModel):
    basic_info: Dict[str, Any]
    personality: Dict[str, Any]
    relationships: List[Dict[str, Any]]
    timeline: List[Dict[str, Any]]
    development_analysis: Dict[str, Any]
    statistics: Dict[str, Any]

class RelationshipNetworkAdvanced(BaseModel):
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]
    stats: Dict[str, Any]


@router.post("", response_model=CharacterResponse, status_code=status.HTTP_201_CREATED)
async def create_character(project_id: str, character_data: CharacterCreate):
    """创建角色"""
    try:
        return character_service.create_character(project_id, character_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("", response_model=List[CharacterListItem])
async def get_characters(
    project_id: str,
    role: Optional[str] = Query(None, description="角色类型筛选"),
    status: Optional[str] = Query(None, description="角色状态筛选"),
    importance: Optional[str] = Query(None, description="角色重要性筛选")
):
    """获取项目的所有角色"""
    try:
        if role or status or importance:
            return character_service.filter_characters(project_id, role, status, importance)
        else:
            return character_service.get_characters(project_id)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/{character_id}", response_model=CharacterResponse)
async def get_character(project_id: str, character_id: str):
    """获取角色详情"""
    try:
        character = character_service.get_character(project_id, character_id)
        if not character:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
        return character
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.put("/{character_id}", response_model=CharacterResponse)
async def update_character(project_id: str, character_id: str, character_data: CharacterUpdate):
    """更新角色"""
    try:
        updated_character = character_service.update_character(project_id, character_id, character_data)
        if not updated_character:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
        return updated_character
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.delete("/{character_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_character(project_id: str, character_id: str):
    """删除角色"""
    try:
        success = character_service.delete_character(project_id, character_id)
        if not success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="角色不存在")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/search/{query}", response_model=List[CharacterListItem])
async def search_characters(project_id: str, query: str):
    """搜索角色"""
    try:
        return character_service.search_characters(project_id, query)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/stats", response_model=dict)
async def get_character_stats(project_id: str):
    """获取角色统计信息"""
    try:
        return character_service.get_character_stats(project_id)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# 角色关系相关API
@router.get("/relationships", response_model=List[CharacterRelationship])
async def get_character_relationships(project_id: str):
    """获取角色关系"""
    try:
        return character_service.get_character_relationships(project_id)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/relationships", response_model=CharacterRelationship, status_code=status.HTTP_201_CREATED)
async def create_character_relationship(
    project_id: str,
    character_a_id: str,
    character_b_id: str,
    relationship_type: str,
    description: Optional[str] = None,
    strength: int = 5
):
    """创建角色关系"""
    try:
        return character_service.create_character_relationship(
            project_id, character_a_id, character_b_id, 
            relationship_type, description, strength
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


# 高级角色管理API端点
@advanced_router.get("/{project_id}", response_model=List[CharacterListItemAdvanced])
async def get_advanced_character_list(
    project_id: str,
    sort_by: str = Query("importance", description="排序方式: name, importance, chapters, relationships"),
    filter_by: str = Query("all", description="过滤条件: all, main, supporting, minor")
):
    """获取高级角色列表"""
    try:
        # 获取基础角色列表
        characters = character_service.get_characters(project_id)

        # 使用数据转换器转换为高级格式
        converter = get_character_data_converter()
        advanced_characters = []

        for char in characters:
            try:
                # 将角色对象转换为字典（如果需要）
                if hasattr(char, '__dict__'):
                    char_dict = char.__dict__
                else:
                    char_dict = char

                # 转换为高级格式
                advanced_data = converter.convert_db_character_to_advanced_format(char_dict)
                advanced_char = CharacterListItemAdvanced(**advanced_data)
                advanced_characters.append(advanced_char)

            except Exception as e:
                # 如果转换失败，使用备用方法
                logger.warning(f"角色数据转换失败 {getattr(char, 'name', 'Unknown')}: {e}")
                fallback_char = CharacterListItemAdvanced(
                    id=getattr(char, 'id', ''),
                    name=getattr(char, 'name', 'Unknown'),
                    description=getattr(char, 'description', '')[:100],
                    personality_tags=[],
                    appearance=getattr(char, 'appearance', '')[:50],
                    first_appearance_chapter='unknown',
                    total_chapters=1,
                    relationship_count=0,
                    event_count=0,
                    importance_score=1.0
                )
                advanced_characters.append(fallback_char)

        # 应用过滤
        if filter_by != "all":
            advanced_characters = _filter_characters_by_importance(advanced_characters, filter_by)

        # 应用排序
        advanced_characters = _sort_characters(advanced_characters, sort_by)

        return advanced_characters

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取角色列表失败: {str(e)}")


@advanced_router.get("/{project_id}/{character_name}/detail", response_model=CharacterDetailAdvanced)
async def get_character_detail_advanced(project_id: str, character_name: str):
    """获取角色详细信息"""
    try:
        # 查找角色
        characters = character_service.get_characters(project_id)
        character = None
        for char in characters:
            if char.name == character_name:
                character = char
                break

        if not character:
            raise HTTPException(status_code=404, detail="角色不存在")

        # 构建详细信息
        detail = CharacterDetailAdvanced(
            basic_info={
                "id": character.id,
                "name": character.name,
                "description": character.description,
                "appearance": character.appearance,
                "current_version": 1,
                "first_appearance": "chapter-1",
                "latest_chapter": "chapter-1",
                "created_at": character.created_at.isoformat() if hasattr(character, 'created_at') else None,
                "last_updated": character.updated_at.isoformat() if hasattr(character, 'updated_at') else None
            },
            personality={
                "traits": character.personality_tags if hasattr(character, 'personality_tags') else [],
                "trait_evolution": [],
                "personality_analysis": {}
            },
            relationships=[],
            timeline=[],
            development_analysis={},
            statistics={
                "total_events": 0,
                "total_relationships": 0,
                "positive_relationships": 0,
                "negative_relationships": 0,
                "version_count": 1,
                "change_count": 0,
                "chapters_appeared": 1,
                "importance_score": _calculate_importance_score(character)
            }
        )

        return detail

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取角色详细信息失败: {str(e)}")


@advanced_router.get("/{project_id}/network", response_model=RelationshipNetworkAdvanced)
async def get_relationship_network_advanced(
    project_id: str,
    include_weak: bool = Query(False, description="是否包含弱关系"),
    min_strength: float = Query(0.3, description="最小关系强度")
):
    """获取角色关系网络"""
    try:
        # 获取角色列表
        characters = character_service.get_characters(project_id)

        # 构建节点
        nodes = []
        for char in characters:
            importance = _calculate_importance_score(char)
            nodes.append({
                "id": char.name,
                "name": char.name,
                "description": char.description[:50] + "..." if len(char.description) > 50 else char.description,
                "personality_tags": (char.personality_tags[:3] if hasattr(char, 'personality_tags') else []),
                "node_size": max(20, min(80, int(20 + importance * 6))),
                "node_color": _get_node_color(importance)
            })

        # 构建边（模拟关系）
        edges = []
        # 这里可以添加实际的关系数据

        # 网络统计
        stats = {
            "total_characters": len(nodes),
            "total_relationships": len(edges),
            "positive_relationships": 0,
            "negative_relationships": 0,
            "average_strength": 0.0,
            "network_density": 0.0
        }

        return RelationshipNetworkAdvanced(
            nodes=nodes,
            edges=edges,
            stats=stats
        )

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"获取关系网络失败: {str(e)}")


@advanced_router.get("/{project_id}/search")
async def search_characters_advanced(
    project_id: str,
    query: str = Query(..., description="搜索关键词"),
    search_type: str = Query("all", description="搜索类型: name, description, personality, all")
):
    """搜索角色"""
    try:
        characters = character_service.get_characters(project_id)
        results = []
        query_lower = query.lower()

        for character in characters:
            match_score = 0
            match_reasons = []

            # 名称匹配
            if search_type in ["name", "all"] and query_lower in character.name.lower():
                match_score += 10
                match_reasons.append("名称匹配")

            # 描述匹配
            if search_type in ["description", "all"] and query_lower in character.description.lower():
                match_score += 5
                match_reasons.append("描述匹配")

            # 性格标签匹配
            if search_type in ["personality", "all"] and hasattr(character, 'personality_tags'):
                for tag in character.personality_tags:
                    if query_lower in tag.lower():
                        match_score += 3
                        match_reasons.append(f"性格标签匹配: {tag}")

            if match_score > 0:
                results.append({
                    "character": {
                        "id": character.id,
                        "name": character.name,
                        "description": character.description[:100] + "..." if len(character.description) > 100 else character.description,
                        "personality_tags": character.personality_tags if hasattr(character, 'personality_tags') else []
                    },
                    "match_score": match_score,
                    "match_reasons": match_reasons
                })

        # 按匹配分数排序
        results.sort(key=lambda x: x["match_score"], reverse=True)

        return results

    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"搜索角色失败: {str(e)}")


# 辅助函数
def _calculate_importance_score(character) -> float:
    """计算角色重要性分数"""
    try:
        score = 1.0

        # 基于描述长度
        score += min(2.0, len(character.description) / 100)

        # 基于性格标签数量
        if hasattr(character, 'personality_tags'):
            score += len(character.personality_tags) * 0.1

        # 基于角色类型
        if hasattr(character, 'role'):
            if character.role == 'protagonist':
                score += 5.0
            elif character.role == 'antagonist':
                score += 4.0
            elif character.role == 'supporting':
                score += 2.0

        return min(10.0, score)

    except Exception:
        return 1.0


def _filter_characters_by_importance(characters: List[CharacterListItemAdvanced], filter_by: str) -> List[CharacterListItemAdvanced]:
    """按重要性过滤角色"""
    if filter_by == "main":
        return [c for c in characters if c.importance_score >= 7.0]
    elif filter_by == "supporting":
        return [c for c in characters if 3.0 <= c.importance_score < 7.0]
    elif filter_by == "minor":
        return [c for c in characters if c.importance_score < 3.0]
    else:
        return characters


def _sort_characters(characters: List[CharacterListItemAdvanced], sort_by: str) -> List[CharacterListItemAdvanced]:
    """排序角色列表"""
    if sort_by == "name":
        return sorted(characters, key=lambda x: x.name)
    elif sort_by == "importance":
        return sorted(characters, key=lambda x: x.importance_score, reverse=True)
    elif sort_by == "chapters":
        return sorted(characters, key=lambda x: x.total_chapters, reverse=True)
    elif sort_by == "relationships":
        return sorted(characters, key=lambda x: x.relationship_count, reverse=True)
    else:
        return characters


def _get_node_color(importance: float) -> str:
    """根据重要性获取节点颜色"""
    if importance >= 7.0:
        return "#FF6B6B"  # 主角 - 红色
    elif importance >= 4.0:
        return "#4ECDC4"  # 重要配角 - 青色
    elif importance >= 2.0:
        return "#45B7D1"  # 一般配角 - 蓝色
    else:
        return "#96CEB4"  # 次要角色 - 绿色
