#!/usr/bin/env python3
"""
统一AI创作助手前端集成测试
"""

import sys
import os
import requests
from datetime import datetime

def test_api_endpoints():
    """测试API端点可访问性"""
    print("🧪 测试API端点")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    endpoints = [
        f"/api/v1/projects/{project_id}/ai-assistant/capabilities",
        f"/api/v1/projects/{project_id}/ai-assistant/conversation-starters",
        f"/api/v1/projects/{project_id}/ai-assistant/model-options"
    ]
    
    results = []
    
    for endpoint in endpoints:
        try:
            url = base_url + endpoint
            print(f"测试: {endpoint}")
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                print(f"  ✅ 成功 (状态码: {response.status_code})")
                data = response.json()
                print(f"  📊 响应数据: {len(str(data))} 字符")
                results.append((endpoint, True, response.status_code))
            else:
                print(f"  ❌ 失败 (状态码: {response.status_code})")
                results.append((endpoint, False, response.status_code))
                
        except requests.exceptions.ConnectionError:
            print(f"  ⚠️  连接失败 - 服务器未启动")
            results.append((endpoint, False, "连接失败"))
        except Exception as e:
            print(f"  ❌ 错误: {e}")
            results.append((endpoint, False, str(e)))
    
    return results


def test_chat_api():
    """测试聊天API"""
    print("\n🧪 测试聊天API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    test_messages = [
        {
            "message": "分析一下林深这个角色的性格特点",
            "mode": "analysis"
        },
        {
            "message": "帮我写一段对话",
            "mode": "generation"
        },
        {
            "message": "怎么写好环境描写？",
            "mode": "auto"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_messages, 1):
        try:
            print(f"\n测试案例 {i}: {test_case['message'][:30]}...")
            
            url = f"{base_url}/api/v1/projects/{project_id}/ai-assistant/chat"
            payload = {
                "message": test_case["message"],
                "mode": test_case["mode"],
                "target_length": 300,
                "include_suggestions": True
            }
            
            response = requests.post(url, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ 成功")
                print(f"  📊 响应成功: {data.get('success', False)}")
                print(f"  🤖 使用模式: {data.get('mode_used', 'unknown')}")
                print(f"  🎯 检测意图: {data.get('intent_detected', 'unknown')}")
                
                if data.get('response_text'):
                    print(f"  💬 响应长度: {len(data['response_text'])} 字符")
                
                if data.get('generated_content'):
                    print(f"  ✍️ 生成内容长度: {len(data['generated_content'])} 字符")
                
                if data.get('suggestions'):
                    print(f"  💡 建议数量: {len(data['suggestions'])}")
                
                results.append((test_case["message"], True, data))
            else:
                print(f"  ❌ 失败 (状态码: {response.status_code})")
                print(f"  📄 响应: {response.text[:200]}...")
                results.append((test_case["message"], False, response.status_code))
                
        except requests.exceptions.ConnectionError:
            print(f"  ⚠️  连接失败 - 服务器未启动")
            results.append((test_case["message"], False, "连接失败"))
        except Exception as e:
            print(f"  ❌ 错误: {e}")
            results.append((test_case["message"], False, str(e)))
    
    return results


def check_frontend_files():
    """检查前端文件是否存在"""
    print("\n🧪 检查前端文件")
    print("=" * 50)
    
    frontend_files = [
        "frontend-vue/src/api/unified-ai-assistant.js",
        "frontend-vue/src/components/ai/UnifiedAIAssistant.vue",
        "frontend-vue/src/views/AIAssistantView.vue"
    ]
    
    results = []
    
    for file_path in frontend_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  ✅ {file_path} ({file_size} 字节)")
            results.append((file_path, True, file_size))
        else:
            print(f"  ❌ {file_path} (不存在)")
            results.append((file_path, False, 0))
    
    return results


def generate_integration_report(api_results, chat_results, file_results):
    """生成集成报告"""
    print("\n📊 前端集成报告")
    print("=" * 60)
    
    # API端点测试结果
    api_success = sum(1 for _, success, _ in api_results if success)
    print(f"API端点测试: {api_success}/{len(api_results)} 通过")
    
    # 聊天API测试结果
    chat_success = sum(1 for _, success, _ in chat_results if success)
    print(f"聊天API测试: {chat_success}/{len(chat_results)} 通过")
    
    # 前端文件检查结果
    file_success = sum(1 for _, exists, _ in file_results if exists)
    print(f"前端文件检查: {file_success}/{len(file_results)} 存在")
    
    total_tests = len(api_results) + len(chat_results) + len(file_results)
    total_success = api_success + chat_success + file_success
    
    print(f"\n总体结果: {total_success}/{total_tests} 测试通过")
    
    if total_success == total_tests:
        print("🎉 所有测试通过！前端集成成功。")
        print("\n📝 集成完成的功能:")
        print("✅ 统一AI助手API接口")
        print("✅ 前端组件和页面")
        print("✅ 聊天和意图分析功能")
        print("✅ 路由和导航集成")
        
        print("\n🚀 使用指南:")
        print("1. 启动后端服务: python -m uvicorn app.main:app --reload")
        print("2. 启动前端服务: cd frontend-vue && npm run dev")
        print("3. 访问项目详情页面，点击'AI创作助手'")
        print("4. 或直接访问: /projects/{project_id}/ai-assistant")
    else:
        print("⚠️  部分测试失败，请检查相关配置。")
        
        if api_success < len(api_results):
            print("- 检查后端服务是否启动")
        if file_success < len(file_results):
            print("- 检查前端文件是否正确创建")
    
    return total_success == total_tests


def main():
    """主函数"""
    print("🚀 统一AI创作助手前端集成测试")
    print("=" * 60)
    
    # 检查前端文件
    file_results = check_frontend_files()
    
    # 测试API端点
    api_results = test_api_endpoints()
    
    # 测试聊天API
    chat_results = test_chat_api()
    
    # 生成报告
    success = generate_integration_report(api_results, chat_results, file_results)
    
    return success


if __name__ == "__main__":
    print("🔧 统一AI创作助手前端集成测试工具")
    print("=" * 60)
    
    success = main()
    
    if success:
        print("\n✅ 前端集成测试完成，所有功能正常！")
        print("\n🎯 现在您可以:")
        print("1. 在项目详情页面使用AI创作助手")
        print("2. 在内容创作页面使用统一AI助手")
        print("3. 享受智能的分析和生成功能")
    else:
        print("\n❌ 部分测试失败，请检查配置和服务状态。")
    
    sys.exit(0 if success else 1)
