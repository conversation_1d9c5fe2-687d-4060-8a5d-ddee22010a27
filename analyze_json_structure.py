#!/usr/bin/env python3
"""
分析JSON结构问题
"""

import json
import re

def analyze_json_structure():
    """分析JSON结构"""
    print("🔍 分析JSON结构问题")
    print("=" * 50)
    
    # 问题JSON
    problematic_json = '''{ "characters": [ { "name": "林深", "identity": "记忆修复师", "description": "穿梭在新东京第三区的霓虹灯海中", "personality_tags": ["专业", "冷静"], "appearance": "金属义眼", "background": "专门处理被病毒侵蚀的记忆碎片", "current_status": "正在处理异常危险的案件", "goals": ["修复客户受损记忆"], "abilities": ["记忆修复技术"], "weaknesses": ["对病毒数据敏感" ]]}}'''
    
    print(f"原始JSON: {problematic_json}")
    print()
    
    # 查找所有的数组结构
    array_pattern = r'"(\w+)"\s*:\s*\[([^\]]*)\]'
    arrays = re.findall(array_pattern, problematic_json)
    
    print("找到的数组字段:")
    for field, content in arrays:
        print(f"  {field}: [{content}]")
    
    print()
    
    # 查找问题位置
    print("查找连续的右方括号:")
    consecutive_brackets = re.findall(r'\]\]+', problematic_json)
    for i, brackets in enumerate(consecutive_brackets):
        print(f"  位置 {i+1}: '{brackets}' (长度: {len(brackets)})")
    
    # 查找具体的问题模式
    problem_pattern = r'"weaknesses"\s*:\s*\[[^\]]*\]\]'
    problem_match = re.search(problem_pattern, problematic_json)
    
    if problem_match:
        print(f"\n找到问题模式: {problem_match.group()}")
        
        # 修复：移除多余的右方括号
        fixed_json = re.sub(r'("weaknesses"\s*:\s*\[[^\]]*)\]\]', r'\1]}', problematic_json)
        print(f"修复后: {fixed_json}")
        
        try:
            data = json.loads(fixed_json)
            print("✅ 修复成功！")
            print(f"角色数量: {len(data.get('characters', []))}")
            
            for char in data.get('characters', []):
                print(f"  角色: {char.get('name', 'Unknown')}")
                print(f"  弱点: {char.get('weaknesses', [])}")
                
        except Exception as e:
            print(f"❌ 修复失败: {e}")
    
    # 通用修复方法
    print("\n通用修复方法:")
    
    # 方法1：移除连续的右方括号，只保留一个
    fixed_json_1 = re.sub(r'\]\]+', ']', problematic_json)
    print(f"方法1 - 移除连续右方括号: {fixed_json_1}")
    
    try:
        data = json.loads(fixed_json_1)
        print("✅ 方法1成功！")
    except Exception as e:
        print(f"❌ 方法1失败: {e}")
    
    # 方法2：检查数组结构并修复
    def fix_array_structure(json_str):
        # 查找所有的数组字段
        def replace_array(match):
            field = match.group(1)
            content = match.group(2)
            # 确保数组正确闭合
            return f'"{field}": [{content}]'
        
        # 修复数组结构
        fixed = re.sub(r'"(\w+)"\s*:\s*\[([^\]]*)\]\]+', replace_array, json_str)
        return fixed
    
    fixed_json_2 = fix_array_structure(problematic_json)
    print(f"方法2 - 修复数组结构: {fixed_json_2}")
    
    try:
        data = json.loads(fixed_json_2)
        print("✅ 方法2成功！")
    except Exception as e:
        print(f"❌ 方法2失败: {e}")

if __name__ == "__main__":
    analyze_json_structure()
