#!/usr/bin/env python3
"""
增强小说创作Agent集成测试
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.enhanced_novel_agent_service import (
    get_enhanced_novel_agent_service, GenerationRequest, GenerationMode,
    ConflictType, EmotionType, PleasureType
)
from app.services.segmented_generation_service import get_segmented_generation_service
from app.services.character_portrayal_service import get_character_portrayal_service, PortrayalType
from app.services.environment_description_service import (
    get_environment_description_service, EnvironmentContext, 
    AtmosphereType, EnvironmentType, SensoryType
)


async def test_enhanced_generation():
    """测试增强生成功能"""
    print("🧪 测试增强小说生成")
    print("=" * 50)
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    try:
        agent_service = get_enhanced_novel_agent_service()
        
        # 创建生成请求
        request = GenerationRequest(
            project_id=project_id,
            user_prompt="林深在新东京第三区遇到了一个神秘的客户，这个客户的记忆被严重损坏，隐藏着一个巨大的秘密。请继续这个故事。",
            generation_mode=GenerationMode.NEXT_CHAPTER,
            target_length=600,
            target_conflict_type=ConflictType.INTERPERSONAL,
            target_emotion_type=EmotionType.FEAR,
            target_pleasure_type=PleasureType.DISCOVERY,
            environment_emphasis=True,
            dialogue_heavy=False,
            action_heavy=True
        )
        
        print(f"生成请求: {request.user_prompt[:50]}...")
        print(f"目标长度: {request.target_length}字")
        print(f"生成模式: {request.generation_mode.value}")
        
        # 执行生成
        result = await agent_service.generate_content(request)
        
        print(f"\n生成结果:")
        print(f"成功: {'是' if result.success else '否'}")
        print(f"字数: {result.word_count}")
        print(f"质量分数: {result.quality_score:.2f}")
        print(f"生成时间: {result.generation_time:.2f}秒")
        print(f"段落数: {result.segments_generated}")
        
        if result.success:
            print(f"\n生成内容:")
            print("-" * 40)
            print(result.content)
            print("-" * 40)
            
            if result.model_analysis:
                print(f"\n三维模型分析:")
                print(f"冲突类型: {result.model_analysis.conflict.conflict_type.value}")
                print(f"冲突强度: {result.model_analysis.conflict.intensity.value}")
                print(f"情绪类型: {result.model_analysis.emotion.primary_emotion.value}")
                print(f"情绪强度: {result.model_analysis.emotion.intensity.value}")
                print(f"爽点类型: {result.model_analysis.pleasure.pleasure_type.value}")
                print(f"爽点强度: {result.model_analysis.pleasure.intensity:.2f}")
                print(f"整体张力: {result.model_analysis.overall_tension:.2f}")
                print(f"叙事动力: {result.model_analysis.narrative_momentum:.2f}")
                print(f"读者参与度: {result.model_analysis.reader_engagement:.2f}")
            
            if result.suggestions:
                print(f"\n改进建议:")
                for i, suggestion in enumerate(result.suggestions, 1):
                    print(f"  {i}. {suggestion}")
            
            if result.strengths:
                print(f"\n优点:")
                for i, strength in enumerate(result.strengths, 1):
                    print(f"  {i}. {strength}")
            
            if result.weaknesses:
                print(f"\n不足:")
                for i, weakness in enumerate(result.weaknesses, 1):
                    print(f"  {i}. {weakness}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_segmented_generation():
    """测试分段生成功能"""
    print("\n🧪 测试分段生成")
    print("=" * 50)
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    try:
        segmented_service = get_segmented_generation_service()
        
        # 启动分段生成会话
        session = await segmented_service.start_generation_session(
            project_id=project_id,
            user_prompt="描写林深在雨夜中追踪一个神秘人物的紧张场面",
            target_length=500
        )
        
        print(f"会话ID: {session.session_id}")
        print(f"计划段落数: {session.plan.estimated_segments}")
        print(f"目标长度: {session.plan.target_length}字")
        
        # 逐段生成
        generated_segments = []
        segment_count = 0
        
        while segment_count < 3:  # 最多生成3段作为测试
            segment = await segmented_service.generate_next_segment(session.session_id)
            if not segment:
                break
            
            segment_count += 1
            generated_segments.append(segment)
            
            print(f"\n段落 {segment_count}:")
            print(f"类型: {segment.segment_type.value}")
            print(f"字数: {segment.word_count}")
            print(f"内容: {segment.content[:100]}...")
            
            if segment.model_analysis:
                print(f"质量评分: 张力={segment.model_analysis.overall_tension:.2f}, "
                      f"动力={segment.model_analysis.narrative_momentum:.2f}, "
                      f"参与度={segment.model_analysis.reader_engagement:.2f}")
        
        # 获取完整内容
        complete_content = segmented_service.get_generated_content(session.session_id)
        print(f"\n完整内容 ({len(complete_content)}字):")
        print("-" * 40)
        print(complete_content)
        print("-" * 40)
        
        # 结束会话
        result = segmented_service.end_session(session.session_id)
        print(f"\n会话结束:")
        print(f"总字数: {result['total_words']}")
        print(f"段落数: {result['segments_count']}")
        print(f"生成时间: {result['generation_time']:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 分段生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_character_portrayal():
    """测试人物刻画功能"""
    print("\n🧪 测试人物刻画")
    print("=" * 50)
    
    try:
        portrayal_service = get_character_portrayal_service()
        
        # 测试角色信息
        character_info = {
            "name": "林深",
            "personality_tags": ["confident", "mysterious", "intelligent"],
            "description": "新东京第三区的顶尖记忆修复师"
        }
        
        # 测试对话刻画
        dialogue_portrayal = portrayal_service.generate_character_portrayal(
            character_info,
            PortrayalType.DIALOGUE,
            {"dialogue_content": "这个案子比我想象的更复杂"}
        )
        
        print(f"对话刻画:")
        print(f"  {dialogue_portrayal}")
        
        # 测试动作刻画
        action_portrayal = portrayal_service.generate_character_portrayal(
            character_info,
            PortrayalType.ACTION,
            {"situation_description": "面对危险时的反应"}
        )
        
        print(f"\n动作刻画:")
        print(f"  {action_portrayal}")
        
        # 测试内心独白
        thought_portrayal = portrayal_service.generate_character_portrayal(
            character_info,
            PortrayalType.INNER_THOUGHT,
            {"conflict_description": "是否要接受这个危险的任务"}
        )
        
        print(f"\n内心独白:")
        print(f"  {thought_portrayal}")
        
        # 获取刻画建议
        suggestions = portrayal_service.get_portrayal_suggestions(
            character_info,
            {"type": "conflict"}
        )
        
        print(f"\n刻画建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 人物刻画测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_description():
    """测试环境描写功能"""
    print("\n🧪 测试环境描写")
    print("=" * 50)
    
    try:
        env_service = get_environment_description_service()
        
        # 创建环境上下文
        context = EnvironmentContext(
            location_name="新东京第三区的暗巷",
            time_of_day="night",
            weather="rainy",
            season="winter",
            character_mood="tense",
            story_phase="conflict"
        )
        
        # 生成紧张氛围的户外环境描写
        description = env_service.generate_environment_description(
            context,
            AtmosphereType.TENSE,
            EnvironmentType.URBAN,
            [SensoryType.VISUAL, SensoryType.AUDITORY, SensoryType.TACTILE],
            200
        )
        
        print(f"环境描写 (紧张氛围):")
        print(f"  {description}")
        
        # 生成神秘氛围的室内环境描写
        context.location_name = "废弃的记忆修复诊所"
        context.time_of_day = "midnight"
        
        mysterious_description = env_service.generate_environment_description(
            context,
            AtmosphereType.MYSTERIOUS,
            EnvironmentType.INDOOR,
            [SensoryType.VISUAL, SensoryType.OLFACTORY],
            150
        )
        
        print(f"\n环境描写 (神秘氛围):")
        print(f"  {mysterious_description}")
        
        # 获取氛围建议
        suggestions = env_service.get_atmosphere_suggestions(
            {"phase": "climax"}
        )
        
        print(f"\n氛围建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境描写测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_generation_suggestions():
    """测试生成建议功能"""
    print("\n🧪 测试生成建议")
    print("=" * 50)
    
    try:
        agent_service = get_enhanced_novel_agent_service()
        
        project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
        suggestions = agent_service.get_generation_suggestions(project_id)
        
        print("生成建议:")
        for i, suggestion in enumerate(suggestions, 1):
            print(f"  {i}. {suggestion['title']}")
            print(f"     模式: {suggestion['mode']}")
            print(f"     描述: {suggestion['description']}")
            print(f"     推荐长度: {suggestion['recommended_length']}字")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 生成建议测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_all_tests():
    """运行所有测试"""
    print("🚀 增强小说创作Agent集成测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试增强生成
    result1 = await test_enhanced_generation()
    test_results.append(("增强生成", result1))
    
    # 测试分段生成
    result2 = await test_segmented_generation()
    test_results.append(("分段生成", result2))
    
    # 测试人物刻画
    result3 = test_character_portrayal()
    test_results.append(("人物刻画", result3))
    
    # 测试环境描写
    result4 = test_environment_description()
    test_results.append(("环境描写", result4))
    
    # 测试生成建议
    result5 = test_generation_suggestions()
    test_results.append(("生成建议", result5))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强小说创作Agent功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
    
    return passed == total


if __name__ == "__main__":
    print("🔧 增强小说创作Agent测试工具")
    print("=" * 60)
    
    # 运行异步测试
    success = asyncio.run(run_all_tests())
    
    if success:
        print("\n✅ 测试完成，系统功能正常！")
        print("\n📝 使用说明:")
        print("1. 使用 /api/v1/projects/{project_id}/enhanced-agent/generate 进行增强生成")
        print("2. 使用 /api/v1/projects/{project_id}/enhanced-agent/segmented/start 开始分段生成")
        print("3. 使用 /api/v1/projects/{project_id}/enhanced-agent/suggestions 获取生成建议")
        print("4. 支持三维动态模型：冲突-情绪-爽点")
        print("5. 支持多种生成模式：下一章、对话场景、动作序列等")
    else:
        print("\n❌ 测试失败，请检查系统配置和依赖。")
    
    sys.exit(0 if success else 1)
