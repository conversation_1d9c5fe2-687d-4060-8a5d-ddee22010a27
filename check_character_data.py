#!/usr/bin/env python3
"""
检查角色数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.character_storage_service import character_storage_service

def check_character_data():
    """检查角色数据"""
    print("🔍 检查角色数据")
    print("=" * 50)
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    try:
        # 获取项目角色
        characters = character_storage_service.get_project_characters(project_id)
        
        print(f"项目ID: {project_id}")
        print(f"角色数量: {len(characters)}")
        
        if characters:
            print("\n角色列表:")
            for i, character in enumerate(characters, 1):
                print(f"  {i}. {character.get('name', 'Unknown')}")
                print(f"     ID: {character.get('id', 'N/A')}")
                print(f"     描述: {character.get('description', 'N/A')[:50]}...")
                print(f"     创建时间: {character.get('created_at', 'N/A')}")
                print()
        else:
            print("⚠️  没有找到角色数据")
        
        # 尝试按名称查找特定角色
        character_name = "林深"
        character = character_storage_service.get_character_by_name(project_id, character_name)
        
        print(f"查找角色 '{character_name}':")
        if character:
            print("✅ 找到角色")
            print(f"  ID: {character.get('id', 'N/A')}")
            print(f"  名称: {character.get('name', 'N/A')}")
            print(f"  描述: {character.get('description', 'N/A')}")
            print(f"  外貌: {character.get('appearance', 'N/A')}")
            print(f"  性格标签: {character.get('personality_tags', [])}")
        else:
            print("❌ 未找到角色")
        
        return len(characters) > 0
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_character_service():
    """检查角色服务"""
    print("\n🔍 检查角色服务")
    print("=" * 50)
    
    try:
        from app.services.character_service import character_service
        
        project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
        
        # 获取角色列表
        characters = character_service.get_characters(project_id)
        
        print(f"角色服务返回的角色数量: {len(characters)}")
        
        if characters:
            print("角色列表:")
            for i, character in enumerate(characters, 1):
                print(f"  {i}. {character.name}")
                print(f"     ID: {character.id}")
                print(f"     角色类型: {character.role}")
                print(f"     状态: {character.status}")
                print(f"     重要性: {character.importance}")
                print()
        else:
            print("⚠️  角色服务中没有角色数据")
        
        return len(characters) > 0
        
    except Exception as e:
        print(f"❌ 检查角色服务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_routes():
    """检查路由注册"""
    print("\n🔍 检查路由注册")
    print("=" * 50)
    
    try:
        from app.main import app
        
        # 获取所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path') and hasattr(route, 'methods'):
                routes.append((route.path, list(route.methods)))
        
        # 查找角色相关路由
        character_routes = [r for r in routes if 'character' in r[0].lower()]
        
        print("角色相关路由:")
        for path, methods in character_routes:
            print(f"  {', '.join(methods)} {path}")
        
        # 检查特定路由
        target_route = "/api/v1/characters/{project_id}/{character_name}/detail"
        found = any(target_route.replace('{project_id}', '').replace('{character_name}', '') in path for path, _ in character_routes)
        
        if found:
            print(f"\n✅ 找到目标路由: {target_route}")
        else:
            print(f"\n❌ 未找到目标路由: {target_route}")
        
        return found
        
    except Exception as e:
        print(f"❌ 检查路由失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 角色数据和路由检查")
    print("=" * 60)
    
    success = True
    
    # 检查角色数据
    success &= check_character_data()
    
    # 检查角色服务
    success &= check_character_service()
    
    # 检查路由
    success &= check_routes()
    
    if success:
        print("\n✅ 检查完成，数据和路由正常。")
    else:
        print("\n❌ 检查发现问题，需要进一步调试。")
