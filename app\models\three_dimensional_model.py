#!/usr/bin/env python3
"""
三维动态模型：冲突-情绪-爽点
用于小说创作的核心框架
"""

from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json


class ConflictType(Enum):
    """冲突类型"""
    INTERNAL = "internal"  # 内心冲突
    INTERPERSONAL = "interpersonal"  # 人际冲突
    SOCIAL = "social"  # 社会冲突
    ENVIRONMENTAL = "environmental"  # 环境冲突
    IDEOLOGICAL = "ideological"  # 理念冲突


class ConflictIntensity(Enum):
    """冲突强度"""
    LOW = 1  # 低强度
    MEDIUM = 2  # 中等强度
    HIGH = 3  # 高强度
    EXTREME = 4  # 极高强度


class EmotionType(Enum):
    """情绪类型"""
    JOY = "joy"  # 喜悦
    ANGER = "anger"  # 愤怒
    SADNESS = "sadness"  # 悲伤
    FEAR = "fear"  # 恐惧
    SURPRISE = "surprise"  # 惊讶
    DISGUST = "disgust"  # 厌恶
    ANTICIPATION = "anticipation"  # 期待
    TRUST = "trust"  # 信任


class EmotionIntensity(Enum):
    """情绪强度"""
    SUBTLE = 1  # 微妙
    MODERATE = 2  # 适中
    STRONG = 3  # 强烈
    OVERWHELMING = 4  # 压倒性


class PleasureType(Enum):
    """爽点类型"""
    ACHIEVEMENT = "achievement"  # 成就感
    REVENGE = "revenge"  # 复仇
    ROMANCE = "romance"  # 浪漫
    POWER = "power"  # 权力
    DISCOVERY = "discovery"  # 发现
    JUSTICE = "justice"  # 正义
    GROWTH = "growth"  # 成长
    SURPRISE_TWIST = "surprise_twist"  # 意外转折


@dataclass
class ConflictDimension:
    """冲突维度"""
    conflict_type: ConflictType
    intensity: ConflictIntensity
    description: str
    participants: List[str] = field(default_factory=list)  # 参与者
    stakes: str = ""  # 利害关系
    resolution_potential: float = 0.5  # 解决潜力 (0-1)
    escalation_trend: float = 0.0  # 升级趋势 (-1到1)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "conflict_type": self.conflict_type.value,
            "intensity": self.intensity.value,
            "description": self.description,
            "participants": self.participants,
            "stakes": self.stakes,
            "resolution_potential": self.resolution_potential,
            "escalation_trend": self.escalation_trend
        }


@dataclass
class EmotionDimension:
    """情绪维度"""
    primary_emotion: EmotionType
    intensity: EmotionIntensity
    description: str
    target_character: str = ""  # 目标角色
    trigger_event: str = ""  # 触发事件
    emotional_arc: List[Tuple[EmotionType, float]] = field(default_factory=list)  # 情绪弧线
    resonance_score: float = 0.5  # 共鸣分数 (0-1)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "primary_emotion": self.primary_emotion.value,
            "intensity": self.intensity.value,
            "description": self.description,
            "target_character": self.target_character,
            "trigger_event": self.trigger_event,
            "emotional_arc": [(emotion.value, score) for emotion, score in self.emotional_arc],
            "resonance_score": self.resonance_score
        }


@dataclass
class PleasureDimension:
    """爽点维度"""
    pleasure_type: PleasureType
    intensity: float  # 强度 (0-1)
    description: str
    setup_quality: float = 0.5  # 铺垫质量 (0-1)
    payoff_satisfaction: float = 0.5  # 回报满足度 (0-1)
    surprise_factor: float = 0.5  # 惊喜因子 (0-1)
    timing_score: float = 0.5  # 时机分数 (0-1)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "pleasure_type": self.pleasure_type.value,
            "intensity": self.intensity,
            "description": self.description,
            "setup_quality": self.setup_quality,
            "payoff_satisfaction": self.payoff_satisfaction,
            "surprise_factor": self.surprise_factor,
            "timing_score": self.timing_score
        }


@dataclass
class ThreeDimensionalModel:
    """三维动态模型"""
    conflict: ConflictDimension
    emotion: EmotionDimension
    pleasure: PleasureDimension
    
    # 模型元数据
    timestamp: datetime = field(default_factory=datetime.now)
    chapter_id: str = ""
    scene_id: str = ""
    
    # 综合评分
    overall_tension: float = 0.0  # 整体张力 (0-1)
    narrative_momentum: float = 0.0  # 叙事动力 (0-1)
    reader_engagement: float = 0.0  # 读者参与度 (0-1)
    
    def calculate_scores(self) -> None:
        """计算综合评分"""
        # 整体张力 = 冲突强度 * 情绪强度 * 0.7 + 爽点强度 * 0.3
        conflict_score = self.conflict.intensity.value / 4.0
        emotion_score = self.emotion.intensity.value / 4.0
        pleasure_score = self.pleasure.intensity
        
        self.overall_tension = (conflict_score * emotion_score * 0.7 + pleasure_score * 0.3)
        
        # 叙事动力 = 冲突升级趋势 * 0.4 + 情绪共鸣 * 0.3 + 爽点时机 * 0.3
        self.narrative_momentum = (
            (self.conflict.escalation_trend + 1) / 2 * 0.4 +
            self.emotion.resonance_score * 0.3 +
            self.pleasure.timing_score * 0.3
        )
        
        # 读者参与度 = 综合多个因素
        self.reader_engagement = (
            self.overall_tension * 0.4 +
            self.emotion.resonance_score * 0.3 +
            self.pleasure.surprise_factor * 0.3
        )
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "conflict": self.conflict.to_dict(),
            "emotion": self.emotion.to_dict(),
            "pleasure": self.pleasure.to_dict(),
            "timestamp": self.timestamp.isoformat(),
            "chapter_id": self.chapter_id,
            "scene_id": self.scene_id,
            "overall_tension": self.overall_tension,
            "narrative_momentum": self.narrative_momentum,
            "reader_engagement": self.reader_engagement
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ThreeDimensionalModel':
        """从字典创建模型实例"""
        conflict = ConflictDimension(
            conflict_type=ConflictType(data["conflict"]["conflict_type"]),
            intensity=ConflictIntensity(data["conflict"]["intensity"]),
            description=data["conflict"]["description"],
            participants=data["conflict"].get("participants", []),
            stakes=data["conflict"].get("stakes", ""),
            resolution_potential=data["conflict"].get("resolution_potential", 0.5),
            escalation_trend=data["conflict"].get("escalation_trend", 0.0)
        )
        
        emotion = EmotionDimension(
            primary_emotion=EmotionType(data["emotion"]["primary_emotion"]),
            intensity=EmotionIntensity(data["emotion"]["intensity"]),
            description=data["emotion"]["description"],
            target_character=data["emotion"].get("target_character", ""),
            trigger_event=data["emotion"].get("trigger_event", ""),
            emotional_arc=[(EmotionType(e), s) for e, s in data["emotion"].get("emotional_arc", [])],
            resonance_score=data["emotion"].get("resonance_score", 0.5)
        )
        
        pleasure = PleasureDimension(
            pleasure_type=PleasureType(data["pleasure"]["pleasure_type"]),
            intensity=data["pleasure"]["intensity"],
            description=data["pleasure"]["description"],
            setup_quality=data["pleasure"].get("setup_quality", 0.5),
            payoff_satisfaction=data["pleasure"].get("payoff_satisfaction", 0.5),
            surprise_factor=data["pleasure"].get("surprise_factor", 0.5),
            timing_score=data["pleasure"].get("timing_score", 0.5)
        )
        
        model = cls(conflict=conflict, emotion=emotion, pleasure=pleasure)
        model.timestamp = datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat()))
        model.chapter_id = data.get("chapter_id", "")
        model.scene_id = data.get("scene_id", "")
        model.overall_tension = data.get("overall_tension", 0.0)
        model.narrative_momentum = data.get("narrative_momentum", 0.0)
        model.reader_engagement = data.get("reader_engagement", 0.0)
        
        return model


@dataclass
class ModelAnalysisResult:
    """模型分析结果"""
    model: ThreeDimensionalModel
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    next_scene_recommendations: List[Dict[str, Any]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "model": self.model.to_dict(),
            "strengths": self.strengths,
            "weaknesses": self.weaknesses,
            "suggestions": self.suggestions,
            "next_scene_recommendations": self.next_scene_recommendations
        }


class ThreeDimensionalAnalyzer:
    """三维模型分析器"""
    
    @staticmethod
    def analyze_content(content: str, context: Dict[str, Any] = None) -> ModelAnalysisResult:
        """分析内容的三维模型"""
        # 这里是分析逻辑的占位符
        # 实际实现会使用AI模型来分析内容
        
        # 示例分析结果
        conflict = ConflictDimension(
            conflict_type=ConflictType.INTERPERSONAL,
            intensity=ConflictIntensity.MEDIUM,
            description="角色间的价值观冲突"
        )
        
        emotion = EmotionDimension(
            primary_emotion=EmotionType.ANGER,
            intensity=EmotionIntensity.MODERATE,
            description="主角对不公正的愤怒"
        )
        
        pleasure = PleasureDimension(
            pleasure_type=PleasureType.JUSTICE,
            intensity=0.6,
            description="正义得到伸张的满足感"
        )
        
        model = ThreeDimensionalModel(conflict=conflict, emotion=emotion, pleasure=pleasure)
        model.calculate_scores()
        
        return ModelAnalysisResult(
            model=model,
            strengths=["冲突设置合理", "情绪表达到位"],
            weaknesses=["爽点铺垫不足"],
            suggestions=["增加更多细节描写", "强化角色动机"]
        )
    
    @staticmethod
    def generate_optimization_suggestions(model: ThreeDimensionalModel) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        if model.overall_tension < 0.5:
            suggestions.append("建议增加冲突强度或情绪张力")
        
        if model.narrative_momentum < 0.4:
            suggestions.append("建议加快情节推进节奏")
        
        if model.reader_engagement < 0.5:
            suggestions.append("建议增加更多吸引读者的元素")
        
        return suggestions
