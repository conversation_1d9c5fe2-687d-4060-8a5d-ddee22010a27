<template>
  <div class="ai-writing-assistant">
    <!-- 简化的AI工具栏 -->
    <div class="ai-toolbar">
      <div class="toolbar-left">
        <div class="ai-status-indicator">
          <div class="status-dot" :class="aiStatus"></div>
          <span class="status-text">{{ getStatusText() }}</span>
        </div>
        <h4 class="toolbar-title">
          <i class="bi bi-robot"></i>
          AI写作助手
        </h4>
      </div>

      <div class="toolbar-actions">
        <div class="action-group">
          <button
            @click="toggleQuickActions"
            class="btn btn-quick"
            :class="{ 'active': showQuickActions }"
            title="快速操作"
          >
            <i class="bi bi-lightning-charge"></i>
            <span class="btn-text">快速操作</span>
          </button>

          <button
            @click="openAIWriting"
            class="btn btn-primary"
            :disabled="aiStatus !== 'online'"
            title="AI智能创作"
          >
            <i class="bi bi-magic"></i>
            <span class="btn-text">AI创作</span>
          </button>

          <button
            @click="showContextPanel = !showContextPanel"
            class="btn btn-context"
            :class="{ 'active': showContextPanel }"
            title="项目上下文"
          >
            <i class="bi bi-diagram-3"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 快速操作面板 -->
    <div v-if="showQuickActions" class="quick-actions-panel">
      <div class="quick-actions-grid">
        <button
          v-for="action in quickActions"
          :key="action.id"
          @click="executeQuickAction(action)"
          class="quick-action-btn"
          :disabled="aiStatus !== 'online'"
        >
          <i :class="action.icon"></i>
          <span class="action-name">{{ action.name }}</span>
          <span class="action-desc">{{ action.description }}</span>
        </button>
      </div>
    </div>

    <!-- 智能建议面板 -->
    <div v-if="showSuggestions" class="suggestions-panel">
      <div class="panel-header">
        <div class="header-left">
          <i class="bi bi-lightbulb"></i>
          <h5>智能写作建议</h5>
        </div>
        <div class="header-actions">
          <button @click="loadSuggestions" class="btn-icon" title="刷新建议">
            <i class="bi bi-arrow-clockwise" :class="{ 'spinning': loadingSuggestions }"></i>
          </button>
          <button @click="showSuggestions = false" class="btn-icon" title="关闭">
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>

      <div class="panel-content">
        <div v-if="loadingSuggestions" class="loading-state">
          <div class="loading-spinner"></div>
          <p>AI正在分析项目内容，生成个性化建议...</p>
        </div>

        <div v-else-if="suggestions.length" class="suggestions-content">
          <div
            v-for="suggestion in suggestions"
            :key="suggestion.type"
            class="suggestion-card"
            :class="suggestion.priority"
          >
            <div class="suggestion-header">
              <div class="suggestion-meta">
                <i :class="getSuggestionIcon(suggestion.type)" class="suggestion-icon"></i>
                <span class="suggestion-title">{{ suggestion.title }}</span>
              </div>
              <span class="priority-badge" :class="suggestion.priority">
                {{ getPriorityText(suggestion.priority) }}
              </span>
            </div>
            <p class="suggestion-description">{{ suggestion.description }}</p>
            <p v-if="suggestion.details" class="suggestion-details">{{ suggestion.details }}</p>
            <div class="suggestion-actions">
              <button
                @click="applySuggestion(suggestion)"
                class="btn btn-apply"
              >
                <i class="bi bi-check-circle"></i>
                应用建议
              </button>
              <button
                @click="dismissSuggestion(suggestion)"
                class="btn btn-dismiss"
              >
                <i class="bi bi-x-circle"></i>
                忽略
              </button>
            </div>
          </div>
        </div>

        <div v-else class="empty-state">
          <i class="bi bi-lightbulb-off"></i>
          <p>暂无写作建议</p>
          <button @click="loadSuggestions" class="btn btn-generate">
            <i class="bi bi-plus-circle"></i>
            生成建议
          </button>
        </div>
      </div>
    </div>
    
    <!-- 上下文面板 -->
    <AIContextPanel 
      v-if="showContextPanel"
      :project-id="projectId"
      @context-updated="onContextUpdated"
    />
    
    <!-- AI创作模态框 -->
    <div v-if="showAIWriting" class="modal-overlay" @click="closeAIWriting">
      <div class="modal-content ai-writing-modal" @click.stop>
        <div class="modal-header">
          <div class="header-content">
            <div class="header-icon">
              <i class="bi bi-robot"></i>
            </div>
            <div class="header-text">
              <h3>AI智能创作</h3>
              <p>基于项目上下文生成高质量内容</p>
            </div>
          </div>
          <button class="close-btn" @click="closeAIWriting">
            <i class="bi bi-x"></i>
          </button>
        </div>

        <div class="modal-body">
          <!-- 创作模式选择 -->
          <div class="section">
            <div class="section-header">
              <h4>
                <i class="bi bi-palette"></i>
                创作模式
              </h4>
              <p class="section-desc">选择适合的创作类型</p>
            </div>
            <div class="writing-modes">
              <button
                v-for="mode in writingModes"
                :key="mode.id"
                @click="selectedMode = mode.id"
                class="mode-btn"
                :class="{ 'active': selectedMode === mode.id }"
              >
                <div class="mode-icon">
                  <i :class="mode.icon"></i>
                </div>
                <div class="mode-content">
                  <span class="mode-name">{{ mode.name }}</span>
                  <span class="mode-desc">{{ mode.description }}</span>
                </div>
              </button>
            </div>
          </div>

          <!-- 创作参数 -->
          <div class="section">
            <div class="section-header">
              <h4>
                <i class="bi bi-sliders"></i>
                创作参数
              </h4>
              <p class="section-desc">调整AI创作的风格和长度</p>
            </div>
            <div class="params-grid">
              <div class="param-group">
                <label class="param-label">
                  <i class="bi bi-text-paragraph"></i>
                  内容长度
                </label>
                <select v-model="writingParams.length" class="form-select">
                  <option value="200">简短 (200字)</option>
                  <option value="400">适中 (400字)</option>
                  <option value="600">详细 (600字)</option>
                  <option value="800">丰富 (800字)</option>
                  <option value="1200">完整场景 (1200字)</option>
                </select>
              </div>

              <div class="param-group">
                <label class="param-label">
                  <i class="bi bi-lightning"></i>
                  创意度
                  <span class="param-value">{{ getCreativityText(writingParams.creativity) }}</span>
                </label>
                <div class="range-container">
                  <input
                    v-model.number="writingParams.creativity"
                    type="range"
                    min="0.1"
                    max="1.0"
                    step="0.1"
                    class="form-range"
                  >
                  <div class="range-labels">
                    <span>保守</span>
                    <span>平衡</span>
                    <span>创新</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 创作指导 -->
          <div class="section">
            <div class="section-header">
              <h4>
                <i class="bi bi-chat-text"></i>
                创作指导
              </h4>
              <p class="section-desc">描述你希望AI创作的内容方向</p>
            </div>
            <div class="prompt-container">
              <textarea
                v-model="writingPrompt"
                class="form-textarea"
                rows="4"
                :placeholder="getPromptPlaceholder()"
              ></textarea>
              <div class="prompt-suggestions">
                <span class="suggestion-label">建议：</span>
                <button
                  v-for="suggestion in promptSuggestions"
                  :key="suggestion"
                  @click="writingPrompt = suggestion"
                  class="suggestion-tag"
                >
                  {{ suggestion }}
                </button>
              </div>
            </div>
          </div>
          
          <!-- 上下文信息预览 -->
          <div v-if="contextSummary" class="context-preview">
            <h5>📋 AI将基于以下信息创作：</h5>
            <div class="context-summary">{{ contextSummary }}</div>
            <button @click="showFullContext = !showFullContext" class="btn btn-sm btn-outline">
              {{ showFullContext ? '收起' : '查看详细' }}上下文
            </button>
            
            <div v-if="showFullContext" class="full-context">
              <div v-if="contextData.active_characters?.length" class="context-section">
                <strong>活跃角色：</strong>
                <span v-for="char in contextData.active_characters" :key="char.id" class="context-tag">
                  {{ char.name }}
                </span>
              </div>
              
              <div v-if="contextData.plot_threads?.length" class="context-section">
                <strong>当前情节：</strong>
                <span v-for="plot in contextData.plot_threads" :key="plot.title" class="context-tag">
                  {{ plot.title }}
                </span>
              </div>
              
              <div v-if="contextData.style_guide" class="context-section">
                <strong>写作风格：</strong>
                <span class="context-tag">{{ contextData.style_guide.sentence_style }}</span>
                <span class="context-tag">{{ contextData.style_guide.dialogue_preference }}</span>
              </div>
            </div>
          </div>
          
          <!-- 生成结果 -->
          <div v-if="generatedContent || generating" class="generated-result">
            <div class="result-header">
              <h5>{{ generating ? '🤖 AI正在创作...' : '✨ 生成结果' }}</h5>
              <div class="model-info" v-if="modelInfo.name">
                <small>{{ modelInfo.type }}/{{ modelInfo.name }}</small>
              </div>
              <div class="result-actions" v-if="!generating">
                <button v-if="hasThink" @click="showThink = !showThink"
                        class="btn btn-sm btn-outline">
                  {{ showThink ? '🧠 隐藏思考' : '🧠 显示思考' }}
                </button>
                <button @click="copyContent" class="btn btn-sm btn-outline">📋 复制</button>
                <button @click="insertContent" class="btn btn-sm btn-primary">✅ 插入</button>
                <button @click="regenerateContent" class="btn btn-sm btn-secondary">🔄 重新生成</button>
              </div>
            </div>

            <!-- 生成进度 -->
            <div v-if="generating" class="generation-progress">
              <div class="progress-bar">
                <div class="progress-indicator"></div>
              </div>
              <small>正在实时生成内容，请稍候...</small>
            </div>

            <!-- Think部分 -->
            <div v-if="hasThink && showThink" class="think-section">
              <div class="think-header">
                <h6>🧠 AI思考过程 {{ generating ? '(实时)' : '' }}</h6>
              </div>
              <div class="think-content" :class="{ 'streaming': generating }">
                {{ thinkContent }}
                <span v-if="generating && thinkContent" class="cursor">|</span>
              </div>
            </div>

            <!-- 生成的内容 -->
            <div class="generated-text" :class="{ 'streaming': generating }">
              {{ generatedContent || (generating ? '开始生成...' : '') }}
              <span v-if="generating" class="cursor">|</span>
            </div>
          </div>
          
          <!-- 生成状态 -->
          <div v-if="generating" class="generating-state">
            <div class="loading-spinner"></div>
            <p>AI正在基于项目上下文创作内容...</p>
            <div class="generation-tips">
              <p>💡 {{ getRandomTip() }}</p>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <button @click="closeAIWriting" class="btn btn-secondary">取消</button>
          <button 
            @click="generateContent" 
            class="btn btn-primary"
            :disabled="generating || !writingPrompt.trim()"
          >
            <span v-if="generating">生成中...</span>
            <span v-else>🚀 开始创作</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { aiWritingApi } from '@/api/ai-writing'
import { useAppStore } from '@/stores/app'
import AIContextPanel from './AIContextPanel.vue'

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  currentContent: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['content-generated', 'suggestion-applied'])

const appStore = useAppStore()

// 状态
const aiStatus = ref('online') // online, offline, loading
const showSuggestions = ref(false)
const showContextPanel = ref(false)
const showAIWriting = ref(false)
const showFullContext = ref(false)
const showQuickActions = ref(false)

// 建议相关
const loadingSuggestions = ref(false)
const suggestions = ref([])

// 上下文相关
const contextData = ref({})
const contextSummary = ref('')

// 创作相关
const generating = ref(false)
const selectedMode = ref('continue')
const writingPrompt = ref('')
const generatedContent = ref('')
const hasThink = ref(false)
const thinkContent = ref('')
const showThink = ref(false)
const modelInfo = ref({ type: '', name: '' })

const writingParams = ref({
  length: 400,
  creativity: 0.7
})

// 快速操作
const quickActions = [
  {
    id: 'continue',
    name: '智能续写',
    description: '基于当前内容继续创作',
    icon: 'bi bi-arrow-right-circle',
    prompt: '请基于当前内容自然地继续创作下去'
  },
  {
    id: 'expand',
    name: '扩展描写',
    description: '丰富场景和人物描写',
    icon: 'bi bi-zoom-in',
    prompt: '请扩展当前场景的环境描写和人物细节'
  },
  {
    id: 'dialogue',
    name: '添加对话',
    description: '为当前场景添加对话',
    icon: 'bi bi-chat-dots',
    prompt: '请为当前场景添加符合人物性格的对话'
  },
  {
    id: 'transition',
    name: '场景转换',
    description: '创作场景转换段落',
    icon: 'bi bi-arrow-left-right',
    prompt: '请创作一个自然的场景转换段落'
  }
]

const writingModes = [
  {
    id: 'continue',
    name: '续写',
    icon: 'bi bi-arrow-right-circle',
    description: '基于当前内容继续创作'
  },
  {
    id: 'scene',
    name: '场景',
    icon: 'bi bi-camera-video',
    description: '创作新的场景描写'
  },
  {
    id: 'dialogue',
    name: '对话',
    icon: 'bi bi-chat-dots',
    description: '创作角色对话内容'
  },
  {
    id: 'description',
    name: '描写',
    icon: 'bi bi-image',
    description: '创作环境或人物描写'
  },
  {
    id: 'action',
    name: '动作',
    icon: 'bi bi-lightning',
    description: '创作动作场面'
  }
]

// 提示建议
const promptSuggestions = computed(() => {
  const suggestions = {
    continue: [
      '继续当前情节发展',
      '推进主要冲突',
      '展现角色内心变化'
    ],
    scene: [
      '描写一个神秘的地方',
      '营造紧张的氛围',
      '展现宏大的场景'
    ],
    dialogue: [
      '角色之间的重要对话',
      '揭示关键信息的对话',
      '展现角色性格的对话'
    ],
    description: [
      '详细描写主角外貌',
      '描绘环境氛围',
      '刻画角色情感状态'
    ],
    action: [
      '激烈的战斗场面',
      '紧张的追逐情节',
      '关键的行动时刻'
    ]
  }
  return suggestions[selectedMode.value] || suggestions.continue
})

// 计算属性
const getStatusText = () => {
  const statusMap = {
    online: 'AI在线',
    offline: 'AI离线',
    loading: '检测中...'
  }
  return statusMap[aiStatus.value] || '未知状态'
}

// 生命周期
onMounted(() => {
  checkAIStatus()
  loadContextData()
})

// 方法
const checkAIStatus = async () => {
  // 简化的AI状态检查
  aiStatus.value = 'online' // 假设AI在线
}

const loadSuggestions = async () => {
  loadingSuggestions.value = true
  
  try {
    const response = await aiWritingApi.getSuggestions(props.projectId)
    if (response.data.success) {
      suggestions.value = response.data.suggestions.suggestions || []
    }
  } catch (error) {
    console.error('加载建议失败:', error)
    appStore.showError('加载写作建议失败')
  } finally {
    loadingSuggestions.value = false
  }
}

const loadContextData = async () => {
  try {
    const response = await aiWritingApi.getContextPanel(props.projectId)
    if (response.data.success) {
      contextData.value = response.data.panel_data
      contextSummary.value = response.data.panel_data.context_summary || ''
    }
  } catch (error) {
    console.error('加载上下文失败:', error)
  }
}

const openAIWriting = () => {
  showAIWriting.value = true
  if (!contextSummary.value) {
    loadContextData()
  }
}

const closeAIWriting = () => {
  showAIWriting.value = false
  generatedContent.value = ''
  writingPrompt.value = ''
}

const generateContent = async () => {
  if (!writingPrompt.value.trim()) {
    appStore.showError('请输入创作指导')
    return
  }

  generating.value = true
  generatedContent.value = ''
  hasThink.value = false
  thinkContent.value = ''

  try {
    const requestData = {
      prompt: buildPromptForMode(),
      context_info: contextData.value,
      max_length: writingParams.value.length,
      temperature: writingParams.value.creativity
    }

    // 使用流式生成
    await generateContentStream(requestData)

  } catch (error) {
    console.error('AI创作失败:', error)
    appStore.showError('AI创作失败: ' + (error.message || '未知错误'))
  } finally {
    generating.value = false
  }
}

const generateContentStream = async (requestData) => {
  try {
    // 使用统一AI助手API进行流式生成
    const { unifiedAIAssistantApi } = await import('@/api/unified-ai-assistant')

    // 先尝试流式API
    try {
      await unifiedAIAssistantApi.chatStream(
        props.projectId,
        {
          message: requestData.prompt,
          mode: 'generation',
          target_length: requestData.max_length,
          context: requestData.context_info
        },
        (data) => {
          // 处理流式数据
          if (data.type === 'content') {
            generatedContent.value += data.content
          } else if (data.type === 'thinking') {
            hasThink.value = true
            thinkContent.value += data.content
          }
        },
        (error) => {
          throw error
        }
      )
    } catch (streamError) {
      // 如果流式API失败，回退到普通API
      console.warn('流式API失败，使用普通API:', streamError)

      const response = await unifiedAIAssistantApi.chat(props.projectId, {
        message: requestData.prompt,
        mode: 'generation',
        target_length: requestData.max_length,
        context: requestData.context_info
      })

      const result = response.data
      if (result.success && result.generated_content) {
        generatedContent.value = result.generated_content
      } else {
        throw new Error('生成失败')
      }
    }
    // 生成完成后的处理
    appStore.showSuccess('AI创作完成！')

  } catch (error) {
    console.error('AI生成失败:', error)
    throw error
  }
}

const buildPromptForMode = () => {
  const modePrompts = {
    continue: `基于当前内容继续创作：${writingPrompt.value}`,
    scene: `创作一个场景：${writingPrompt.value}`,
    dialogue: `创作角色对话：${writingPrompt.value}`,
    description: `创作环境或角色描写：${writingPrompt.value}`,
    action: `创作动作场面：${writingPrompt.value}`
  }
  
  return modePrompts[selectedMode.value] || writingPrompt.value
}

const getPromptPlaceholder = () => {
  const placeholders = {
    continue: '描述接下来应该发生什么，或者想要的情节发展方向...',
    scene: '描述想要创作的场景，包括地点、氛围、参与的角色...',
    dialogue: '描述对话的背景、参与角色、对话目的...',
    description: '描述想要描写的对象、重点、风格...',
    action: '描述动作场面的背景、参与者、动作类型...'
  }
  
  return placeholders[selectedMode.value] || '请输入创作指导...'
}

const applySuggestion = (suggestion) => {
  writingPrompt.value = suggestion.description
  selectedMode.value = 'continue'
  openAIWriting()
  emit('suggestion-applied', suggestion)
}

const copyContent = async () => {
  try {
    await navigator.clipboard.writeText(generatedContent.value)
    appStore.showSuccess('内容已复制到剪贴板')
  } catch (error) {
    appStore.showError('复制失败')
  }
}

const insertContent = () => {
  emit('content-generated', {
    content: generatedContent.value,
    mode: selectedMode.value
  })
  closeAIWriting()
  appStore.showSuccess('内容已插入编辑器')
}

const regenerateContent = () => {
  generateContent()
}

// 新增方法
const toggleQuickActions = () => {
  showQuickActions.value = !showQuickActions.value
  if (showQuickActions.value) {
    showSuggestions.value = false
  }
}

const executeQuickAction = (action) => {
  selectedMode.value = action.id
  writingPrompt.value = action.prompt
  showQuickActions.value = false
  openAIWriting()
}

const getPriorityText = (priority) => {
  const priorityMap = {
    'high': '重要',
    'medium': '一般',
    'low': '建议'
  }
  return priorityMap[priority] || priority
}

const getCreativityText = (value) => {
  if (value <= 0.3) return '保守'
  if (value <= 0.7) return '平衡'
  return '创新'
}

const dismissSuggestion = (suggestion) => {
  const index = suggestions.value.findIndex(s => s.type === suggestion.type)
  if (index > -1) {
    suggestions.value.splice(index, 1)
  }
}

const getSuggestionIcon = (type) => {
  const iconMap = {
    'plot_development': 'bi bi-graph-up',
    'character_development': 'bi bi-person-circle',
    'worldbuilding': 'bi bi-globe',
    'story_structure': 'bi bi-book'
  }
  return iconMap[type] || 'bi bi-lightbulb'
}

const getRandomTip = () => {
  const tips = [
    'AI正在分析你的角色设定和情节发展',
    '基于项目上下文生成更符合设定的内容',
    '保持与已有内容的风格一致性',
    '考虑角色的性格特点和行为模式',
    '确保情节的逻辑连贯性'
  ]
  return tips[Math.floor(Math.random() * tips.length)]
}

const onContextUpdated = (newContext) => {
  contextData.value = newContext
}
</script>

<style scoped>
.ai-writing-assistant {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ai-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.ai-status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  font-size: 0.875rem;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-dot.online {
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
}

.status-dot.offline {
  background: #ef4444;
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.3);
}

.status-dot.loading {
  background: #f59e0b;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-weight: 500;
}

.toolbar-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.toolbar-actions {
  display: flex;
  align-items: center;
}

.action-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.btn-quick {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
}

.btn-quick:hover,
.btn-quick.active {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.btn-primary {
  background: rgba(255, 255, 255, 0.9);
  color: #6366f1;
  font-weight: 600;
}

.btn-primary:hover {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary:disabled {
  background: rgba(255, 255, 255, 0.5);
  color: rgba(99, 102, 241, 0.5);
  cursor: not-allowed;
  transform: none;
}

.btn-context {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
  padding: 0.5rem;
  width: 40px;
  height: 40px;
  justify-content: center;
}

.btn-context:hover,
.btn-context.active {
  background: rgba(255, 255, 255, 0.25);
}

.btn-text {
  display: none;
}

@media (min-width: 768px) {
  .btn-text {
    display: inline;
  }
}

/* 快速操作面板 */
.quick-actions-panel {
  border-top: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 1rem 1.5rem;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.quick-action-btn:hover {
  border-color: #6366f1;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
  transform: translateY(-2px);
}

.quick-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.quick-action-btn i {
  font-size: 1.5rem;
  color: #6366f1;
  margin-bottom: 0.5rem;
}

.action-name {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.action-desc {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.4;
}

.suggestions-panel {
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-left i {
  color: #6366f1;
  font-size: 1.1rem;
}

.panel-header h5 {
  margin: 0;
  color: #374151;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:hover {
  background: #f3f4f6;
  color: #374151;
}

.spinning {
  animation: spin 1s linear infinite;
}

.panel-content {
  max-height: 400px;
  overflow-y: auto;
}

.suggestions-content {
  padding: 1rem 1.5rem;
  display: grid;
  gap: 1rem;
}

.suggestion-card {
  padding: 1.25rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.suggestion-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.suggestion-card.high {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.suggestion-card.medium {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}

.suggestion-card.low {
  border-left: 4px solid #10b981;
  background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.suggestion-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.suggestion-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.suggestion-icon {
  color: #6366f1;
  font-size: 1.1rem;
}

.suggestion-title {
  font-weight: 600;
  color: #374151;
  font-size: 0.95rem;
}

.priority-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-weight: 500;
  flex-shrink: 0;
}

.priority-badge.high {
  background: #fee2e2;
  color: #dc2626;
}

.priority-badge.medium {
  background: #fef3c7;
  color: #d97706;
}

.priority-badge.low {
  background: #dcfce7;
  color: #16a34a;
}

.suggestion-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.suggestion-details {
  font-size: 0.8rem;
  color: #9ca3af;
  margin-bottom: 1rem;
  line-height: 1.4;
  font-style: italic;
}

.suggestion-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-apply {
  background: #6366f1;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-apply:hover {
  background: #5b21b6;
  transform: translateY(-1px);
}

.btn-dismiss {
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.btn-dismiss:hover {
  background: #e5e7eb;
  color: #374151;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #9ca3af;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #d1d5db;
}

.empty-state p {
  margin-bottom: 1rem;
  font-size: 0.875rem;
}

.btn-generate {
  background: #6366f1;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-generate:hover {
  background: #5b21b6;
  transform: translateY(-1px);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.ai-writing-modal {
  background: white;
  border-radius: 16px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.header-text h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.header-text p {
  margin: 0.25rem 0 0 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
}

.section:last-child {
  margin-bottom: 0;
}

.section-header {
  margin-bottom: 1rem;
}

.section-header h4 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-header i {
  color: #6366f1;
}

.section-desc {
  margin: 0;
  font-size: 0.875rem;
  color: #6b7280;
}

.writing-modes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.mode-btn {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.mode-btn:hover {
  border-color: #6366f1;
  background: #f8fafc;
  transform: translateY(-1px);
}

.mode-btn.active {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eef2ff 0%, #f8fafc 100%);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

.mode-icon {
  width: 40px;
  height: 40px;
  background: #f1f5f9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 1.25rem;
  color: #6366f1;
}

.mode-btn.active .mode-icon {
  background: #6366f1;
  color: white;
}

.mode-content {
  flex: 1;
}

.mode-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.mode-desc {
  font-size: 0.8rem;
  color: #6b7280;
  line-height: 1.4;
}

/* 参数设置 */
.params-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.param-group {
  display: flex;
  flex-direction: column;
}

.param-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.param-label i {
  color: #6366f1;
  margin-right: 0.5rem;
}

.param-value {
  font-size: 0.8rem;
  color: #6366f1;
  font-weight: 600;
}

.form-select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.range-container {
  display: flex;
  flex-direction: column;
}

.form-range {
  width: 100%;
  margin-bottom: 0.5rem;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #6366f1;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #6366f1;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #6b7280;
}

/* 提示输入 */
.prompt-container {
  display: flex;
  flex-direction: column;
}

.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  min-height: 100px;
  transition: all 0.2s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.prompt-suggestions {
  margin-top: 0.75rem;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.5rem;
}

.suggestion-label {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.suggestion-tag {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-tag:hover {
  background: #6366f1;
  color: white;
  border-color: #6366f1;
}

.context-preview {
  margin: 1.5rem 0;
  padding: 1rem;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.context-preview h5 {
  margin: 0 0 0.75rem 0;
  color: #1e40af;
}

.context-summary {
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.full-context {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #bfdbfe;
}

.context-section {
  margin-bottom: 0.75rem;
}

.context-tag {
  display: inline-block;
  margin: 0.25rem 0.5rem 0.25rem 0;
  padding: 0.25rem 0.5rem;
  background: #dbeafe;
  color: #1e40af;
  border-radius: 12px;
  font-size: 0.75rem;
}

.generated-result {
  margin-top: 1.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.result-header h5 {
  margin: 0;
  color: #374151;
}

.result-actions {
  display: flex;
  gap: 0.5rem;
}

.generated-text {
  padding: 1rem;
  white-space: pre-wrap;
  line-height: 1.6;
  color: #374151;
  max-height: 300px;
  overflow-y: auto;
}

.generating-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.generation-tips {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.generation-tips p {
  margin: 0;
  font-style: italic;
  color: #1e40af;
}

.loading-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.quick-actions-panel,
.suggestions-panel {
  animation: fadeIn 0.3s ease-out;
}

/* Think部分样式 */
.think-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.think-header {
  background: #e9ecef;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #dee2e6;
}

.think-header h6 {
  margin: 0;
  font-size: 0.9rem;
  color: #495057;
  font-weight: 600;
}

.think-content {
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  color: #6c757d;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  background: #ffffff;
}

.model-info {
  font-size: 0.75rem;
  color: #6c757d;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-left: auto;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.result-header h5 {
  margin: 0;
  flex: 1;
}

/* 流式生成样式 */
.generation-progress {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-indicator {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  animation: progress-flow 2s ease-in-out infinite;
}

@keyframes progress-flow {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

.generated-text.streaming {
  min-height: 60px;
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
}

.cursor {
  animation: blink 1s infinite;
  color: #007bff;
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-toolbar {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .toolbar-left {
    justify-content: space-between;
  }

  .action-group {
    justify-content: center;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .params-grid {
    grid-template-columns: 1fr;
  }

  .writing-modes {
    grid-template-columns: 1fr;
  }

  .modal-overlay {
    padding: 0.5rem;
  }

  .ai-writing-modal {
    max-height: 95vh;
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .header-content {
    gap: 0.75rem;
  }

  .header-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }

  .header-text h3 {
    font-size: 1.1rem;
  }

  .header-text p {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .suggestion-actions {
    flex-direction: column;
  }

  .btn-apply,
  .btn-dismiss {
    justify-content: center;
  }

  .prompt-suggestions {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
