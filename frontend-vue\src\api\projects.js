import { http } from './index'

export const projectApi = {
  // 仪表板
  getDashboard: () => http.get('/dashboard'),

  // 项目管理
  getProjects: () => http.get('/projects'),
  getProject: (id) => http.get(`/projects/${id}`),
  createProject: (data) => http.post('/projects', data),
  updateProject: (id, data) => http.put(`/projects/${id}`, data),
  deleteProject: (id) => http.delete(`/projects/${id}`),

  // 世界观设定
  getWorldbuilding: (projectId) => http.get(`/projects/${projectId}/worldbuilding`),
  updateWorldbuilding: (projectId, data) => http.post(`/projects/${projectId}/worldbuilding`, data),

  // 内容生成
  generateContent: (projectId, data) => http.post(`/projects/${projectId}/generate`, data),

  // 章节管理
  getChapters: (projectId) => http.get(`/projects/${projectId}/chapters`),
  getChapter: (projectId, chapterId) => http.get(`/projects/${projectId}/chapters/${chapterId}`),
  createChapter: (projectId, data) => http.post(`/projects/${projectId}/chapters`, data),
  updateChapter: (projectId, chapterId, data) => http.put(`/projects/${projectId}/chapters/${chapterId}`, data),
  deleteChapter: (projectId, chapterId) => http.delete(`/projects/${projectId}/chapters/${chapterId}`),

  // 角色管理
  getCharacters: (projectId) => http.get(`/projects/${projectId}/characters`),
  getCharacter: (projectId, characterId) => http.get(`/projects/${projectId}/characters/${characterId}`),
  createCharacter: (projectId, data) => http.post(`/projects/${projectId}/characters`, data),
  updateCharacter: (projectId, characterId, data) => http.put(`/projects/${projectId}/characters/${characterId}`, data),
  deleteCharacter: (projectId, characterId) => http.delete(`/projects/${projectId}/characters/${characterId}`),

  // 角色关系
  getCharacterRelationships: (projectId) => http.get(`/projects/${projectId}/characters/relationships`),
  createCharacterRelationship: (projectId, data) => http.post(`/projects/${projectId}/characters/relationships`, data),
  updateCharacterRelationship: (projectId, relationshipId, data) => http.put(`/projects/${projectId}/characters/relationships/${relationshipId}`, data),
  deleteCharacterRelationship: (projectId, relationshipId) => http.delete(`/projects/${projectId}/characters/relationships/${relationshipId}`),

  // 高级角色管理
  getAdvancedCharacterList: (projectId, params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    return http.get(`/characters/${projectId}${queryString ? '?' + queryString : ''}`)
  },
  getCharacterDetail: (projectId, characterName) => http.get(`/characters/${projectId}/${characterName}/detail`),
  getRelationshipNetwork: (projectId, params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    return http.get(`/characters/${projectId}/network${queryString ? '?' + queryString : ''}`)
  },
  searchCharacters: (projectId, params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    return http.get(`/characters/${projectId}/search${queryString ? '?' + queryString : ''}`)
  },
  getCharacterRelationshipDetail: (projectId, characterName) => http.get(`/characters/${projectId}/${characterName}/relationships`),

  // 时间线管理
  getTimeline: (projectId) => http.get(`/projects/${projectId}/timeline`),
  getTimelineEvent: (projectId, eventId) => http.get(`/projects/${projectId}/timeline/${eventId}`),
  createTimelineEvent: (projectId, data) => http.post(`/projects/${projectId}/timeline`, data),
  updateTimelineEvent: (projectId, eventId, data) => http.put(`/projects/${projectId}/timeline/${eventId}`, data),
  deleteTimelineEvent: (projectId, eventId) => http.delete(`/projects/${projectId}/timeline/${eventId}`),

  // AI辅助功能
  generateWorldbuilding: (projectId, data) => http.post(`/projects/${projectId}/ai/worldbuilding`, data),
  generateCharacter: (projectId, data) => http.post(`/projects/${projectId}/ai/character`, data),
  generatePlot: (projectId, data) => http.post(`/projects/${projectId}/ai/plot`, data),
  continueWriting: (projectId, data) => http.post(`/projects/${projectId}/ai/continue`, data),
  generateWorldbuildingAI: (projectId, data) => http.post(`/projects/${projectId}/worldbuilding/ai-generate`, data),

  // 搜索
  searchContent: (projectId, data) => http.post(`/projects/${projectId}/search`, data),

  // 导出功能
  exportProject: (projectId, format) => http.get(`/projects/${projectId}/export?format=${format}`, { responseType: 'blob' }),

  // 统计信息
  getProjectStats: (projectId) => http.get(`/projects/${projectId}/stats`),

  // 增强向量化功能
  vectorizeChapter: (projectId, data) => http.post(`/projects/${projectId}/enhanced-vectorization/chapters/vectorize`, data),
  vectorizeProject: (projectId, data) => http.post(`/projects/${projectId}/enhanced-vectorization/project/vectorize`, data),
  getVectorizationStatus: (projectId) => http.get(`/projects/${projectId}/enhanced-vectorization/status`),
  enhancedSearch: (projectId, data) => http.post(`/projects/${projectId}/enhanced-vectorization/search`, data),
  getSearchSuggestions: (projectId, partialQuery = '') => http.get(`/projects/${projectId}/enhanced-vectorization/search/suggestions?partial_query=${partialQuery}`),
  analyzeChapterStructure: (projectId, data) => http.post(`/projects/${projectId}/enhanced-vectorization/analyze/structure`, data),
  getProjectInsights: (projectId) => http.get(`/projects/${projectId}/enhanced-vectorization/insights`),
  getKnowledgeGraphStats: (projectId) => http.get(`/projects/${projectId}/enhanced-vectorization/knowledge-graph/stats`),
  getEntityRelationships: (projectId, entityName) => http.get(`/projects/${projectId}/enhanced-vectorization/knowledge-graph/entity/${entityName}/relationships`),
  multiLevelVectorSearch: (projectId, query, levels = '0,1,2,3', limit = 10) => http.get(`/projects/${projectId}/enhanced-vectorization/vectors/multi-level/search?query=${query}&levels=${levels}&limit=${limit}`)
}
