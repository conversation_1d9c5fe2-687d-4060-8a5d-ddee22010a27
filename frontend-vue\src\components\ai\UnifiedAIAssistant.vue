<template>
  <div class="unified-ai-assistant">
    <!-- AI助手头部 -->
    <div class="ai-header">
      <div class="header-left">
        <div class="ai-avatar">
          <i class="bi bi-robot"></i>
        </div>
        <div class="header-info">
          <h3>🤖 AI创作助手</h3>
          <div class="ai-status">
            <span class="status-dot" :class="aiStatus"></span>
            <span class="status-text">{{ getStatusText() }}</span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <button 
          @click="toggleExpanded" 
          class="btn btn-sm btn-outline-primary"
          :title="expanded ? '收起助手' : '展开助手'"
        >
          <i :class="expanded ? 'bi bi-chevron-up' : 'bi bi-chevron-down'"></i>
        </button>
      </div>
    </div>

    <!-- AI助手内容 -->
    <div v-if="expanded" class="ai-content">
      <!-- 快速操作按钮 -->
      <div class="quick-actions">
        <h5>🚀 快速操作</h5>
        <div class="action-buttons">
          <button 
            v-for="starter in conversationStarters" 
            :key="starter.category"
            @click="executeQuickAction(starter)"
            class="quick-btn"
            :class="starter.category"
            :title="starter.example"
          >
            {{ starter.icon }} {{ starter.text }}
          </button>
        </div>
      </div>

      <!-- 聊天界面 -->
      <div class="chat-container">
        <div class="chat-messages" ref="messagesContainer">
          <div 
            v-for="message in messages" 
            :key="message.id"
            class="message"
            :class="message.type"
          >
            <div class="message-avatar">
              <i :class="getMessageIcon(message.type)"></i>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
              
              <!-- 分析结果 -->
              <div v-if="message.analysis" class="message-analysis">
                <div class="analysis-header">
                  <i class="bi bi-lightbulb"></i>
                  <span>分析结果</span>
                </div>
                <div class="analysis-content">{{ message.analysis }}</div>
              </div>

              <!-- 生成内容 -->
              <div v-if="message.generated" class="message-generated">
                <div class="generated-header">
                  <i class="bi bi-magic"></i>
                  <span>生成内容</span>
                  <div class="quality-score" v-if="message.quality_score">
                    质量: {{ (message.quality_score * 100).toFixed(0) }}%
                  </div>
                </div>
                <div class="generated-content">{{ message.generated }}</div>
                <div class="generated-actions">
                  <button @click="copyContent(message.generated)" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-clipboard"></i> 复制
                  </button>
                  <button @click="insertContent(message.generated)" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle"></i> 插入
                  </button>
                </div>
              </div>

              <!-- 建议列表 -->
              <div v-if="message.suggestions && message.suggestions.length" class="message-suggestions">
                <div class="suggestions-header">
                  <i class="bi bi-lightbulb"></i>
                  <span>相关建议</span>
                </div>
                <div class="suggestions-list">
                  <button 
                    v-for="(suggestion, index) in message.suggestions.slice(0, 3)" 
                    :key="index"
                    @click="applySuggestion(suggestion)"
                    class="suggestion-btn"
                  >
                    {{ suggestion }}
                  </button>
                </div>
              </div>

              <!-- 下一步操作 -->
              <div v-if="message.next_actions && message.next_actions.length" class="message-actions">
                <div class="actions-header">
                  <i class="bi bi-arrow-right-circle"></i>
                  <span>下一步操作</span>
                </div>
                <div class="actions-list">
                  <button 
                    v-for="action in message.next_actions.slice(0, 2)" 
                    :key="action.action"
                    @click="executeAction(action)"
                    class="action-btn"
                    :title="action.description"
                  >
                    {{ action.title }}
                  </button>
                </div>
              </div>

              <div class="message-meta">
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                <span v-if="message.mode_used" class="message-mode">{{ getModeText(message.mode_used) }}</span>
                <span v-if="message.intent_detected" class="message-intent">{{ getIntentText(message.intent_detected) }}</span>
              </div>
            </div>
          </div>

          <!-- 正在输入指示器 -->
          <div v-if="isTyping" class="message assistant typing">
            <div class="message-avatar">
              <i class="bi bi-robot"></i>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input">
          <div class="input-container">
            <div class="input-wrapper">
              <textarea
                v-model="currentMessage"
                @keydown.enter.exact.prevent="sendMessage"
                @keydown.enter.shift.exact="addNewLine"
                placeholder="输入您的问题或创作需求..."
                class="message-input"
                rows="1"
                ref="messageInput"
              ></textarea>
              <div class="input-actions">
                <button 
                  @click="analyzeIntent" 
                  class="btn btn-sm btn-outline-secondary"
                  :disabled="!currentMessage.trim() || isLoading"
                  title="分析意图"
                >
                  <i class="bi bi-search"></i>
                </button>
                <button 
                  @click="sendMessage" 
                  class="btn btn-sm btn-primary"
                  :disabled="!currentMessage.trim() || isLoading"
                >
                  <i v-if="isLoading" class="bi bi-arrow-clockwise spin"></i>
                  <i v-else class="bi bi-send"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- 模式选择 -->
          <div class="mode-selector">
            <label class="mode-label">模式:</label>
            <select v-model="selectedMode" class="form-select form-select-sm">
              <option value="auto">🤖 智能模式</option>
              <option value="analysis">🔍 分析模式</option>
              <option value="generation">✍️ 生成模式</option>
              <option value="hybrid">🔄 混合模式</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useAppStore } from '@/stores/app'
import { unifiedAIAssistantApi, useAIAssistant, MessageType, AgentMode } from '@/api/unified-ai-assistant'

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  initialExpanded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['content-generated', 'suggestion-applied'])

const appStore = useAppStore()
const { assistant } = useAIAssistant(props.projectId)

// 状态
const expanded = ref(props.initialExpanded)
const messages = ref([])
const currentMessage = ref('')
const selectedMode = ref('auto')
const isLoading = ref(false)
const isTyping = ref(false)
const conversationStarters = ref([])

// 引用
const messagesContainer = ref(null)
const messageInput = ref(null)

// 计算属性
const aiStatus = computed(() => {
  return isLoading.value ? 'loading' : 'online'
})

// 方法
const getStatusText = () => {
  if (isLoading.value) return 'AI思考中...'
  return 'AI在线'
}

const toggleExpanded = () => {
  expanded.value = !expanded.value
  if (expanded.value) {
    nextTick(() => {
      messageInput.value?.focus()
    })
  }
}

const getMessageIcon = (type) => {
  const icons = {
    [MessageType.USER]: 'bi bi-person-circle',
    [MessageType.ASSISTANT]: 'bi bi-robot',
    [MessageType.SYSTEM]: 'bi bi-info-circle',
    [MessageType.ERROR]: 'bi bi-exclamation-triangle'
  }
  return icons[type] || 'bi bi-chat'
}

const formatMessage = (content) => {
  // 简单的Markdown格式化
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getModeText = (mode) => {
  const modeTexts = {
    'auto': '智能',
    'analysis': '分析',
    'generation': '生成',
    'hybrid': '混合'
  }
  return modeTexts[mode] || mode
}

const getIntentText = (intent) => {
  const intentTexts = {
    'character_query': '角色查询',
    'plot_query': '剧情查询',
    'scene_query': '场景查询',
    'content_generation': '内容生成',
    'writing_help': '写作帮助',
    'analysis_request': '分析请求'
  }
  return intentTexts[intent] || intent
}

const addMessage = (type, content, metadata = {}) => {
  const message = {
    id: Date.now() + Math.random(),
    type,
    content,
    timestamp: new Date(),
    ...metadata
  }
  messages.value.push(message)
  
  nextTick(() => {
    scrollToBottom()
  })
  
  return message
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

const sendMessage = async () => {
  if (!currentMessage.value.trim() || isLoading.value) return

  const userMessage = currentMessage.value.trim()
  currentMessage.value = ''

  // 添加用户消息
  addMessage(MessageType.USER, userMessage)

  isLoading.value = true
  isTyping.value = true

  try {
    const response = await unifiedAIAssistantApi.chat(props.projectId, {
      message: userMessage,
      mode: selectedMode.value,
      target_length: 500,
      include_suggestions: true
    })

    const result = response.data

    if (result.success) {
      // 添加AI响应
      const assistantMessage = addMessage(MessageType.ASSISTANT, result.response_text, {
        mode_used: result.mode_used,
        intent_detected: result.intent_detected,
        analysis: result.analysis_response,
        generated: result.generated_content,
        quality_score: result.quality_score,
        suggestions: result.suggestions,
        next_actions: result.next_actions
      })

      // 如果有生成内容，触发事件
      if (result.generated_content) {
        emit('content-generated', {
          content: result.generated_content,
          quality_score: result.quality_score
        })
      }
    } else {
      addMessage(MessageType.ERROR, '抱歉，AI助手暂时无法响应，请稍后重试。')
    }

  } catch (error) {
    console.error('发送消息失败:', error)
    addMessage(MessageType.ERROR, `发送失败: ${error.message}`)
    appStore.showError('AI助手响应失败')
  } finally {
    isLoading.value = false
    isTyping.value = false
  }
}

const analyzeIntent = async () => {
  if (!currentMessage.value.trim()) return

  try {
    const response = await unifiedAIAssistantApi.analyzeIntent(props.projectId, {
      message: currentMessage.value
    })

    const result = response.data
    appStore.showSuccess(`意图: ${getIntentText(result.primary_intent)} (置信度: ${(result.confidence * 100).toFixed(0)}%)`)
  } catch (error) {
    console.error('意图分析失败:', error)
    appStore.showError('意图分析失败')
  }
}

const addNewLine = () => {
  currentMessage.value += '\n'
}

const executeQuickAction = async (starter) => {
  currentMessage.value = starter.example
  await sendMessage()
}

const applySuggestion = (suggestion) => {
  currentMessage.value = suggestion
  emit('suggestion-applied', suggestion)
}

const executeAction = async (action) => {
  try {
    const response = await unifiedAIAssistantApi.executeQuickAction(props.projectId, {
      action: action.action,
      mode: selectedMode.value
    })

    if (response.data.success) {
      addMessage(MessageType.ASSISTANT, response.data.response_text, {
        generated: response.data.generated_content,
        suggestions: response.data.suggestions
      })
    }
  } catch (error) {
    console.error('执行操作失败:', error)
    appStore.showError('操作执行失败')
  }
}

const copyContent = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    appStore.showSuccess('内容已复制到剪贴板')
  } catch (error) {
    appStore.showError('复制失败')
  }
}

const insertContent = (content) => {
  emit('content-generated', {
    content,
    mode: 'insert'
  })
  appStore.showSuccess('内容已插入编辑器')
}

// 初始化
onMounted(async () => {
  try {
    const response = await unifiedAIAssistantApi.getConversationStarters(props.projectId)
    conversationStarters.value = response.data.starters || []
  } catch (error) {
    console.error('获取对话启动器失败:', error)
  }

  // 添加欢迎消息
  addMessage(MessageType.ASSISTANT, '您好！我是您的AI创作助手。我可以帮您分析角色、剧情、场景，也可以直接创作生成内容。请告诉我您需要什么帮助？', {
    suggestions: [
      '分析主角的性格特点',
      '帮我写一段对话',
      '给我一些剧情发展建议'
    ]
  })
})

// 监听展开状态
watch(expanded, (newVal) => {
  if (newVal) {
    nextTick(() => {
      messageInput.value?.focus()
    })
  }
})
</script>

<style scoped>
.unified-ai-assistant {
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.ai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ai-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.header-info h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.status-dot.loading {
  background: #f59e0b;
  animation: pulse 1.5s infinite;
}

.ai-content {
  padding: 1rem;
}

.quick-actions {
  margin-bottom: 1rem;
}

.quick-actions h5 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.quick-btn:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.chat-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.chat-messages {
  height: 400px;
  overflow-y: auto;
  padding: 1rem;
  background: #fafafa;
}

.message {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: #3b82f6;
  color: white;
}

.message.assistant .message-avatar {
  background: #10b981;
  color: white;
}

.message.error .message-avatar {
  background: #ef4444;
  color: white;
}

.message-content {
  flex: 1;
  max-width: 80%;
}

.message.user .message-content {
  text-align: right;
}

.message-text {
  background: white;
  padding: 0.75rem;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 0.5rem;
}

.message.user .message-text {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.message-analysis,
.message-generated {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 0.75rem;
  margin-top: 0.5rem;
}

.analysis-header,
.generated-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #0369a1;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.quality-score {
  margin-left: auto;
  background: #10b981;
  color: white;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.generated-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.message-suggestions,
.message-actions {
  margin-top: 0.5rem;
}

.suggestions-header,
.actions-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #7c3aed;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.suggestions-list,
.actions-list {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.suggestion-btn,
.action-btn {
  padding: 0.4rem 0.6rem;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-btn:hover,
.action-btn:hover {
  background: #e5e7eb;
}

.message-meta {
  display: flex;
  gap: 0.75rem;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.message.user .message-meta {
  justify-content: flex-end;
}

.typing-indicator {
  display: flex;
  gap: 0.25rem;
  padding: 0.75rem;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #9ca3af;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

.chat-input {
  border-top: 1px solid #e5e7eb;
  background: white;
  padding: 1rem;
}

.input-container {
  margin-bottom: 0.75rem;
}

.input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.75rem;
  resize: none;
  font-family: inherit;
  font-size: 0.9rem;
  line-height: 1.4;
  max-height: 120px;
}

.message-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-actions {
  display: flex;
  gap: 0.5rem;
}

.mode-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.mode-label {
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: 500;
}

.form-select-sm {
  font-size: 0.8rem;
  padding: 0.4rem 0.6rem;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
