<template>
  <div class="character-management-test">
    <div class="container-fluid">
      <div class="row">
        <div class="col-12">
          <h2 class="mb-4">角色管理测试页面</h2>
          
          <!-- 测试按钮 -->
          <div class="mb-4">
            <button 
              class="btn btn-primary me-2" 
              @click="loadMockData"
            >
              加载模拟数据
            </button>
            <button 
              class="btn btn-secondary me-2" 
              @click="clearData"
            >
              清空数据
            </button>
            <button 
              class="btn btn-info" 
              @click="toggleView"
            >
              切换视图: {{ viewMode }}
            </button>
          </div>

          <!-- 状态显示 -->
          <div class="alert alert-info mb-4">
            <strong>当前状态:</strong>
            <ul class="mb-0 mt-2">
              <li>视图模式: {{ viewMode }}</li>
              <li>角色数量: {{ characters.length }}</li>
              <li>选中角色: {{ selectedCharacter?.name || '无' }}</li>
              <li>API状态: {{ apiStatus }}</li>
            </ul>
          </div>

          <!-- 高级角色管理组件 -->
          <div v-if="viewMode === 'advanced'">
            <AdvancedCharacterManagement 
              :project-id="testProjectId"
              @character-selected="onCharacterSelected"
            />
          </div>

          <!-- 基础视图 -->
          <div v-else class="basic-test-view">
            <div class="row">
              <div 
                v-for="character in characters" 
                :key="character.id"
                class="col-md-4 mb-3"
              >
                <div class="card">
                  <div class="card-body">
                    <h5 class="card-title">{{ character.name }}</h5>
                    <p class="card-text">{{ character.description.substring(0, 100) }}...</p>
                    <div class="mb-2">
                      <span 
                        v-for="tag in character.personality_tags.slice(0, 3)" 
                        :key="tag"
                        class="badge bg-secondary me-1"
                      >
                        {{ tag }}
                      </span>
                    </div>
                    <small class="text-muted">
                      重要性: {{ character.importance_score.toFixed(1) }}
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="characters.length === 0" class="text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h4 class="mt-3">暂无角色数据</h4>
            <p class="text-muted">点击"加载模拟数据"按钮开始测试</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import AdvancedCharacterManagement from './AdvancedCharacterManagement.vue'
import { mockCharacterList } from '@/data/mock-character-data.js'

export default {
  name: 'CharacterManagementTest',
  components: {
    AdvancedCharacterManagement
  },
  setup() {
    const viewMode = ref('basic')
    const characters = ref([])
    const selectedCharacter = ref(null)
    const apiStatus = ref('未连接')
    const testProjectId = ref('test-project-001')

    const loadMockData = () => {
      try {
        characters.value = [...mockCharacterList]
        apiStatus.value = '模拟数据已加载'
        console.log('Mock data loaded:', characters.value)
      } catch (error) {
        console.error('Failed to load mock data:', error)
        apiStatus.value = '加载失败'
      }
    }

    const clearData = () => {
      characters.value = []
      selectedCharacter.value = null
      apiStatus.value = '数据已清空'
    }

    const toggleView = () => {
      viewMode.value = viewMode.value === 'basic' ? 'advanced' : 'basic'
    }

    const onCharacterSelected = (character) => {
      selectedCharacter.value = character
      console.log('Character selected:', character)
    }

    // 模拟API调用（用于高级组件）
    const mockApiCalls = () => {
      // 这里可以添加模拟API响应的逻辑
      window.mockCharacterApi = {
        getAdvancedCharacterList: () => Promise.resolve({ data: mockCharacterList }),
        getCharacterDetail: (projectId, characterName) => {
          // 返回模拟的角色详情
          return Promise.resolve({ 
            data: {
              basic_info: {
                name: characterName,
                description: '模拟角色详情...'
              },
              personality: { traits: ['测试', '模拟'] },
              relationships: [],
              timeline: [],
              statistics: { total_events: 0 }
            }
          })
        },
        getRelationshipNetwork: () => Promise.resolve({ 
          data: { nodes: [], edges: [], stats: {} }
        })
      }
    }

    // 初始化模拟API
    mockApiCalls()

    return {
      viewMode,
      characters,
      selectedCharacter,
      apiStatus,
      testProjectId,
      loadMockData,
      clearData,
      toggleView,
      onCharacterSelected
    }
  }
}
</script>

<style scoped>
.character-management-test {
  padding: 2rem;
  min-height: 100vh;
  background: #f8f9fa;
}

.card {
  transition: transform 0.2s;
}

.card:hover {
  transform: translateY(-2px);
}

.badge {
  font-size: 0.7rem;
}
</style>
