#!/usr/bin/env python3
"""
修复角色存储问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_memory_service import get_ai_memory_service
from app.services.character_storage_service import character_storage_service
from app.models.memory import MemoryType

def fix_character_storage():
    """修复角色存储"""
    print("🔧 修复角色存储")
    print("=" * 50)
    
    # 测试内容 - 包含明显的角色信息
    test_content = """
    林深穿梭在新东京第三区的霓虹灯海中，他的金属义眼在蓝光中闪烁。作为一名记忆修复师，
    他专门处理那些被病毒侵蚀的记忆碎片。今天的客户是一个虚拟性爱程序成瘾者，脑区已经
    严重损伤。
    
    "Zero，开始扫描，"林深通过后颈的神经接口与AI助手连接。
    
    Zero是与林深大脑皮层直接连接的AI助手，它用机械音回应："扫描开始，检测到异常协议入侵。"
    
    突然，一股未知的数据流冲击了林深的神经系统，Zero的声音戛然而止。林深意识到，
    这次的案子比想象中更加危险。
    """
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    chapter_id = "test_chapter_fix"
    
    try:
        # 获取AI记忆服务
        ai_memory_service = get_ai_memory_service()
        
        print("1. 开始向量化处理...")
        print(f"   项目ID: {project_id}")
        print(f"   章节ID: {chapter_id}")
        print(f"   内容长度: {len(test_content)} 字符")
        
        # 执行向量化，只提取角色信息
        result = ai_memory_service.process_chapter_content(
            project_id=project_id,
            chapter_id=chapter_id,
            content=test_content,
            extract_types=[MemoryType.CHARACTER]
        )
        
        print(f"\n2. 向量化结果:")
        print(f"   成功: {'是' if result.success else '否'}")
        if not result.success:
            print(f"   错误信息: {result.error_message}")
            return False
        
        print(f"   提取的角色数量: {len(result.characters)}")
        
        # 显示角色信息
        if result.characters:
            print("\n3. 提取的角色信息:")
            for i, character in enumerate(result.characters, 1):
                print(f"   角色 {i}: {character.name}")
                print(f"     身份: {character.identity}")
                print(f"     描述: {character.description}")
                print(f"     性格: {', '.join(character.personality_tags)}")
                print(f"     外貌: {character.appearance}")
                print()
        else:
            print("\n⚠️  没有提取到角色信息！")
            return False
        
        # 验证角色是否已保存
        print("4. 验证角色保存:")
        characters = character_storage_service.get_project_characters(project_id)
        print(f"   存储的角色数量: {len(characters)}")
        
        if characters:
            print("   存储的角色列表:")
            for i, character in enumerate(characters, 1):
                print(f"     {i}. {character.get('name', 'Unknown')}")
                print(f"        描述: {character.get('description', 'N/A')[:50]}...")
        
        # 测试按名称查找
        character_name = "林深"
        character = character_storage_service.get_character_by_name(project_id, character_name)
        
        if character:
            print(f"\n✅ 成功找到角色 '{character_name}'")
            print(f"   ID: {character.get('id', 'N/A')}")
            print(f"   描述: {character.get('description', 'N/A')}")
            return True
        else:
            print(f"\n❌ 未找到角色 '{character_name}'")
            return False
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_character():
    """创建测试角色"""
    print("\n🧪 创建测试角色")
    print("=" * 50)
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    # 手动创建角色数据
    character_data = {
        "project_id": project_id,
        "name": "林深",
        "description": "穿梭在新东京第三区的霓虹灯海中的记忆修复师，专门处理被病毒侵蚀的记忆碎片。",
        "appearance": "身材高大，左眼是金属义眼，后颈有神经接口装置。",
        "personality_tags": ["冷静", "专业", "谨慎"],
        "background": "新东京第三区的顶尖记忆修复师，擅长处理复杂的记忆修复案例。",
        "current_status": "正在处理一个危险的记忆修复案例",
        "goals": ["完成当前案例", "保护客户安全"],
        "abilities": ["记忆修复", "神经接口操作", "数据分析"],
        "weaknesses": ["对未知技术谨慎", "依赖AI助手"]
    }
    
    try:
        # 保存角色
        saved_character = character_storage_service.save_character_memory(character_data)
        
        if saved_character:
            print("✅ 测试角色创建成功！")
            print(f"   角色ID: {saved_character.get('id', 'N/A')}")
            print(f"   角色名称: {saved_character.get('name', 'N/A')}")
            return True
        else:
            print("❌ 测试角色创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 创建测试角色失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_character_api():
    """测试角色API"""
    print("\n🧪 测试角色API")
    print("=" * 50)
    
    import requests
    import urllib.parse
    
    base_url = "http://localhost:8000"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    character_name = "林深"
    
    # URL编码角色名称
    encoded_name = urllib.parse.quote(character_name)
    
    # 测试获取角色详情
    detail_url = f"{base_url}/api/v1/characters/{project_id}/{encoded_name}/detail"
    
    try:
        response = requests.get(detail_url, timeout=5)
        
        print(f"请求URL: {detail_url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 角色详情API测试成功！")
            print(f"   角色名称: {data.get('basic_info', {}).get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ 角色详情API测试失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️  服务器未运行，跳过API测试")
        return True
    except Exception as e:
        print(f"❌ API测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 角色存储修复工具")
    print("=" * 60)
    
    success = True
    
    # 方法1：通过向量化修复
    success &= fix_character_storage()
    
    # 方法2：手动创建测试角色（如果向量化失败）
    if not success:
        print("\n向量化方法失败，尝试手动创建...")
        success = create_test_character()
    
    # 测试API
    if success:
        test_character_api()
    
    if success:
        print("\n✅ 角色存储修复成功！")
        print("现在可以正常使用角色管理功能了。")
    else:
        print("\n❌ 角色存储修复失败，需要进一步调试。")
