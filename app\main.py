from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import os

from app.routers import analyze, memory, recommend, evaluate, edit, memory_api, project_api, compat_api
from app.routers import chapters, characters, worldbuilding, timeline, ai_models, ai_writing, ai_memory, character_timeline_api, novel_agent, enhanced_vectorization
from app.routers.characters import advanced_router
from app.api.character_management_routes import router as character_management_router

app = FastAPI(
    title="AI小说创作系统",
    description="基于AI的智能小说创作平台",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册原有路由
app.include_router(analyze.router, prefix="/analyze", tags=["分析"])
app.include_router(memory.router, prefix="/memory", tags=["记忆"])
app.include_router(recommend.router, prefix="/recommend", tags=["推荐"])
app.include_router(evaluate.router, prefix="/evaluate", tags=["评估"])
app.include_router(edit.router, prefix="/edit", tags=["编辑"])
app.include_router(memory_api.router, prefix="/memory-api", tags=["记忆API"])
app.include_router(project_api.router, prefix="/api/v1", tags=["项目管理"])
app.include_router(compat_api.router, tags=["兼容性API"])

# 注册新的功能路由
app.include_router(chapters.router, prefix="/api/v1")  # 启用章节向量化功能
app.include_router(characters.router, prefix="/api/v1")
app.include_router(advanced_router, prefix="/api/v1")  # 高级角色管理功能
app.include_router(character_management_router, prefix="/api/v1")  # 角色管理API
app.include_router(worldbuilding.router, prefix="/api/v1")
app.include_router(timeline.router, prefix="/api/v1")
app.include_router(ai_models.router, prefix="/api/v1")
app.include_router(ai_writing.router, prefix="/api/v1")
app.include_router(ai_memory.router)
app.include_router(character_timeline_api.router)
app.include_router(novel_agent.router, prefix="/api/v1")  # 小说创作智能Agent
app.include_router(enhanced_vectorization.router, prefix="/api/v1")  # 增强向量化功能

# 静态文件服务
if os.path.exists("frontend-vue/dist"):
    app.mount("/static", StaticFiles(directory="frontend-vue/dist/static"), name="static")

    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        """服务Vue.js单页应用"""
        if full_path.startswith("api/"):
            return {"error": "API endpoint not found"}

        file_path = f"frontend-vue/dist/{full_path}"
        if os.path.exists(file_path) and os.path.isfile(file_path):
            return FileResponse(file_path)
        else:
            return FileResponse("frontend-vue/dist/index.html")

@app.get("/")
async def root():
    """根路径"""
    if os.path.exists("frontend-vue/dist/index.html"):
        return FileResponse("frontend-vue/dist/index.html")
    else:
        return {
            "message": "AI小说创作系统API",
            "version": "1.0.0",
            "docs": "/docs",
            "frontend": "前端未构建，请运行 'cd frontend-vue && npm run build'"
        }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "AI小说创作系统运行正常"}

@app.get("/api/v1/dashboard")
async def get_dashboard():
    """获取仪表板数据"""
    from app.storage.memory_storage import storage

    # 统计项目数据
    projects = storage.get_all('projects')
    chapters = storage.get_all('chapters')
    characters = storage.get_all('characters')
    timeline_events = storage.get_all('timeline_events')

    # 计算总字数
    total_words = sum(ch.get('word_count', 0) for ch in chapters)

    # 最近活动（模拟）
    recent_activities = [
        {
            "id": "1",
            "type": "chapter_created",
            "message": "创建了新章节",
            "timestamp": "2024-01-15T10:30:00Z"
        },
        {
            "id": "2",
            "type": "character_updated",
            "message": "更新了角色信息",
            "timestamp": "2024-01-15T09:15:00Z"
        }
    ]

    return {
        "stats": {
            "total_projects": len(projects),
            "total_chapters": len(chapters),
            "total_characters": len(characters),
            "total_words": total_words,
            "timeline_events": len(timeline_events)
        },
        "recent_activities": recent_activities,
        "projects": projects[:5]  # 最近5个项目
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)