#!/usr/bin/env python3
"""
智能内容需求分析器
在生成前自动分析需要哪些背景信息和上下文内容
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from app.services.ai_model_service import ai_model_service
from app.services.content_retrieval_service import get_content_retrieval_service

logger = logging.getLogger(__name__)


class ContentNeedType(Enum):
    """内容需求类型"""
    CHARACTER_INFO = "character_info"  # 角色信息
    PLOT_CONTEXT = "plot_context"  # 剧情上下文
    WORLD_SETTING = "world_setting"  # 世界观设定
    PREVIOUS_EVENTS = "previous_events"  # 前置事件
    RELATIONSHIP_DYNAMICS = "relationship_dynamics"  # 关系动态
    LOCATION_DETAILS = "location_details"  # 地点详情
    EMOTIONAL_STATE = "emotional_state"  # 情绪状态
    TIMELINE_INFO = "timeline_info"  # 时间线信息


@dataclass
class ContentNeed:
    """内容需求"""
    need_type: ContentNeedType
    description: str
    priority: int  # 1-5, 5最高
    specific_items: List[str] = field(default_factory=list)  # 具体需要的项目
    search_keywords: List[str] = field(default_factory=list)  # 搜索关键词
    context_depth: str = "standard"  # minimal, standard, full
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "need_type": self.need_type.value,
            "description": self.description,
            "priority": self.priority,
            "specific_items": self.specific_items,
            "search_keywords": self.search_keywords,
            "context_depth": self.context_depth
        }


@dataclass
class AnalysisResult:
    """分析结果"""
    generation_intent: str  # 生成意图
    content_needs: List[ContentNeed] = field(default_factory=list)
    suggested_approach: str = ""  # 建议的生成方法
    estimated_complexity: int = 1  # 复杂度 1-5
    recommended_length: int = 500  # 推荐长度（字数）
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "generation_intent": self.generation_intent,
            "content_needs": [need.to_dict() for need in self.content_needs],
            "suggested_approach": self.suggested_approach,
            "estimated_complexity": self.estimated_complexity,
            "recommended_length": self.recommended_length
        }


class ContentNeedAnalyzer:
    """智能内容需求分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.content_retrieval = get_content_retrieval_service()
        
        # 预定义的分析模式
        self.analysis_patterns = {
            "next_chapter": {
                "keywords": ["下一章", "继续", "接下来", "然后"],
                "default_needs": [
                    ContentNeedType.PLOT_CONTEXT,
                    ContentNeedType.CHARACTER_INFO,
                    ContentNeedType.PREVIOUS_EVENTS
                ]
            },
            "character_development": {
                "keywords": ["角色", "人物", "性格", "发展", "成长"],
                "default_needs": [
                    ContentNeedType.CHARACTER_INFO,
                    ContentNeedType.RELATIONSHIP_DYNAMICS,
                    ContentNeedType.EMOTIONAL_STATE
                ]
            },
            "dialogue_scene": {
                "keywords": ["对话", "交流", "谈话", "说话"],
                "default_needs": [
                    ContentNeedType.CHARACTER_INFO,
                    ContentNeedType.RELATIONSHIP_DYNAMICS,
                    ContentNeedType.EMOTIONAL_STATE,
                    ContentNeedType.LOCATION_DETAILS
                ]
            },
            "action_scene": {
                "keywords": ["动作", "战斗", "冲突", "行动"],
                "default_needs": [
                    ContentNeedType.CHARACTER_INFO,
                    ContentNeedType.WORLD_SETTING,
                    ContentNeedType.LOCATION_DETAILS,
                    ContentNeedType.PREVIOUS_EVENTS
                ]
            },
            "world_building": {
                "keywords": ["世界", "设定", "背景", "环境"],
                "default_needs": [
                    ContentNeedType.WORLD_SETTING,
                    ContentNeedType.LOCATION_DETAILS,
                    ContentNeedType.TIMELINE_INFO
                ]
            }
        }
    
    async def analyze_generation_request(
        self, 
        project_id: str, 
        user_prompt: str,
        generation_context: Dict[str, Any] = None
    ) -> AnalysisResult:
        """分析生成请求，确定内容需求"""
        try:
            self.logger.info(f"分析生成请求: {user_prompt[:50]}...")
            
            # 1. 基础意图识别
            intent = self._identify_generation_intent(user_prompt)
            
            # 2. 使用AI进行深度分析
            ai_analysis = await self._ai_analyze_needs(user_prompt, intent, generation_context)
            
            # 3. 结合规则和AI分析结果
            content_needs = self._combine_analysis_results(intent, ai_analysis, user_prompt)
            
            # 4. 估算复杂度和推荐参数
            complexity = self._estimate_complexity(content_needs, user_prompt)
            length = self._recommend_length(intent, complexity)
            approach = self._suggest_approach(intent, content_needs)
            
            result = AnalysisResult(
                generation_intent=intent,
                content_needs=content_needs,
                suggested_approach=approach,
                estimated_complexity=complexity,
                recommended_length=length
            )
            
            self.logger.info(f"分析完成: 意图={intent}, 需求数={len(content_needs)}, 复杂度={complexity}")
            return result
            
        except Exception as e:
            self.logger.error(f"分析生成请求失败: {e}")
            return AnalysisResult(generation_intent="general")
    
    def _identify_generation_intent(self, prompt: str) -> str:
        """识别生成意图"""
        prompt_lower = prompt.lower()
        
        for intent, config in self.analysis_patterns.items():
            if any(keyword in prompt_lower for keyword in config["keywords"]):
                return intent
        
        # 默认意图
        return "general"
    
    async def _ai_analyze_needs(
        self, 
        prompt: str, 
        intent: str, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """使用AI分析内容需求"""
        try:
            analysis_prompt = f"""
请分析以下小说创作请求，确定需要哪些背景信息和上下文内容：

用户请求：{prompt}
识别意图：{intent}
当前上下文：{json.dumps(context or {}, ensure_ascii=False, indent=2)}

请分析并返回JSON格式的结果：
{{
    "character_needs": {{
        "required": ["需要的具体角色名称"],
        "info_types": ["基本信息", "性格特点", "当前状态", "关系网络"],
        "priority": 1-5
    }},
    "plot_needs": {{
        "required": ["需要的剧情信息"],
        "context_range": "最近几章的内容",
        "priority": 1-5
    }},
    "world_needs": {{
        "required": ["需要的世界观信息"],
        "details": ["地点", "时间", "规则", "背景"],
        "priority": 1-5
    }},
    "emotional_needs": {{
        "required": ["需要的情绪信息"],
        "characters": ["相关角色"],
        "priority": 1-5
    }},
    "generation_suggestions": {{
        "approach": "建议的生成方法",
        "focus_points": ["重点关注的方面"],
        "length_estimate": "建议字数",
        "complexity": 1-5
    }}
}}

只返回JSON，不要其他内容：
"""
            
            response = ai_model_service.generate_text(
                prompt=analysis_prompt,
                max_tokens=800,
                temperature=0.3
            )
            
            # 解析AI响应
            try:
                # 提取JSON部分
                json_start = response.find('{')
                json_end = response.rfind('}')
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end + 1]
                    return json.loads(json_str)
            except json.JSONDecodeError:
                self.logger.warning("AI分析结果JSON解析失败")
            
            return {}
            
        except Exception as e:
            self.logger.error(f"AI分析失败: {e}")
            return {}
    
    def _combine_analysis_results(
        self, 
        intent: str, 
        ai_analysis: Dict[str, Any], 
        prompt: str
    ) -> List[ContentNeed]:
        """结合规则和AI分析结果"""
        content_needs = []
        
        # 1. 基于意图的默认需求
        if intent in self.analysis_patterns:
            default_needs = self.analysis_patterns[intent]["default_needs"]
            for need_type in default_needs:
                content_needs.append(ContentNeed(
                    need_type=need_type,
                    description=f"基于{intent}意图的{need_type.value}需求",
                    priority=3
                ))
        
        # 2. 基于AI分析的具体需求
        if ai_analysis:
            # 角色需求
            if "character_needs" in ai_analysis:
                char_needs = ai_analysis["character_needs"]
                content_needs.append(ContentNeed(
                    need_type=ContentNeedType.CHARACTER_INFO,
                    description="AI分析的角色信息需求",
                    priority=char_needs.get("priority", 3),
                    specific_items=char_needs.get("required", []),
                    search_keywords=char_needs.get("required", [])
                ))
            
            # 剧情需求
            if "plot_needs" in ai_analysis:
                plot_needs = ai_analysis["plot_needs"]
                content_needs.append(ContentNeed(
                    need_type=ContentNeedType.PLOT_CONTEXT,
                    description="AI分析的剧情上下文需求",
                    priority=plot_needs.get("priority", 3),
                    specific_items=plot_needs.get("required", [])
                ))
            
            # 世界观需求
            if "world_needs" in ai_analysis:
                world_needs = ai_analysis["world_needs"]
                content_needs.append(ContentNeed(
                    need_type=ContentNeedType.WORLD_SETTING,
                    description="AI分析的世界观设定需求",
                    priority=world_needs.get("priority", 3),
                    specific_items=world_needs.get("details", [])
                ))
            
            # 情绪需求
            if "emotional_needs" in ai_analysis:
                emotion_needs = ai_analysis["emotional_needs"]
                content_needs.append(ContentNeed(
                    need_type=ContentNeedType.EMOTIONAL_STATE,
                    description="AI分析的情绪状态需求",
                    priority=emotion_needs.get("priority", 3),
                    specific_items=emotion_needs.get("characters", [])
                ))
        
        # 3. 基于提示词的特定需求
        content_needs.extend(self._extract_specific_needs_from_prompt(prompt))
        
        # 4. 去重和优化
        return self._optimize_content_needs(content_needs)
    
    def _extract_specific_needs_from_prompt(self, prompt: str) -> List[ContentNeed]:
        """从提示词中提取特定需求"""
        needs = []
        
        # 角色名称提取
        character_pattern = r'([A-Za-z\u4e00-\u9fff]+)(?:这个角色|的性格|说话|行动)'
        characters = re.findall(character_pattern, prompt)
        if characters:
            needs.append(ContentNeed(
                need_type=ContentNeedType.CHARACTER_INFO,
                description="提示词中提到的角色信息",
                priority=4,
                specific_items=list(set(characters)),
                search_keywords=list(set(characters))
            ))
        
        # 地点提取
        location_pattern = r'在([A-Za-z\u4e00-\u9fff]+)(?:地方|场所|房间|城市)'
        locations = re.findall(location_pattern, prompt)
        if locations:
            needs.append(ContentNeed(
                need_type=ContentNeedType.LOCATION_DETAILS,
                description="提示词中提到的地点信息",
                priority=3,
                specific_items=list(set(locations)),
                search_keywords=list(set(locations))
            ))
        
        return needs
    
    def _optimize_content_needs(self, needs: List[ContentNeed]) -> List[ContentNeed]:
        """优化内容需求列表"""
        # 按类型合并重复需求
        need_map = {}
        for need in needs:
            key = need.need_type.value
            if key in need_map:
                # 合并需求
                existing = need_map[key]
                existing.priority = max(existing.priority, need.priority)
                existing.specific_items.extend(need.specific_items)
                existing.search_keywords.extend(need.search_keywords)
                # 去重
                existing.specific_items = list(set(existing.specific_items))
                existing.search_keywords = list(set(existing.search_keywords))
            else:
                need_map[key] = need
        
        # 按优先级排序
        optimized_needs = list(need_map.values())
        optimized_needs.sort(key=lambda x: x.priority, reverse=True)
        
        return optimized_needs
    
    def _estimate_complexity(self, needs: List[ContentNeed], prompt: str) -> int:
        """估算生成复杂度"""
        complexity = 1
        
        # 基于需求数量
        complexity += min(len(needs), 3)
        
        # 基于需求优先级
        high_priority_count = sum(1 for need in needs if need.priority >= 4)
        complexity += high_priority_count
        
        # 基于提示词复杂度
        if len(prompt) > 100:
            complexity += 1
        if "复杂" in prompt or "详细" in prompt:
            complexity += 1
        
        return min(complexity, 5)
    
    def _recommend_length(self, intent: str, complexity: int) -> int:
        """推荐生成长度"""
        base_lengths = {
            "next_chapter": 800,
            "character_development": 600,
            "dialogue_scene": 400,
            "action_scene": 600,
            "world_building": 500,
            "general": 500
        }
        
        base_length = base_lengths.get(intent, 500)
        return base_length + (complexity - 1) * 100
    
    def _suggest_approach(self, intent: str, needs: List[ContentNeed]) -> str:
        """建议生成方法"""
        approaches = {
            "next_chapter": "基于前文情节发展，注重情节连贯性和角色发展",
            "character_development": "深入挖掘角色内心，展现性格特点和成长轨迹",
            "dialogue_scene": "突出角色个性，通过对话推进情节和展现关系",
            "action_scene": "营造紧张氛围，详细描写动作和环境",
            "world_building": "丰富世界观设定，增强代入感和真实感",
            "general": "平衡各个要素，确保内容质量和可读性"
        }
        
        base_approach = approaches.get(intent, approaches["general"])
        
        # 根据需求调整建议
        high_priority_needs = [need for need in needs if need.priority >= 4]
        if high_priority_needs:
            need_types = [need.need_type.value for need in high_priority_needs]
            base_approach += f"，特别关注：{', '.join(need_types)}"
        
        return base_approach


# 全局实例
content_need_analyzer = ContentNeedAnalyzer()


def get_content_need_analyzer() -> ContentNeedAnalyzer:
    """获取内容需求分析器实例"""
    return content_need_analyzer
