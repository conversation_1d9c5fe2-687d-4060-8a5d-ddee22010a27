#!/usr/bin/env python3
"""
调试具体的JSON问题
"""

import json

def debug_json_issue():
    """调试JSON问题"""
    print("🔍 调试JSON问题")
    print("=" * 50)
    
    # 问题JSON
    problematic_json = '''{ "characters": [ { "name": "林深", "identity": "记忆修复师", "description": "穿梭在新东京第三区的霓虹灯海中", "personality_tags": ["专业", "冷静"], "appearance": "金属义眼", "background": "专门处理被病毒侵蚀的记忆碎片", "current_status": "正在处理异常危险的案件", "goals": ["修复客户受损记忆"], "abilities": ["记忆修复技术"], "weaknesses": ["对病毒数据敏感" ]]}}'''
    
    print(f"原始JSON: {problematic_json}")
    print(f"长度: {len(problematic_json)}")
    
    # 分析问题
    print("\n分析问题:")
    print(f"左括号数量: {problematic_json.count('{')}")
    print(f"右括号数量: {problematic_json.count('}')}")
    print(f"左方括号数量: {problematic_json.count('[')}")
    print(f"右方括号数量: {problematic_json.count(']')}")
    quote_count = problematic_json.count('"')
    print(f"引号数量: {quote_count}")
    
    # 尝试解析
    try:
        data = json.loads(problematic_json)
        print("✅ JSON解析成功！")
        print(f"角色数量: {len(data.get('characters', []))}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        print(f"错误位置: 行 {e.lineno}, 列 {e.colno}")
        
        # 找到错误位置的字符
        if e.colno <= len(problematic_json):
            error_char = problematic_json[e.colno - 1] if e.colno > 0 else 'N/A'
            print(f"错误字符: '{error_char}'")
            
            # 显示错误位置周围的内容
            start = max(0, e.colno - 20)
            end = min(len(problematic_json), e.colno + 20)
            context = problematic_json[start:end]
            print(f"错误上下文: ...{context}...")
    
    # 手动修复
    print("\n尝试手动修复:")
    
    # 修复方案1：检查是否缺少右引号
    if quote_count % 2 == 1:
        print("检测到奇数个引号，可能缺少右引号")
        fixed_json = problematic_json + '"'
        print(f"修复后: {fixed_json[-50:]}")
        
        try:
            data = json.loads(fixed_json)
            print("✅ 修复方案1成功！")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ 修复方案1失败: {e}")
    
    # 修复方案2：分析具体的截断位置
    print("\n分析截断位置:")
    
    # 查找最后一个完整的字段
    import re
    
    # 查找所有的字段模式
    field_pattern = r'"(\w+)"\s*:\s*'
    fields = re.findall(field_pattern, problematic_json)
    print(f"找到的字段: {fields}")
    
    # 查找最后一个字段的值
    last_field_match = None
    for match in re.finditer(field_pattern, problematic_json):
        last_field_match = match
    
    if last_field_match:
        field_name = last_field_match.group(1)
        field_start = last_field_match.end()
        field_value = problematic_json[field_start:]
        print(f"最后一个字段: {field_name}")
        print(f"字段值: {field_value}")
        
        # 如果是数组字段且不完整
        if field_value.startswith('[') and not field_value.rstrip().endswith(']'):
            print("检测到不完整的数组")
            
            # 尝试修复数组
            field_quote_count = field_value.count('"')
            if '"' in field_value and field_quote_count % 2 == 1:
                # 缺少右引号
                fixed_json = problematic_json + '"]'
            else:
                # 只缺少右方括号
                fixed_json = problematic_json + ']'
            
            # 然后添加缺失的对象和数组闭合
            brace_count = fixed_json.count('{') - fixed_json.count('}')
            bracket_count = fixed_json.count('[') - fixed_json.count(']')
            
            fixed_json += '}' * brace_count
            fixed_json += ']' * bracket_count
            
            print(f"修复后JSON: {fixed_json}")
            
            try:
                data = json.loads(fixed_json)
                print("✅ 修复方案2成功！")
                print(f"角色数量: {len(data.get('characters', []))}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ 修复方案2失败: {e}")
    
    return False

if __name__ == "__main__":
    debug_json_issue()
