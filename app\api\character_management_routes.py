"""
角色管理API路由
提供角色列表、详细信息、关系网络等功能
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from app.services.character_service import character_service
from app.services.character_state_manager import get_character_state_manager
from app.services.character_event_tracker import get_character_event_tracker
from app.services.bond_relationship_manager import get_bond_relationship_manager
from app.services.ai_memory_service import get_ai_memory_service

router = APIRouter()

# 响应模型
class CharacterListItem(BaseModel):
    id: str
    name: str
    description: str
    personality_tags: List[str]
    appearance: str
    first_appearance_chapter: str
    total_chapters: int
    relationship_count: int
    event_count: int
    importance_score: float

class CharacterDetail(BaseModel):
    basic_info: Dict[str, Any]
    personality: Dict[str, Any]
    relationships: List[Dict[str, Any]]
    timeline: List[Dict[str, Any]]
    development_analysis: Dict[str, Any]
    statistics: Dict[str, Any]

class RelationshipNetwork(BaseModel):
    nodes: List[Dict[str, Any]]
    edges: List[Dict[str, Any]]
    stats: Dict[str, Any]


@router.get("/characters/{project_id}", response_model=List[CharacterListItem])
async def get_character_list(project_id: str, 
                           sort_by: str = Query("importance", description="排序方式: name, importance, chapters, relationships"),
                           filter_by: str = Query("all", description="过滤条件: all, main, supporting, minor")):
    """获取项目角色列表"""
    try:
        # 获取项目所有角色
        characters = character_service.get_characters_by_project(project_id)
        
        if not characters:
            return []
        
        character_list = []
        state_manager = get_character_state_manager()
        event_tracker = get_character_event_tracker()
        bond_manager = get_bond_relationship_manager()
        
        for character in characters:
            # 获取角色状态信息
            character_state = await state_manager._get_character_state(project_id, character.name)
            
            # 获取角色事件数量
            timeline = await event_tracker.get_character_timeline(project_id, character.name)
            event_count = len(timeline.events) if timeline else 0
            
            # 获取角色关系数量
            bonds = await bond_manager.get_character_bonds(project_id, character.name)
            relationship_count = len(bonds)
            
            # 计算重要性分数
            importance_score = await _calculate_character_importance(
                character, character_state, event_count, relationship_count
            )
            
            # 获取出现章节数
            total_chapters = len(character.chapters) if hasattr(character, 'chapters') else 1
            
            character_item = CharacterListItem(
                id=character.id,
                name=character.name,
                description=character.description[:100] + "..." if len(character.description) > 100 else character.description,
                personality_tags=character.personality_tags[:5],  # 只显示前5个标签
                appearance=character.appearance[:50] + "..." if len(character.appearance) > 50 else character.appearance,
                first_appearance_chapter=getattr(character, 'first_chapter', 'unknown'),
                total_chapters=total_chapters,
                relationship_count=relationship_count,
                event_count=event_count,
                importance_score=importance_score
            )
            
            character_list.append(character_item)
        
        # 应用过滤
        if filter_by != "all":
            character_list = _filter_characters(character_list, filter_by)
        
        # 应用排序
        character_list = _sort_characters(character_list, sort_by)
        
        return character_list
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色列表失败: {str(e)}")


@router.get("/characters/{project_id}/{character_name}/detail", response_model=CharacterDetail)
async def get_character_detail(project_id: str, character_name: str):
    """获取角色详细信息"""
    try:
        # 获取基础角色信息
        character = character_service.get_character_by_name(project_id, character_name)
        if not character:
            raise HTTPException(status_code=404, detail="角色不存在")
        
        state_manager = get_character_state_manager()
        event_tracker = get_character_event_tracker()
        bond_manager = get_bond_relationship_manager()
        
        # 1. 基础信息
        character_state = await state_manager._get_character_state(project_id, character_name)
        basic_info = {
            "id": character.id,
            "name": character.name,
            "description": character.description,
            "appearance": character.appearance,
            "current_version": character_state.current_version if character_state else 1,
            "first_appearance": getattr(character, 'first_chapter', 'unknown'),
            "latest_chapter": character_state.latest_chapter_id if character_state else 'unknown',
            "created_at": character.created_at.isoformat() if hasattr(character, 'created_at') else None,
            "last_updated": character_state.last_updated.isoformat() if character_state else None
        }
        
        # 2. 性格信息
        personality = {
            "traits": character.personality_tags,
            "trait_evolution": await _get_personality_evolution(character_state) if character_state else [],
            "personality_analysis": await _analyze_personality_development(character_state) if character_state else {}
        }
        
        # 3. 关系信息
        bonds = await bond_manager.get_character_bonds(project_id, character_name)
        relationships = []
        for bond in bonds:
            other_char = bond.character_b if bond.character_a == character_name else bond.character_a
            relationships.append({
                "character": other_char,
                "bond_type": bond.bond_type.value,
                "bond_type_display": _get_bond_type_display(bond.bond_type.value),
                "strength": bond.strength,
                "intensity": bond.get_intensity_level().value,
                "description": bond.description,
                "origin_chapter": bond.origin_chapter,
                "current_chapter": bond.current_chapter,
                "is_positive": bond.is_positive_bond(),
                "is_negative": bond.is_negative_bond(),
                "evolution_count": len(bond.evolution_history)
            })
        
        # 4. 时间线信息
        timeline = await event_tracker.get_character_timeline(project_id, character_name)
        timeline_events = []
        if timeline:
            for event in timeline.events[-20:]:  # 最近20个事件
                timeline_events.append({
                    "chapter_id": event.chapter_id,
                    "event_type": event.event_type.value,
                    "event_type_display": _get_event_type_display(event.event_type.value),
                    "description": event.event_description,
                    "importance": event.importance.value,
                    "involved_characters": event.involved_characters,
                    "location": event.location,
                    "emotional_impact": event.emotional_impact,
                    "timestamp": event.timestamp.isoformat()
                })
        
        # 5. 发展分析
        development_analysis = await event_tracker.analyze_character_development(project_id, character_name)
        
        # 6. 统计信息
        statistics = {
            "total_events": len(timeline.events) if timeline else 0,
            "total_relationships": len(relationships),
            "positive_relationships": len([r for r in relationships if r["is_positive"]]),
            "negative_relationships": len([r for r in relationships if r["is_negative"]]),
            "version_count": character_state.current_version if character_state else 1,
            "change_count": len(character_state.accumulated_changes) if character_state else 0,
            "chapters_appeared": len(set(event.chapter_id for event in timeline.events)) if timeline else 1,
            "importance_score": await _calculate_character_importance(character, character_state, 
                                                                   len(timeline.events) if timeline else 0, 
                                                                   len(relationships))
        }
        
        return CharacterDetail(
            basic_info=basic_info,
            personality=personality,
            relationships=relationships,
            timeline=timeline_events,
            development_analysis=development_analysis,
            statistics=statistics
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色详细信息失败: {str(e)}")


@router.get("/characters/{project_id}/network", response_model=RelationshipNetwork)
async def get_relationship_network(project_id: str, 
                                 include_weak: bool = Query(False, description="是否包含弱关系"),
                                 min_strength: float = Query(0.3, description="最小关系强度")):
    """获取角色关系网络"""
    try:
        bond_manager = get_bond_relationship_manager()
        
        # 获取关系网络
        network = await bond_manager.get_relationship_network(project_id)
        
        if not network:
            return RelationshipNetwork(nodes=[], edges=[], stats={})
        
        # 构建节点
        nodes = []
        for character in network['characters']:
            # 获取角色基础信息
            char_obj = character_service.get_character_by_name(project_id, character)
            if char_obj:
                nodes.append({
                    "id": character,
                    "name": character,
                    "description": char_obj.description[:50] + "..." if len(char_obj.description) > 50 else char_obj.description,
                    "personality_tags": char_obj.personality_tags[:3],  # 前3个标签
                    "node_size": await _calculate_node_size(project_id, character),
                    "node_color": await _calculate_node_color(project_id, character)
                })
        
        # 构建边（关系）
        edges = []
        for bond in network['bonds']:
            # 过滤弱关系
            if not include_weak and bond['strength'] < min_strength:
                continue
                
            edges.append({
                "source": bond['source'],
                "target": bond['target'],
                "bond_type": bond['bond_type'],
                "bond_type_display": _get_bond_type_display(bond['bond_type']),
                "strength": bond['strength'],
                "intensity": bond['intensity'],
                "description": bond['description'],
                "is_positive": bond['is_positive'],
                "is_negative": bond['is_negative'],
                "edge_color": _get_edge_color(bond['bond_type'], bond['is_positive'], bond['is_negative']),
                "edge_width": max(1, bond['strength'] * 5)  # 根据强度调整线条粗细
            })
        
        # 网络统计
        stats = {
            "total_characters": len(nodes),
            "total_relationships": len(edges),
            "positive_relationships": len([e for e in edges if e["is_positive"]]),
            "negative_relationships": len([e for e in edges if e["is_negative"]]),
            "average_strength": sum(e["strength"] for e in edges) / len(edges) if edges else 0,
            "network_density": len(edges) / (len(nodes) * (len(nodes) - 1) / 2) if len(nodes) > 1 else 0
        }
        
        return RelationshipNetwork(
            nodes=nodes,
            edges=edges,
            stats=stats
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取关系网络失败: {str(e)}")


@router.get("/characters/{project_id}/{character_name}/relationships")
async def get_character_relationships(project_id: str, character_name: str):
    """获取角色的详细关系信息"""
    try:
        bond_manager = get_bond_relationship_manager()
        
        # 获取角色关系分析
        analysis = await bond_manager.analyze_character_relationships(project_id, character_name)
        
        return analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取角色关系失败: {str(e)}")


@router.get("/characters/{project_id}/search")
async def search_characters(project_id: str, 
                          query: str = Query(..., description="搜索关键词"),
                          search_type: str = Query("all", description="搜索类型: name, description, personality, all")):
    """搜索角色"""
    try:
        characters = character_service.get_characters_by_project(project_id)
        
        if not characters:
            return []
        
        results = []
        query_lower = query.lower()
        
        for character in characters:
            match_score = 0
            match_reasons = []
            
            # 名称匹配
            if search_type in ["name", "all"] and query_lower in character.name.lower():
                match_score += 10
                match_reasons.append("名称匹配")
            
            # 描述匹配
            if search_type in ["description", "all"] and query_lower in character.description.lower():
                match_score += 5
                match_reasons.append("描述匹配")
            
            # 性格标签匹配
            if search_type in ["personality", "all"]:
                for tag in character.personality_tags:
                    if query_lower in tag.lower():
                        match_score += 3
                        match_reasons.append(f"性格标签匹配: {tag}")
            
            if match_score > 0:
                results.append({
                    "character": {
                        "id": character.id,
                        "name": character.name,
                        "description": character.description[:100] + "..." if len(character.description) > 100 else character.description,
                        "personality_tags": character.personality_tags
                    },
                    "match_score": match_score,
                    "match_reasons": match_reasons
                })
        
        # 按匹配分数排序
        results.sort(key=lambda x: x["match_score"], reverse=True)
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索角色失败: {str(e)}")


# 辅助函数
async def _calculate_character_importance(character, character_state, event_count, relationship_count):
    """计算角色重要性分数"""
    try:
        score = 0.0
        
        # 基础分数
        score += 1.0
        
        # 关系数量影响
        score += relationship_count * 0.5
        
        # 事件数量影响
        score += event_count * 0.2
        
        # 版本数量影响（变化越多越重要）
        if character_state:
            score += character_state.current_version * 0.3
        
        # 描述长度影响
        score += min(2.0, len(character.description) / 100)
        
        # 性格标签数量影响
        score += len(character.personality_tags) * 0.1
        
        return min(10.0, score)  # 最高10分
        
    except Exception:
        return 1.0


def _filter_characters(character_list: List[CharacterListItem], filter_by: str) -> List[CharacterListItem]:
    """过滤角色列表"""
    if filter_by == "main":
        return [c for c in character_list if c.importance_score >= 7.0]
    elif filter_by == "supporting":
        return [c for c in character_list if 3.0 <= c.importance_score < 7.0]
    elif filter_by == "minor":
        return [c for c in character_list if c.importance_score < 3.0]
    else:
        return character_list


def _sort_characters(character_list: List[CharacterListItem], sort_by: str) -> List[CharacterListItem]:
    """排序角色列表"""
    if sort_by == "name":
        return sorted(character_list, key=lambda x: x.name)
    elif sort_by == "importance":
        return sorted(character_list, key=lambda x: x.importance_score, reverse=True)
    elif sort_by == "chapters":
        return sorted(character_list, key=lambda x: x.total_chapters, reverse=True)
    elif sort_by == "relationships":
        return sorted(character_list, key=lambda x: x.relationship_count, reverse=True)
    else:
        return character_list


async def _get_personality_evolution(character_state) -> List[Dict[str, Any]]:
    """获取性格演变历史"""
    try:
        evolution = []

        if not character_state or not character_state.accumulated_changes:
            return evolution

        personality_changes = [
            change for change in character_state.accumulated_changes
            if change.change_type.value == "PERSONALITY_CHANGED"
        ]

        for change in personality_changes:
            evolution.append({
                "chapter_id": change.chapter_id,
                "description": change.change_description,
                "old_traits": change.old_value if isinstance(change.old_value, list) else [],
                "new_traits": change.new_value if isinstance(change.new_value, list) else [],
                "timestamp": change.timestamp.isoformat()
            })

        return evolution

    except Exception:
        return []


async def _analyze_personality_development(character_state) -> Dict[str, Any]:
    """分析性格发展"""
    try:
        if not character_state:
            return {}

        analysis = {
            "total_changes": len(character_state.accumulated_changes),
            "personality_changes": len([
                c for c in character_state.accumulated_changes
                if c.change_type.value == "PERSONALITY_CHANGED"
            ]),
            "development_trend": "stable",  # stable, growing, declining
            "key_turning_points": []
        }

        # 分析发展趋势
        if analysis["personality_changes"] > 3:
            analysis["development_trend"] = "dynamic"
        elif analysis["personality_changes"] > 1:
            analysis["development_trend"] = "evolving"

        return analysis

    except Exception:
        return {}


def _get_bond_type_display(bond_type: str) -> str:
    """获取羁绊类型的显示名称"""
    display_map = {
        "love": "爱情",
        "friendship": "友情",
        "family": "亲情",
        "mentor": "师徒",
        "loyalty": "忠诚",
        "trust": "信任",
        "admiration": "敬佩",
        "protection": "保护",
        "gratitude": "感激",
        "hatred": "仇恨",
        "rivalry": "竞争",
        "jealousy": "嫉妒",
        "betrayal": "背叛",
        "fear": "恐惧",
        "distrust": "不信任",
        "contempt": "蔑视",
        "revenge": "复仇",
        "misunderstanding": "误会",
        "complicated": "复杂关系",
        "forbidden": "禁忌关系",
        "unrequited": "单相思",
        "conflicted": "矛盾关系",
        "dependency": "依赖关系",
        "manipulation": "操控关系",
        "alliance": "联盟",
        "cooperation": "合作",
        "acquaintance": "熟人",
        "stranger": "陌生人"
    }
    return display_map.get(bond_type, bond_type)


def _get_event_type_display(event_type: str) -> str:
    """获取事件类型的显示名称"""
    display_map = {
        "APPEARANCE": "登场",
        "DIALOGUE": "对话",
        "ACTION": "行动",
        "EMOTION": "情感",
        "CONFLICT": "冲突",
        "DISCOVERY": "发现",
        "DECISION": "决定",
        "RELATIONSHIP": "关系",
        "GROWTH": "成长",
        "SETBACK": "挫折",
        "ACHIEVEMENT": "成就",
        "REVELATION": "揭示",
        "TRANSFORMATION": "转变",
        "SACRIFICE": "牺牲",
        "BETRAYAL": "背叛",
        "REUNION": "重逢",
        "DEPARTURE": "离别",
        "DEATH": "死亡",
        "BIRTH": "诞生",
        "MARRIAGE": "结婚",
        "REVENGE": "复仇",
        "FORGIVENESS": "宽恕",
        "ALLIANCE": "结盟",
        "COMPETITION": "竞争",
        "COOPERATION": "合作"
    }
    return display_map.get(event_type, event_type)


async def _calculate_node_size(project_id: str, character_name: str) -> int:
    """计算节点大小（基于重要性）"""
    try:
        character = character_service.get_character_by_name(project_id, character_name)
        if not character:
            return 20

        state_manager = get_character_state_manager()
        event_tracker = get_character_event_tracker()
        bond_manager = get_bond_relationship_manager()

        character_state = await state_manager._get_character_state(project_id, character_name)
        timeline = await event_tracker.get_character_timeline(project_id, character_name)
        bonds = await bond_manager.get_character_bonds(project_id, character_name)

        importance = await _calculate_character_importance(
            character, character_state,
            len(timeline.events) if timeline else 0,
            len(bonds)
        )

        # 将重要性分数映射到节点大小 (20-80)
        return int(20 + (importance / 10) * 60)

    except Exception:
        return 30


async def _calculate_node_color(project_id: str, character_name: str) -> str:
    """计算节点颜色（基于角色类型）"""
    try:
        character = character_service.get_character_by_name(project_id, character_name)
        if not character:
            return "#999999"

        # 基于重要性分数确定颜色
        state_manager = get_character_state_manager()
        event_tracker = get_character_event_tracker()
        bond_manager = get_bond_relationship_manager()

        character_state = await state_manager._get_character_state(project_id, character_name)
        timeline = await event_tracker.get_character_timeline(project_id, character_name)
        bonds = await bond_manager.get_character_bonds(project_id, character_name)

        importance = await _calculate_character_importance(
            character, character_state,
            len(timeline.events) if timeline else 0,
            len(bonds)
        )

        if importance >= 7.0:
            return "#FF6B6B"  # 主角 - 红色
        elif importance >= 4.0:
            return "#4ECDC4"  # 重要配角 - 青色
        elif importance >= 2.0:
            return "#45B7D1"  # 一般配角 - 蓝色
        else:
            return "#96CEB4"  # 次要角色 - 绿色

    except Exception:
        return "#999999"


def _get_edge_color(bond_type: str, is_positive: bool, is_negative: bool) -> str:
    """获取边的颜色"""
    if is_positive:
        return "#4CAF50"  # 绿色 - 正面关系
    elif is_negative:
        return "#F44336"  # 红色 - 负面关系
    else:
        return "#FF9800"  # 橙色 - 复杂关系
