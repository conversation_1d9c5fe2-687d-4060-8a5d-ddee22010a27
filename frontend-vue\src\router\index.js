import { createRouter, createWebHistory } from 'vue-router'
import { useAppStore } from '@/stores/app'

// 路由组件懒加载
const Dashboard = () => import('@/views/Dashboard.vue')
const Projects = () => import('@/views/Projects.vue')
const ProjectDetail = () => import('@/views/ProjectDetail.vue')
const Worldbuilding = () => import('@/views/Worldbuilding.vue')
const Writing = () => import('@/views/Writing.vue')
const Characters = () => import('@/views/Characters.vue')
const Timeline = () => import('@/views/Timeline.vue')
const NovelAgent = () => import('@/views/NovelAgent.vue')
const EnhancedVectorization = () => import('@/views/EnhancedVectorization.vue')
const AIAssistantView = () => import('@/views/AIAssistantView.vue')
const Models = () => import('@/views/Models.vue')
const Settings = () => import('@/views/Settings.vue')

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      icon: 'bi-speedometer2'
    }
  },
  {
    path: '/projects',
    name: 'Projects',
    component: Projects,
    meta: {
      title: '项目管理',
      icon: 'bi-folder'
    }
  },
  {
    path: '/projects/:id',
    name: 'ProjectDetail',
    component: ProjectDetail,
    meta: {
      title: '项目详情',
      icon: 'bi-folder-open'
    }
  },
  {
    path: '/projects/:id/worldbuilding',
    name: 'Worldbuilding',
    component: Worldbuilding,
    meta: {
      title: '世界观设定',
      icon: 'bi-globe'
    }
  },
  {
    path: '/projects/:id/writing',
    name: 'Writing',
    component: Writing,
    meta: {
      title: '内容创作',
      icon: 'bi-pencil-square'
    }
  },
  {
    path: '/projects/:id/characters',
    name: 'Characters',
    component: Characters,
    meta: {
      title: '角色管理',
      icon: 'bi-people'
    }
  },
  {
    path: '/projects/:id/timeline',
    name: 'Timeline',
    component: Timeline,
    meta: {
      title: '情节时间线',
      icon: 'bi-clock-history'
    }
  },
  {
    path: '/projects/:projectId/agent',
    name: 'NovelAgent',
    component: NovelAgent,
    meta: {
      title: 'AI创作助手',
      icon: 'bi-robot'
    }
  },
  {
    path: '/projects/:id/vectorization',
    name: 'EnhancedVectorization',
    component: EnhancedVectorization,
    meta: {
      title: '增强向量化',
      icon: 'bi-diagram-3'
    }
  },
  {
    path: '/projects/:projectId/ai-assistant',
    name: 'AIAssistant',
    component: AIAssistantView,
    meta: {
      title: 'AI创作助手',
      icon: 'bi-robot'
    }
  },
  {
    path: '/models',
    name: 'Models',
    component: Models,
    meta: {
      title: 'AI模型管理',
      icon: 'bi-cpu'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      icon: 'bi-gear'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const appStore = useAppStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - AI小说创作系统`
  }
  
  // 设置加载状态
  appStore.setLoading(true)
  
  next()
})

router.afterEach(() => {
  const appStore = useAppStore()
  
  // 清除加载状态
  setTimeout(() => {
    appStore.setLoading(false)
  }, 300)
})

export default router
