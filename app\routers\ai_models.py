"""
AI模型管理API路由
"""

from typing import List
from fastapi import APIRouter, HTTPException, status

from app.models.ai_model import (
    AIModelCreate, AIModelUpdate, AIModelResponse, AIModelListItem,
    AIModelTestRequest, AIModelTestResponse, AIGenerationRequest, AIGenerationResponse
)
from app.services.ai_model_service import ai_model_service

router = APIRouter(prefix="/ai-models", tags=["ai-models"])


@router.post("", response_model=AIModelResponse, status_code=status.HTTP_201_CREATED)
async def create_ai_model(model_data: AIModelCreate):
    """创建AI模型"""
    try:
        return ai_model_service.create_ai_model(model_data)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("", response_model=List[AIModelListItem])
async def get_ai_models():
    """获取所有AI模型"""
    try:
        return ai_model_service.get_ai_models()
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/ollama", response_model=List[AIModelListItem])
async def get_ollama_models():
    """获取Ollama模型列表"""
    try:
        return ai_model_service.get_ollama_models()
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/huggingface", response_model=List[AIModelListItem])
async def get_huggingface_models():
    """获取Hugging Face模型列表"""
    try:
        return ai_model_service.get_huggingface_models()
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/{model_id}", response_model=AIModelResponse)
async def get_ai_model(model_id: str):
    """获取AI模型详情"""
    try:
        model = ai_model_service.get_ai_model(model_id)
        if not model:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="AI模型不存在")
        return model
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.put("/{model_id}", response_model=AIModelResponse)
async def update_ai_model(model_id: str, model_data: AIModelUpdate):
    """更新AI模型"""
    try:
        updated_model = ai_model_service.update_ai_model(model_id, model_data)
        if not updated_model:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="AI模型不存在")
        return updated_model
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.delete("/{model_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_ai_model(model_id: str):
    """删除AI模型"""
    try:
        success = ai_model_service.delete_ai_model(model_id)
        if not success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="AI模型不存在")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/{model_id}/test", response_model=AIModelTestResponse)
async def test_ai_model(model_id: str, test_request: AIModelTestRequest):
    """测试AI模型"""
    try:
        return ai_model_service.test_ai_model(model_id, test_request)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/refresh", status_code=status.HTTP_200_OK)
async def refresh_ai_models():
    """刷新AI模型列表"""
    try:
        ai_model_service.refresh_models()
        return {"message": "AI模型列表刷新成功"}
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/stats", response_model=dict)
async def get_ai_model_stats():
    """获取AI模型统计信息"""
    try:
        return ai_model_service.get_ai_model_stats()
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/remote-servers", response_model=List[dict])
async def get_remote_servers():
    """获取远程服务器列表"""
    try:
        return ai_model_service.remote_ollama_servers
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/remote-models", response_model=List[AIModelListItem])
async def get_remote_models():
    """获取远程服务器模型列表"""
    try:
        return ai_model_service.get_remote_ollama_models()
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/test-remote-server")
async def test_remote_server(server_config: dict):
    """测试远程服务器连接"""
    try:
        import requests

        base_url = server_config.get('base_url')
        if not base_url:
            raise ValueError("缺少base_url参数")

        # 测试连接
        response = requests.get(f"{base_url}/api/version", timeout=10)

        if response.status_code == 200:
            version_info = response.json()

            # 获取模型列表
            models_response = requests.get(f"{base_url}/api/tags", timeout=10)
            models_count = 0
            if models_response.status_code == 200:
                models_data = models_response.json()
                models_count = len(models_data.get('models', []))

            return {
                "success": True,
                "message": "连接成功",
                "version": version_info.get('version', 'unknown'),
                "models_count": models_count
            }
        else:
            return {
                "success": False,
                "message": f"连接失败: HTTP {response.status_code}"
            }

    except Exception as e:
        return {
            "success": False,
            "message": f"连接失败: {str(e)}"
        }


# AI生成相关API
@router.post("/generate", response_model=AIGenerationResponse)
async def generate_content(generation_request: AIGenerationRequest):
    """AI内容生成"""
    try:
        return ai_model_service.generate_content(generation_request)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/projects/{project_id}/generate", response_model=AIGenerationResponse)
async def generate_project_content(project_id: str, generation_request: AIGenerationRequest):
    """为项目生成AI内容"""
    try:
        return ai_model_service.generate_project_content(project_id, generation_request)
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
