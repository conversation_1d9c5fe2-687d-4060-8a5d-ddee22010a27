"""
角色数据格式转换器
解决向量化提取的Character数据与数据库模型不匹配的问题
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from dataclasses import asdict

from app.services.text_structure_analyzer import Character
from app.models.character_new import CharacterRole, CharacterStatus, CharacterImportance

logger = logging.getLogger(__name__)


class CharacterDataConverter:
    """角色数据格式转换器"""
    
    def __init__(self):
        self.logger = logger
    
    def convert_extracted_character_to_db_format(self, character: Character, project_id: str, chapter_id: str) -> Dict[str, Any]:
        """
        将向量化提取的Character转换为数据库格式
        
        Args:
            character: 向量化提取的角色对象
            project_id: 项目ID
            chapter_id: 章节ID
            
        Returns:
            数据库格式的角色数据
        """
        try:
            # 基础字段映射
            db_character = {
                'project_id': project_id,
                'name': character.name,
                'description': character.description or "",
                'appearance': character.appearance or "",
                'background': character.background or "",
                'age': self._parse_age(character.age),
                'gender': character.gender,
                'occupation': character.occupation,
                'location': None,  # 向量化数据中没有location字段
                
                # 角色类型转换
                'role': self._convert_importance_to_role(character.importance),
                'status': CharacterStatus.ALIVE.value,  # 默认为活着
                'importance': self._convert_importance_to_enum(character.importance),
                
                # 复杂字段处理
                'personality': character.personality_traits or [],
                'traits': self._merge_traits(character),
                'relationships': character.relationships or [],
                'goals': [],  # 向量化数据中没有goals字段
                'conflicts': [],  # 向量化数据中没有conflicts字段
                'arc': "",  # 向量化数据中没有arc字段
                'notes': "",  # 向量化数据中没有notes字段
                
                # 元数据
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'source': 'llm_extraction',
                'first_appearance': character.first_appearance or chapter_id,
                'appearances': [chapter_id],
                
                # 向量化特有字段
                'aliases': character.aliases or [],
                'abilities': character.abilities or [],
                'specialties': character.specialties or [],
                'skills': character.skills or [],
            }
            
            return db_character
            
        except Exception as e:
            self.logger.error(f"转换角色数据失败 {character.name}: {e}")
            # 返回最小化的数据结构
            return self._create_minimal_character_data(character, project_id, chapter_id)
    
    def _parse_age(self, age_str: Optional[str]) -> Optional[int]:
        """解析年龄字符串为整数"""
        if not age_str:
            return None
        
        try:
            # 尝试直接转换
            return int(age_str)
        except ValueError:
            # 尝试从字符串中提取数字
            import re
            numbers = re.findall(r'\d+', age_str)
            if numbers:
                return int(numbers[0])
            return None
    
    def _convert_importance_to_role(self, importance: str) -> str:
        """将重要性转换为角色类型"""
        importance_lower = importance.lower() if importance else "minor"
        
        if importance_lower in ["main", "protagonist", "critical"]:
            return CharacterRole.PROTAGONIST.value
        elif importance_lower in ["secondary", "important", "supporting"]:
            return CharacterRole.SUPPORTING.value
        elif importance_lower in ["antagonist", "villain", "enemy"]:
            return CharacterRole.ANTAGONIST.value
        else:
            return CharacterRole.MINOR.value
    
    def _convert_importance_to_enum(self, importance: str) -> str:
        """将重要性转换为枚举值"""
        importance_lower = importance.lower() if importance else "minor"
        
        if importance_lower in ["main", "protagonist", "critical"]:
            return CharacterImportance.CRITICAL.value
        elif importance_lower in ["secondary", "important"]:
            return CharacterImportance.IMPORTANT.value
        elif importance_lower in ["supporting", "normal"]:
            return CharacterImportance.NORMAL.value
        else:
            return CharacterImportance.MINOR.value
    
    def _merge_traits(self, character: Character) -> List[str]:
        """合并所有特征信息"""
        traits = []
        
        # 添加性格特征
        if character.personality_traits:
            traits.extend(character.personality_traits)
        
        # 添加能力
        if character.abilities:
            traits.extend([f"能力:{ability}" for ability in character.abilities])
        
        # 添加特长
        if character.specialties:
            traits.extend([f"特长:{specialty}" for specialty in character.specialties])
        
        # 添加技能
        if character.skills:
            traits.extend([f"技能:{skill}" for skill in character.skills])
        
        return list(set(traits))  # 去重
    
    def _create_minimal_character_data(self, character: Character, project_id: str, chapter_id: str) -> Dict[str, Any]:
        """创建最小化的角色数据"""
        return {
            'project_id': project_id,
            'name': character.name,
            'description': character.description or "",
            'appearance': character.appearance or "",
            'role': CharacterRole.MINOR.value,
            'status': CharacterStatus.ALIVE.value,
            'importance': CharacterImportance.MINOR.value,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'source': 'llm_extraction',
            'appearances': [chapter_id],
        }
    
    def convert_db_character_to_advanced_format(self, db_character: Dict[str, Any]) -> Dict[str, Any]:
        """
        将数据库格式的角色转换为高级角色管理格式
        
        Args:
            db_character: 数据库格式的角色数据
            
        Returns:
            高级角色管理格式的数据
        """
        try:
            # 计算重要性分数
            importance_score = self._calculate_importance_score(db_character)
            
            # 提取性格标签
            personality_tags = self._extract_personality_tags(db_character)
            
            advanced_character = {
                'id': db_character.get('id', ''),
                'name': db_character.get('name', ''),
                'description': self._truncate_text(db_character.get('description', ''), 100),
                'personality_tags': personality_tags[:5],  # 最多5个标签
                'appearance': self._truncate_text(db_character.get('appearance', ''), 50),
                'first_appearance_chapter': db_character.get('first_appearance', 'unknown'),
                'total_chapters': len(db_character.get('appearances', [])),
                'relationship_count': len(db_character.get('relationships', [])),
                'event_count': 0,  # 暂时设为0，后续可以从事件系统获取
                'importance_score': importance_score
            }
            
            return advanced_character
            
        except Exception as e:
            self.logger.error(f"转换高级格式失败: {e}")
            return self._create_minimal_advanced_character(db_character)
    
    def _calculate_importance_score(self, db_character: Dict[str, Any]) -> float:
        """计算角色重要性分数"""
        try:
            score = 1.0
            
            # 基于重要性等级
            importance = db_character.get('importance', 'minor')
            if importance == 'critical':
                score += 6.0
            elif importance == 'important':
                score += 4.0
            elif importance == 'normal':
                score += 2.0
            
            # 基于角色类型
            role = db_character.get('role', 'minor')
            if role == 'protagonist':
                score += 3.0
            elif role == 'antagonist':
                score += 2.5
            elif role == 'supporting':
                score += 1.5
            
            # 基于描述长度
            description = db_character.get('description', '')
            score += min(1.0, len(description) / 200)
            
            # 基于出场次数
            appearances = db_character.get('appearances', [])
            score += min(1.0, len(appearances) * 0.2)
            
            # 基于关系数量
            relationships = db_character.get('relationships', [])
            score += min(1.0, len(relationships) * 0.3)
            
            return min(10.0, score)
            
        except Exception:
            return 1.0
    
    def _extract_personality_tags(self, db_character: Dict[str, Any]) -> List[str]:
        """提取性格标签"""
        tags = []
        
        # 从personality字段提取
        personality = db_character.get('personality', [])
        if isinstance(personality, list):
            tags.extend(personality)
        
        # 从traits字段提取
        traits = db_character.get('traits', [])
        if isinstance(traits, list):
            # 过滤出性格相关的特征
            personality_traits = [
                trait for trait in traits 
                if not any(prefix in trait for prefix in ['能力:', '特长:', '技能:'])
            ]
            tags.extend(personality_traits)
        
        return list(set(tags))[:10]  # 去重并限制数量
    
    def _truncate_text(self, text: str, max_length: int) -> str:
        """截断文本"""
        if not text:
            return ""
        
        if len(text) <= max_length:
            return text
        
        return text[:max_length-3] + "..."
    
    def _create_minimal_advanced_character(self, db_character: Dict[str, Any]) -> Dict[str, Any]:
        """创建最小化的高级角色数据"""
        return {
            'id': db_character.get('id', ''),
            'name': db_character.get('name', 'Unknown'),
            'description': db_character.get('description', '')[:100],
            'personality_tags': [],
            'appearance': db_character.get('appearance', '')[:50],
            'first_appearance_chapter': 'unknown',
            'total_chapters': 1,
            'relationship_count': 0,
            'event_count': 0,
            'importance_score': 1.0
        }


# 全局实例
character_data_converter = CharacterDataConverter()


def get_character_data_converter() -> CharacterDataConverter:
    """获取角色数据转换器实例"""
    return character_data_converter
