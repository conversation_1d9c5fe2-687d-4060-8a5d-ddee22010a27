#!/usr/bin/env python3
"""
字符级别的JSON分析
"""

import json

def char_level_analysis():
    """字符级别分析JSON"""
    print("🔍 字符级别JSON分析")
    print("=" * 50)
    
    # 修复后的JSON
    fixed_json = '''{ "characters": [ { "name": "林深", "identity": "记忆修复师", "description": "穿梭在新东京第三区的霓虹灯海中", "personality_tags": ["专业", "冷静"], "appearance": "金属义眼", "background": "专门处理被病毒侵蚀的记忆碎片", "current_status": "正在处理异常危险的案件", "goals": ["修复客户受损记忆"], "abilities": ["记忆修复技术"], "weaknesses": ["对病毒数据敏感"]}}'''
    
    print(f"JSON长度: {len(fixed_json)}")
    print()
    
    # 查看第285个字符周围的内容
    error_pos = 284  # 0-based index
    start = max(0, error_pos - 20)
    end = min(len(fixed_json), error_pos + 20)
    
    print(f"错误位置 {error_pos} 周围的内容:")
    context = fixed_json[start:end]
    print(f"'{context}'")
    
    if error_pos < len(fixed_json):
        error_char = fixed_json[error_pos]
        print(f"错误位置的字符: '{error_char}' (ASCII: {ord(error_char)})")
    
    print()
    
    # 分析JSON结构
    print("分析JSON结构:")
    
    # 查找所有的对象和数组
    brace_stack = []
    bracket_stack = []
    
    for i, char in enumerate(fixed_json):
        if char == '{':
            brace_stack.append(i)
        elif char == '}':
            if brace_stack:
                start_pos = brace_stack.pop()
                if i == error_pos or abs(i - error_pos) < 5:
                    print(f"对象闭合位置 {i}: 从 {start_pos} 到 {i}")
        elif char == '[':
            bracket_stack.append(i)
        elif char == ']':
            if bracket_stack:
                start_pos = bracket_stack.pop()
                if i == error_pos or abs(i - error_pos) < 5:
                    print(f"数组闭合位置 {i}: 从 {start_pos} 到 {i}")
    
    # 手动构建正确的JSON
    print("\n手动构建正确的JSON:")
    
    correct_json = {
        "characters": [
            {
                "name": "林深",
                "identity": "记忆修复师",
                "description": "穿梭在新东京第三区的霓虹灯海中",
                "personality_tags": ["专业", "冷静"],
                "appearance": "金属义眼",
                "background": "专门处理被病毒侵蚀的记忆碎片",
                "current_status": "正在处理异常危险的案件",
                "goals": ["修复客户受损记忆"],
                "abilities": ["记忆修复技术"],
                "weaknesses": ["对病毒数据敏感"]
            }
        ]
    }
    
    correct_json_str = json.dumps(correct_json, ensure_ascii=False, separators=(',', ': '))
    print(f"正确的JSON: {correct_json_str}")
    
    # 比较差异
    print(f"\n比较差异:")
    print(f"原始长度: {len(fixed_json)}")
    print(f"正确长度: {len(correct_json_str)}")
    
    # 找到第一个不同的字符
    min_len = min(len(fixed_json), len(correct_json_str))
    for i in range(min_len):
        if fixed_json[i] != correct_json_str[i]:
            print(f"第一个差异位置: {i}")
            print(f"原始: '{fixed_json[i]}' (ASCII: {ord(fixed_json[i])})")
            print(f"正确: '{correct_json_str[i]}' (ASCII: {ord(correct_json_str[i])})")
            
            # 显示差异上下文
            context_start = max(0, i - 10)
            context_end = min(min_len, i + 10)
            print(f"原始上下文: '{fixed_json[context_start:context_end]}'")
            print(f"正确上下文: '{correct_json_str[context_start:context_end]}'")
            break
    
    # 测试正确的JSON
    try:
        data = json.loads(correct_json_str)
        print(f"\n✅ 正确JSON解析成功！")
        print(f"角色数量: {len(data.get('characters', []))}")
    except Exception as e:
        print(f"❌ 正确JSON解析失败: {e}")

def find_exact_problem():
    """找到确切的问题"""
    print("\n🔍 找到确切问题")
    print("=" * 50)
    
    # 逐步构建JSON，找到问题位置
    parts = [
        '{ "characters": [',
        ' { "name": "林深",',
        ' "identity": "记忆修复师",',
        ' "description": "穿梭在新东京第三区的霓虹灯海中",',
        ' "personality_tags": ["专业", "冷静"],',
        ' "appearance": "金属义眼",',
        ' "background": "专门处理被病毒侵蚀的记忆碎片",',
        ' "current_status": "正在处理异常危险的案件",',
        ' "goals": ["修复客户受损记忆"],',
        ' "abilities": ["记忆修复技术"],',
        ' "weaknesses": ["对病毒数据敏感"]',
        ' }',
        ' ]',
        '}'
    ]
    
    cumulative_json = ""
    for i, part in enumerate(parts):
        cumulative_json += part
        print(f"步骤 {i+1}: {part}")
        
        try:
            json.loads(cumulative_json)
            print(f"  ✅ 步骤 {i+1} 解析成功")
        except json.JSONDecodeError as e:
            print(f"  ❌ 步骤 {i+1} 解析失败: {e}")
            print(f"  当前JSON: {cumulative_json}")
            if i < len(parts) - 1:
                print(f"  继续下一步...")
            else:
                print(f"  这是最后一步，JSON应该完整")

if __name__ == "__main__":
    char_level_analysis()
    find_exact_problem()
