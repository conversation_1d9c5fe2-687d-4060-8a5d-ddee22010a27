{"name": "novel-assistant-frontend", "version": "1.0.0", "description": "AI小说创作系统前端 - Vue.js版本", "type": "module", "scripts": {"dev": "vite", "dev:quiet": "SASS_SILENCE_DEPRECATIONS=true vite", "start": "node start-simple.js", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.0", "@popperjs/core": "^2.11.8", "marked": "^9.1.6", "highlight.js": "^11.9.0", "d3": "^7.8.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "sass": "^1.70.0"}, "keywords": ["vue", "novel", "ai", "writing", "assistant"], "author": "Novel Assistant Team", "license": "MIT"}