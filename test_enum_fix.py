#!/usr/bin/env python3
"""
测试枚举修复的脚本
"""

def test_bond_type_enum():
    """测试BondType枚举是否正确定义"""
    try:
        from app.models.character_bond import BondType, CharacterBond
        
        print("🧪 测试BondType枚举")
        print("=" * 50)
        
        # 测试所有枚举值
        print("1. 测试枚举值定义...")
        all_bond_types = [
            BondType.LOVE, BondType.FRIENDSHIP, BondType.FAMILY,
            BondType.MENTOR, BondType.LOYALTY, BondType.TRUST,
            BondType.ADMIRATION, BondType.PROTECTION, BondType.GRATITUDE,
            BondType.RESPECT,  # 新添加的枚举值
            BondType.HATRED, BondType.RIVALRY, BondType.JEALOUSY,
            BondType.BETRAYAL, BondType.FEAR, BondType.DISTRUST,
            BondType.CONTEMPT, BondType.REVENGE, BondType.MISUNDERSTANDING,
            BondType.COMPLICATED, BondType.FORBIDDEN, BondType.UNREQUITED,
            BondType.CONFLICTED, BondType.DEPENDENCY, BondType.MANIPULATION,
            BondType.ALLIANCE, BondType.COOPERATION, BondType.ACQUAINTANCE,
            BondType.STRANGER
        ]
        
        print(f"   定义的枚举值数量: {len(all_bond_types)}")
        print(f"   RESPECT枚举值: {BondType.RESPECT}")
        print(f"   RESPECT枚举值字符串: {BondType.RESPECT.value}")
        
        # 测试CharacterBond创建
        print("\n2. 测试CharacterBond创建...")
        bond = CharacterBond(
            project_id="test_project",
            character_a="角色A",
            character_b="角色B",
            bond_type=BondType.RESPECT,
            strength=0.7,
            description="相互尊重的关系"
        )
        print(f"   创建成功: {bond.character_a} -> {bond.character_b}")
        print(f"   羁绊类型: {bond.bond_type.value}")
        print(f"   是否为正面羁绊: {bond.is_positive_bond()}")
        
        # 测试转换概率
        print("\n3. 测试羁绊转换概率...")
        from app.models.character_bond import BondTypeCompatibility
        
        # 测试从RIVALRY到RESPECT的转换
        prob = BondTypeCompatibility.get_transition_probability(BondType.RIVALRY, BondType.RESPECT)
        print(f"   RIVALRY -> RESPECT 转换概率: {prob}")
        
        # 测试从HATRED到RESPECT的转换
        prob = BondTypeCompatibility.get_transition_probability(BondType.HATRED, BondType.RESPECT)
        print(f"   HATRED -> RESPECT 转换概率: {prob}")
        
        print("\n✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bond_relationship_manager():
    """测试羁绊关系管理器"""
    try:
        print("\n🧪 测试羁绊关系管理器")
        print("=" * 50)
        
        from app.services.bond_relationship_manager import BondRelationshipManager
        from app.models.character_bond import BondType
        
        manager = BondRelationshipManager()
        
        # 测试演变推理
        reasoning = manager._get_evolution_reasoning(BondType.RIVALRY, BondType.RESPECT)
        print(f"   RIVALRY -> RESPECT 演变推理: {reasoning}")
        
        print("✅ 羁绊关系管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 羁绊关系管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 枚举修复验证测试")
    print("=" * 60)
    
    success = True
    success &= test_bond_type_enum()
    success &= test_bond_relationship_manager()
    
    if success:
        print("\n🎉 所有测试通过！枚举修复成功。")
    else:
        print("\n💥 部分测试失败，请检查修复。")
