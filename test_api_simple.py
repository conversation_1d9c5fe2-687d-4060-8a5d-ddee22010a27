#!/usr/bin/env python3
"""
简单的API测试
"""

import requests
import urllib.parse
import json

def test_character_api():
    """测试角色API"""
    print("🧪 测试角色API")
    print("=" * 50)
    
    base_url = "http://localhost:12088"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    # 测试角色列表
    print("1. 测试角色列表API:")
    list_url = f"{base_url}/api/v1/characters/{project_id}/advanced"
    
    try:
        response = requests.get(list_url, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   角色数量: {len(data)}")
            
            if data:
                print("   角色列表:")
                for i, char in enumerate(data, 1):
                    name = char.get('name', 'Unknown')
                    print(f"     {i}. {name}")
                
                # 测试第一个角色的详情
                first_char_name = data[0].get('name', '')
                if first_char_name:
                    print(f"\n2. 测试角色详情API (角色: {first_char_name}):")
                    encoded_name = urllib.parse.quote(first_char_name)
                    detail_url = f"{base_url}/api/v1/characters/{project_id}/{encoded_name}/detail"
                    
                    detail_response = requests.get(detail_url, timeout=5)
                    print(f"   状态码: {detail_response.status_code}")
                    
                    if detail_response.status_code == 200:
                        detail_data = detail_response.json()
                        print("   ✅ 角色详情获取成功!")
                        print(f"   角色名称: {detail_data.get('basic_info', {}).get('name', 'N/A')}")
                        print(f"   角色描述: {detail_data.get('basic_info', {}).get('description', 'N/A')[:100]}...")
                        return True
                    else:
                        print(f"   ❌ 角色详情获取失败: {detail_response.text}")
                        return False
            else:
                print("   ⚠️  没有角色数据")
                return False
        else:
            print(f"   ❌ 角色列表获取失败: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败：服务器未运行")
        return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_character_update():
    """测试角色更新"""
    print("\n🧪 测试角色更新API")
    print("=" * 50)
    
    base_url = "http://localhost:12088"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    # 先获取角色列表
    list_url = f"{base_url}/api/v1/characters/{project_id}/advanced"
    
    try:
        response = requests.get(list_url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            
            if data:
                first_char_name = data[0].get('name', '')
                print(f"测试更新角色: {first_char_name}")
                
                encoded_name = urllib.parse.quote(first_char_name)
                update_url = f"{base_url}/api/v1/characters/{project_id}/{encoded_name}/detail"
                
                update_data = {
                    "description": "这是一个更新后的角色描述，用于测试角色更新功能。",
                    "personality_tags": ["测试标签1", "测试标签2", "更新后的性格"],
                    "current_status": "测试状态：角色信息已更新"
                }
                
                update_response = requests.put(
                    update_url,
                    json=update_data,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
                
                print(f"更新状态码: {update_response.status_code}")
                
                if update_response.status_code == 200:
                    result = update_response.json()
                    print("✅ 角色更新成功!")
                    print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    return True
                else:
                    print(f"❌ 角色更新失败: {update_response.text}")
                    return False
            else:
                print("⚠️  没有角色可以更新")
                return False
        else:
            print(f"❌ 获取角色列表失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 更新测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 角色管理API简单测试")
    print("=" * 60)
    
    success = True
    
    # 测试角色详情
    success &= test_character_api()
    
    # 测试角色更新
    success &= test_character_update()
    
    if success:
        print("\n✅ 所有测试通过！角色管理功能正常。")
    else:
        print("\n❌ 部分测试失败，请检查服务器状态。")
