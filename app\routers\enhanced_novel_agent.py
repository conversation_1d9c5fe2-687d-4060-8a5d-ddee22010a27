#!/usr/bin/env python3
"""
增强小说创作Agent API路由
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from app.services.enhanced_novel_agent_service import (
    get_enhanced_novel_agent_service, GenerationRequest, GenerationMode,
    ConflictType, EmotionType, PleasureType
)

router = APIRouter(prefix="/projects/{project_id}/enhanced-agent", tags=["增强小说创作Agent"])


class EnhancedGenerationRequest(BaseModel):
    """增强生成请求"""
    user_prompt: str
    generation_mode: str = GenerationMode.NEXT_CHAPTER.value
    target_length: int = 800
    chapter_id: Optional[str] = None
    
    # 三维模型目标
    target_conflict_type: Optional[str] = None
    target_emotion_type: Optional[str] = None
    target_pleasure_type: Optional[str] = None
    
    # 生成偏好
    focus_characters: List[str] = []
    environment_emphasis: bool = True
    dialogue_heavy: bool = False
    action_heavy: bool = False


class EnhancedGenerationResponse(BaseModel):
    """增强生成响应"""
    success: bool
    content: str = ""
    word_count: int = 0
    
    # 质量评估
    quality_score: float = 0.0
    model_analysis: Optional[Dict[str, Any]] = None
    
    # 生成信息
    generation_time: float = 0.0
    segments_generated: int = 0
    session_id: Optional[str] = None
    
    # 改进建议
    suggestions: List[str] = []
    strengths: List[str] = []
    weaknesses: List[str] = []


class SegmentedGenerationRequest(BaseModel):
    """分段生成请求"""
    user_prompt: str
    target_length: int = 800
    chapter_id: Optional[str] = None


class SegmentedGenerationResponse(BaseModel):
    """分段生成响应"""
    session_id: str
    plan: Dict[str, Any]
    status: str


class SegmentGenerationResponse(BaseModel):
    """段落生成响应"""
    success: bool
    segment: Optional[Dict[str, Any]] = None
    session_status: Dict[str, Any] = {}
    complete: bool = False


@router.post("/generate", response_model=EnhancedGenerationResponse)
async def generate_enhanced_content(project_id: str, request: EnhancedGenerationRequest):
    """生成增强小说内容"""
    try:
        agent_service = get_enhanced_novel_agent_service()
        
        # 转换请求
        generation_request = GenerationRequest(
            project_id=project_id,
            user_prompt=request.user_prompt,
            generation_mode=GenerationMode(request.generation_mode),
            target_length=request.target_length,
            chapter_id=request.chapter_id,
            focus_characters=request.focus_characters,
            environment_emphasis=request.environment_emphasis,
            dialogue_heavy=request.dialogue_heavy,
            action_heavy=request.action_heavy
        )
        
        # 设置三维模型目标
        if request.target_conflict_type:
            generation_request.target_conflict_type = ConflictType(request.target_conflict_type)
        if request.target_emotion_type:
            generation_request.target_emotion_type = EmotionType(request.target_emotion_type)
        if request.target_pleasure_type:
            generation_request.target_pleasure_type = PleasureType(request.target_pleasure_type)
        
        # 生成内容
        result = await agent_service.generate_content(generation_request)
        
        return EnhancedGenerationResponse(
            success=result.success,
            content=result.content,
            word_count=result.word_count,
            quality_score=result.quality_score,
            model_analysis=result.model_analysis.to_dict() if result.model_analysis else None,
            generation_time=result.generation_time,
            segments_generated=result.segments_generated,
            session_id=result.session_id,
            suggestions=result.suggestions,
            strengths=result.strengths,
            weaknesses=result.weaknesses
        )
        
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/segmented/start", response_model=SegmentedGenerationResponse)
async def start_segmented_generation(project_id: str, request: SegmentedGenerationRequest):
    """开始分段生成"""
    try:
        from app.services.segmented_generation_service import get_segmented_generation_service
        
        segmented_service = get_segmented_generation_service()
        
        session = await segmented_service.start_generation_session(
            project_id=project_id,
            user_prompt=request.user_prompt,
            target_length=request.target_length,
            chapter_id=request.chapter_id
        )
        
        return SegmentedGenerationResponse(
            session_id=session.session_id,
            plan=session.plan.to_dict() if session.plan else {},
            status="started"
        )
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/segmented/{session_id}/next", response_model=SegmentGenerationResponse)
async def generate_next_segment(project_id: str, session_id: str):
    """生成下一个段落"""
    try:
        from app.services.segmented_generation_service import get_segmented_generation_service
        
        segmented_service = get_segmented_generation_service()
        
        segment = await segmented_service.generate_next_segment(session_id)
        session_status = segmented_service.get_session_status(session_id)
        
        return SegmentGenerationResponse(
            success=segment is not None,
            segment=segment.to_dict() if segment else None,
            session_status=session_status,
            complete=not session_status.get("is_active", False)
        )
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/segmented/{session_id}/status")
async def get_segmented_generation_status(project_id: str, session_id: str):
    """获取分段生成状态"""
    try:
        from app.services.segmented_generation_service import get_segmented_generation_service
        
        segmented_service = get_segmented_generation_service()
        status = segmented_service.get_session_status(session_id)
        
        return status
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/segmented/{session_id}/content")
async def get_segmented_generation_content(project_id: str, session_id: str):
    """获取分段生成的完整内容"""
    try:
        from app.services.segmented_generation_service import get_segmented_generation_service
        
        segmented_service = get_segmented_generation_service()
        content = segmented_service.get_generated_content(session_id)
        
        return {"content": content}
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/segmented/{session_id}/complete")
async def complete_segmented_generation(project_id: str, session_id: str):
    """完成分段生成"""
    try:
        from app.services.segmented_generation_service import get_segmented_generation_service
        
        segmented_service = get_segmented_generation_service()
        result = segmented_service.end_session(session_id)
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/suggestions")
async def get_generation_suggestions(project_id: str):
    """获取生成建议"""
    try:
        agent_service = get_enhanced_novel_agent_service()
        suggestions = agent_service.get_generation_suggestions(project_id)
        
        return {"suggestions": suggestions}
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/models/conflict-types")
async def get_conflict_types():
    """获取冲突类型"""
    return {
        "conflict_types": [
            {"value": conflict.value, "name": conflict.name} 
            for conflict in ConflictType
        ]
    }


@router.get("/models/emotion-types")
async def get_emotion_types():
    """获取情绪类型"""
    return {
        "emotion_types": [
            {"value": emotion.value, "name": emotion.name} 
            for emotion in EmotionType
        ]
    }


@router.get("/models/pleasure-types")
async def get_pleasure_types():
    """获取爽点类型"""
    return {
        "pleasure_types": [
            {"value": pleasure.value, "name": pleasure.name} 
            for pleasure in PleasureType
        ]
    }


@router.get("/models/generation-modes")
async def get_generation_modes():
    """获取生成模式"""
    return {
        "generation_modes": [
            {"value": mode.value, "name": mode.name} 
            for mode in GenerationMode
        ]
    }


@router.post("/character-portrayal")
async def generate_character_portrayal(project_id: str, request: Dict[str, Any]):
    """生成角色刻画"""
    try:
        from app.services.character_portrayal_service import get_character_portrayal_service, PortrayalType
        
        portrayal_service = get_character_portrayal_service()
        
        character_info = request.get("character_info", {})
        portrayal_type = PortrayalType(request.get("portrayal_type", "dialogue"))
        context = request.get("context", {})
        
        portrayal = portrayal_service.generate_character_portrayal(
            character_info, portrayal_type, context
        )
        
        return {"portrayal": portrayal}
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/environment-description")
async def generate_environment_description(project_id: str, request: Dict[str, Any]):
    """生成环境描写"""
    try:
        from app.services.environment_description_service import (
            get_environment_description_service, EnvironmentContext, 
            AtmosphereType, EnvironmentType, SensoryType
        )
        
        env_service = get_environment_description_service()
        
        # 构建环境上下文
        context_data = request.get("context", {})
        context = EnvironmentContext(
            location_name=context_data.get("location_name", ""),
            time_of_day=context_data.get("time_of_day", ""),
            weather=context_data.get("weather", ""),
            season=context_data.get("season", ""),
            character_mood=context_data.get("character_mood", ""),
            story_phase=context_data.get("story_phase", "")
        )
        
        atmosphere = AtmosphereType(request.get("atmosphere", "peaceful"))
        env_type = EnvironmentType(request.get("environment_type", "outdoor"))
        focus_senses = [SensoryType(s) for s in request.get("focus_senses", ["visual", "auditory"])]
        length = request.get("length", 200)
        
        description = env_service.generate_environment_description(
            context, atmosphere, env_type, focus_senses, length
        )
        
        return {"description": description}
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
