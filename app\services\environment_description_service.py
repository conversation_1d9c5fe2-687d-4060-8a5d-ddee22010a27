#!/usr/bin/env python3
"""
环境描写服务
开发环境描写生成模块，结合世界观设定生成生动的场景描述
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class EnvironmentType(Enum):
    """环境类型"""
    INDOOR = "indoor"  # 室内
    OUTDOOR = "outdoor"  # 户外
    URBAN = "urban"  # 城市
    RURAL = "rural"  # 乡村
    NATURAL = "natural"  # 自然
    FANTASY = "fantasy"  # 奇幻
    SCIFI = "scifi"  # 科幻
    HISTORICAL = "historical"  # 历史


class AtmosphereType(Enum):
    """氛围类型"""
    PEACEFUL = "peaceful"  # 宁静
    TENSE = "tense"  # 紧张
    ROMANTIC = "romantic"  # 浪漫
    MYSTERIOUS = "mysterious"  # 神秘
    MELANCHOLIC = "melancholic"  # 忧郁
    ENERGETIC = "energetic"  # 活力
    OMINOUS = "ominous"  # 不祥
    NOSTALGIC = "nostalgic"  # 怀旧


class SensoryType(Enum):
    """感官类型"""
    VISUAL = "visual"  # 视觉
    AUDITORY = "auditory"  # 听觉
    OLFACTORY = "olfactory"  # 嗅觉
    TACTILE = "tactile"  # 触觉
    GUSTATORY = "gustatory"  # 味觉


@dataclass
class EnvironmentTemplate:
    """环境模板"""
    template_id: str
    environment_type: EnvironmentType
    atmosphere_type: AtmosphereType
    description_template: str
    sensory_elements: Dict[SensoryType, List[str]] = field(default_factory=dict)
    mood_keywords: List[str] = field(default_factory=list)
    example_output: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "template_id": self.template_id,
            "environment_type": self.environment_type.value,
            "atmosphere_type": self.atmosphere_type.value,
            "description_template": self.description_template,
            "sensory_elements": {k.value: v for k, v in self.sensory_elements.items()},
            "mood_keywords": self.mood_keywords,
            "example_output": self.example_output
        }


@dataclass
class EnvironmentContext:
    """环境上下文"""
    location_name: str = ""
    time_of_day: str = ""
    weather: str = ""
    season: str = ""
    character_mood: str = ""
    story_phase: str = ""  # 故事阶段
    previous_scene: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "location_name": self.location_name,
            "time_of_day": self.time_of_day,
            "weather": self.weather,
            "season": self.season,
            "character_mood": self.character_mood,
            "story_phase": self.story_phase,
            "previous_scene": self.previous_scene
        }


class EnvironmentDescriptionService:
    """环境描写服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.templates = self._initialize_templates()
        self.sensory_libraries = self._initialize_sensory_libraries()
        self.atmosphere_modifiers = self._initialize_atmosphere_modifiers()
        self.time_descriptors = self._initialize_time_descriptors()
        self.weather_effects = self._initialize_weather_effects()
    
    def _initialize_templates(self) -> Dict[str, EnvironmentTemplate]:
        """初始化环境模板"""
        templates = {}
        
        # 宁静室内环境
        templates["peaceful_indoor"] = EnvironmentTemplate(
            template_id="peaceful_indoor",
            environment_type=EnvironmentType.INDOOR,
            atmosphere_type=AtmosphereType.PEACEFUL,
            description_template="""
{location_name}内{time_description}，{lighting_description}。
{visual_elements}
{auditory_elements}
{olfactory_elements}
{atmosphere_conclusion}
""",
            sensory_elements={
                SensoryType.VISUAL: ["柔和的光线", "温暖的色调", "整洁的摆设", "舒适的家具"],
                SensoryType.AUDITORY: ["轻柔的音乐", "钟表滴答声", "远处的鸟鸣", "安静的氛围"],
                SensoryType.OLFACTORY: ["淡淡的花香", "咖啡的香味", "清新的空气", "木质的香味"]
            },
            mood_keywords=["宁静", "温馨", "舒适", "安详"],
            example_output="书房内午后的阳光透过百叶窗洒在桌案上，形成斑驳的光影。空气中弥漫着淡淡的檀香，远处传来鸟儿啁啾声，一切都显得那么宁静祥和。"
        )
        
        # 紧张户外环境
        templates["tense_outdoor"] = EnvironmentTemplate(
            template_id="tense_outdoor",
            environment_type=EnvironmentType.OUTDOOR,
            atmosphere_type=AtmosphereType.TENSE,
            description_template="""
{location_name}{time_description}，{weather_description}。
{visual_elements}
{auditory_elements}
{tactile_elements}
{tension_buildup}
""",
            sensory_elements={
                SensoryType.VISUAL: ["阴沉的天空", "摇摆的树影", "昏暗的光线", "不祥的云层"],
                SensoryType.AUDITORY: ["风声呼啸", "树叶沙沙", "远处的雷声", "诡异的寂静"],
                SensoryType.TACTILE: ["刺骨的寒风", "湿润的空气", "粗糙的地面", "紧绷的肌肉"]
            },
            mood_keywords=["紧张", "不安", "压抑", "危险"],
            example_output="荒野上乌云密布，狂风卷起枯叶在空中飞舞。远山如黑色巨兽般蛰伏，雷声在云层中低沉地滚动，空气中弥漫着暴雨前的压抑感。"
        )
        
        # 浪漫环境
        templates["romantic_outdoor"] = EnvironmentTemplate(
            template_id="romantic_outdoor",
            environment_type=EnvironmentType.OUTDOOR,
            atmosphere_type=AtmosphereType.ROMANTIC,
            description_template="""
{location_name}在{time_description}显得格外{mood_adjective}。
{visual_elements}
{auditory_elements}
{olfactory_elements}
{romantic_atmosphere}
""",
            sensory_elements={
                SensoryType.VISUAL: ["柔和的月光", "星光点点", "花瓣飞舞", "温暖的灯光"],
                SensoryType.AUDITORY: ["轻柔的音乐", "流水潺潺", "夜莺歌唱", "微风轻拂"],
                SensoryType.OLFACTORY: ["花香阵阵", "清新的空气", "淡淡的香水味", "青草的味道"]
            },
            mood_keywords=["浪漫", "温柔", "甜蜜", "梦幻"],
            example_output="花园里月光如水，玫瑰花瓣在微风中轻舞。喷泉的水声如天籁般悦耳，空气中弥漫着茉莉花的香甜，整个世界都沉浸在温柔的梦境中。"
        )
        
        # 神秘环境
        templates["mysterious_indoor"] = EnvironmentTemplate(
            template_id="mysterious_indoor",
            environment_type=EnvironmentType.INDOOR,
            atmosphere_type=AtmosphereType.MYSTERIOUS,
            description_template="""
{location_name}笼罩在{lighting_description}中。
{visual_elements}
{auditory_elements}
{mysterious_details}
{atmosphere_conclusion}
""",
            sensory_elements={
                SensoryType.VISUAL: ["昏暗的光线", "摇曳的烛光", "神秘的阴影", "古老的装饰"],
                SensoryType.AUDITORY: ["诡异的声响", "回音缭绕", "细微的脚步声", "令人不安的寂静"],
                SensoryType.OLFACTORY: ["陈旧的气味", "蜡烛的味道", "霉味", "神秘的香料味"]
            },
            mood_keywords=["神秘", "诡异", "古老", "不可知"],
            example_output="古堡的大厅里烛光摇曳，巨大的油画在阴影中若隐若现。空气中弥漫着岁月的尘埃味，每一个角落都似乎隐藏着不为人知的秘密。"
        )
        
        return templates
    
    def _initialize_sensory_libraries(self) -> Dict[SensoryType, Dict[str, List[str]]]:
        """初始化感官描述库"""
        return {
            SensoryType.VISUAL: {
                "light": ["金色阳光", "银色月光", "昏黄灯光", "刺眼强光", "柔和光线"],
                "color": ["温暖色调", "冷色调", "鲜艳色彩", "单调色彩", "对比强烈"],
                "texture": ["光滑表面", "粗糙质感", "细腻纹理", "斑驳痕迹", "闪亮光泽"],
                "movement": ["轻柔摆动", "急速移动", "缓慢流淌", "静止不动", "颤抖摇摆"]
            },
            SensoryType.AUDITORY: {
                "nature": ["鸟儿啁啾", "风声呼啸", "流水潺潺", "雷声轰鸣", "虫鸣声声"],
                "human": ["脚步声", "呼吸声", "心跳声", "说话声", "笑声"],
                "mechanical": ["机器轰鸣", "钟表滴答", "门窗开合", "引擎声", "电子音"],
                "music": ["悠扬旋律", "激昂乐章", "轻柔音乐", "沉重节拍", "和谐音符"]
            },
            SensoryType.OLFACTORY: {
                "natural": ["花香", "草香", "泥土味", "海风味", "森林气息"],
                "food": ["咖啡香", "面包香", "香料味", "烟火味", "酒香"],
                "artificial": ["香水味", "清洁剂味", "油漆味", "塑料味", "金属味"],
                "emotional": ["温暖气息", "清新空气", "陈旧气味", "神秘香味", "熟悉味道"]
            },
            SensoryType.TACTILE: {
                "temperature": ["温暖", "炎热", "凉爽", "寒冷", "冰冷"],
                "texture": ["光滑", "粗糙", "柔软", "坚硬", "湿润"],
                "pressure": ["轻抚", "重压", "紧握", "轻触", "拥抱"],
                "air": ["微风", "强风", "闷热", "清爽", "潮湿"]
            }
        }
    
    def _initialize_atmosphere_modifiers(self) -> Dict[AtmosphereType, Dict[str, List[str]]]:
        """初始化氛围修饰词"""
        return {
            AtmosphereType.PEACEFUL: {
                "adjectives": ["宁静的", "安详的", "平和的", "舒适的", "温馨的"],
                "verbs": ["轻抚", "环绕", "沐浴", "拥抱", "守护"],
                "metaphors": ["如母亲的怀抱", "像温柔的摇篮", "似天堂般美好"]
            },
            AtmosphereType.TENSE: {
                "adjectives": ["紧张的", "压抑的", "不安的", "危险的", "阴森的"],
                "verbs": ["笼罩", "压迫", "威胁", "逼近", "包围"],
                "metaphors": ["如暴风雨前的宁静", "像蓄势待发的猛兽", "似悬在头顶的利剑"]
            },
            AtmosphereType.ROMANTIC: {
                "adjectives": ["浪漫的", "温柔的", "甜蜜的", "梦幻的", "迷人的"],
                "verbs": ["轻抚", "拥抱", "亲吻", "陶醉", "沉醉"],
                "metaphors": ["如诗如画", "像童话世界", "似爱情的乐章"]
            },
            AtmosphereType.MYSTERIOUS: {
                "adjectives": ["神秘的", "诡异的", "不可知的", "古老的", "深邃的"],
                "verbs": ["隐藏", "笼罩", "暗示", "诱惑", "迷惑"],
                "metaphors": ["如迷雾般缥缈", "像古老的谜题", "似深不见底的深渊"]
            }
        }
    
    def _initialize_time_descriptors(self) -> Dict[str, Dict[str, str]]:
        """初始化时间描述词"""
        return {
            "dawn": {"description": "晨曦初露", "mood": "希望", "light": "柔和的晨光"},
            "morning": {"description": "朝阳初升", "mood": "活力", "light": "明亮的阳光"},
            "noon": {"description": "正午时分", "mood": "热烈", "light": "强烈的日光"},
            "afternoon": {"description": "午后时光", "mood": "慵懒", "light": "温暖的斜阳"},
            "dusk": {"description": "黄昏时分", "mood": "宁静", "light": "金色的夕阳"},
            "night": {"description": "夜幕降临", "mood": "神秘", "light": "皎洁的月光"},
            "midnight": {"description": "午夜时分", "mood": "深沉", "light": "微弱的星光"}
        }
    
    def _initialize_weather_effects(self) -> Dict[str, Dict[str, Any]]:
        """初始化天气效果"""
        return {
            "sunny": {
                "visual": ["阳光明媚", "万里无云", "光影斑驳"],
                "mood": "明朗",
                "atmosphere": "温暖舒适"
            },
            "rainy": {
                "visual": ["雨滴飞溅", "水雾弥漫", "湿润大地"],
                "mood": "忧郁",
                "atmosphere": "清新湿润"
            },
            "cloudy": {
                "visual": ["乌云密布", "天色阴沉", "光线昏暗"],
                "mood": "压抑",
                "atmosphere": "沉闷厚重"
            },
            "windy": {
                "visual": ["树叶飞舞", "衣袂飘扬", "尘土飞扬"],
                "mood": "动荡",
                "atmosphere": "清爽凉快"
            },
            "snowy": {
                "visual": ["雪花纷飞", "银装素裹", "晶莹剔透"],
                "mood": "纯净",
                "atmosphere": "寂静清冷"
            }
        }
    
    def generate_environment_description(
        self,
        environment_context: EnvironmentContext,
        target_atmosphere: AtmosphereType,
        environment_type: EnvironmentType = EnvironmentType.OUTDOOR,
        focus_senses: List[SensoryType] = None,
        length: int = 200
    ) -> str:
        """生成环境描写"""
        try:
            # 选择合适的模板
            template = self._select_template(environment_type, target_atmosphere)
            
            if not template:
                return self._generate_fallback_description(environment_context, target_atmosphere)
            
            # 构建描述元素
            description_elements = self._build_description_elements(
                template, environment_context, focus_senses
            )
            
            # 生成完整描述
            description = self._compose_description(template, description_elements, length)
            
            return description
            
        except Exception as e:
            self.logger.error(f"生成环境描写失败: {e}")
            return f"[{environment_context.location_name}的环境描写]"
    
    def _select_template(
        self, 
        env_type: EnvironmentType, 
        atmosphere: AtmosphereType
    ) -> Optional[EnvironmentTemplate]:
        """选择合适的模板"""
        # 优先选择完全匹配的模板
        for template in self.templates.values():
            if (template.environment_type == env_type and 
                template.atmosphere_type == atmosphere):
                return template
        
        # 其次选择氛围匹配的模板
        for template in self.templates.values():
            if template.atmosphere_type == atmosphere:
                return template
        
        # 最后选择环境类型匹配的模板
        for template in self.templates.values():
            if template.environment_type == env_type:
                return template
        
        return None
    
    def _build_description_elements(
        self,
        template: EnvironmentTemplate,
        context: EnvironmentContext,
        focus_senses: List[SensoryType] = None
    ) -> Dict[str, str]:
        """构建描述元素"""
        elements = {}
        
        # 时间描述
        if context.time_of_day:
            time_desc = self.time_descriptors.get(context.time_of_day, {})
            elements["time_description"] = time_desc.get("description", context.time_of_day)
            elements["lighting_description"] = time_desc.get("light", "光线")
        
        # 天气描述
        if context.weather:
            weather_desc = self.weather_effects.get(context.weather, {})
            elements["weather_description"] = weather_desc.get("atmosphere", "")
        
        # 感官元素
        focus_senses = focus_senses or [SensoryType.VISUAL, SensoryType.AUDITORY]
        
        for sense in focus_senses:
            if sense in template.sensory_elements:
                sense_elements = template.sensory_elements[sense]
                elements[f"{sense.value}_elements"] = self._select_sensory_elements(
                    sense_elements, context
                )
        
        # 氛围修饰
        atmosphere_mods = self.atmosphere_modifiers.get(template.atmosphere_type, {})
        elements["mood_adjective"] = self._select_random_element(
            atmosphere_mods.get("adjectives", ["美丽的"])
        )
        
        return elements
    
    def _select_sensory_elements(
        self, 
        elements: List[str], 
        context: EnvironmentContext
    ) -> str:
        """选择感官元素"""
        # 根据上下文选择合适的感官元素
        selected = elements[:2] if len(elements) >= 2 else elements
        return "，".join(selected) + "。"
    
    def _select_random_element(self, elements: List[str]) -> str:
        """随机选择元素"""
        import random
        return random.choice(elements) if elements else ""
    
    def _compose_description(
        self,
        template: EnvironmentTemplate,
        elements: Dict[str, str],
        target_length: int
    ) -> str:
        """组合描述"""
        description = template.description_template
        
        # 替换占位符
        for key, value in elements.items():
            placeholder = "{" + key + "}"
            if placeholder in description:
                description = description.replace(placeholder, value)
        
        # 清理未替换的占位符
        import re
        description = re.sub(r'\{[^}]+\}', '', description)
        
        # 清理多余的空白
        description = re.sub(r'\s+', ' ', description).strip()
        
        # 长度调整
        if len(description) > target_length * 1.2:
            sentences = description.split('。')
            description = '。'.join(sentences[:len(sentences)//2]) + '。'
        
        return description
    
    def _generate_fallback_description(
        self,
        context: EnvironmentContext,
        atmosphere: AtmosphereType
    ) -> str:
        """生成备用描述"""
        location = context.location_name or "这里"
        time = context.time_of_day or "此时"
        mood = atmosphere.value
        
        return f"{location}在{time}显得格外{mood}。"
    
    def enhance_with_character_perspective(
        self,
        base_description: str,
        character_info: Dict[str, Any]
    ) -> str:
        """基于角色视角增强描述"""
        character_mood = character_info.get("current_mood", "")
        personality = character_info.get("personality_tags", [])
        
        # 根据角色心情调整描述
        if character_mood == "sad":
            base_description += " 一切都显得那么灰暗无光。"
        elif character_mood == "happy":
            base_description += " 世界在他眼中都变得明亮起来。"
        elif character_mood == "anxious":
            base_description += " 每一个细节都让人感到不安。"
        
        return base_description
    
    def get_atmosphere_suggestions(
        self,
        story_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """获取氛围建议"""
        suggestions = []
        
        story_phase = story_context.get("phase", "")
        
        if story_phase == "opening":
            suggestions.append({
                "atmosphere": AtmosphereType.PEACEFUL.value,
                "description": "开篇宁静氛围，为后续冲突做铺垫"
            })
        elif story_phase == "conflict":
            suggestions.append({
                "atmosphere": AtmosphereType.TENSE.value,
                "description": "紧张氛围，突出冲突激烈程度"
            })
        elif story_phase == "climax":
            suggestions.append({
                "atmosphere": AtmosphereType.OMINOUS.value,
                "description": "不祥氛围，营造高潮紧迫感"
            })
        elif story_phase == "resolution":
            suggestions.append({
                "atmosphere": AtmosphereType.PEACEFUL.value,
                "description": "回归宁静，展现问题解决后的和谐"
            })
        
        return suggestions


# 全局实例
environment_description_service = EnvironmentDescriptionService()


def get_environment_description_service() -> EnvironmentDescriptionService:
    """获取环境描写服务实例"""
    return environment_description_service
