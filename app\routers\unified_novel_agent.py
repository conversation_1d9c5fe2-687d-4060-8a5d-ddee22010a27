#!/usr/bin/env python3
"""
统一小说创作Agent API路由
整合分析建议和内容生成功能
"""

from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

from app.services.unified_novel_agent_service import (
    get_unified_novel_agent_service, UnifiedAgentRequest, AgentMode,
    ConflictType, EmotionType, PleasureType, GenerationMode
)
from app.services.intelligent_intent_recognizer import get_intelligent_intent_recognizer

router = APIRouter(prefix="/projects/{project_id}/ai-assistant", tags=["AI创作助手"])


class ChatRequest(BaseModel):
    """聊天请求"""
    message: str
    mode: str = AgentMode.AUTO.value
    context: Optional[Dict[str, Any]] = None
    
    # 生成相关参数（可选）
    target_length: int = 500
    generation_mode: Optional[str] = None
    target_conflict_type: Optional[str] = None
    target_emotion_type: Optional[str] = None
    target_pleasure_type: Optional[str] = None
    
    # 分析相关参数（可选）
    analysis_depth: str = "standard"
    include_suggestions: bool = True


class ChatResponse(BaseModel):
    """聊天响应"""
    success: bool
    mode_used: str
    intent_detected: str
    
    # 响应内容
    response_text: str = ""
    generated_content: Optional[str] = None
    
    # 分析结果
    thinking_process: List[Dict[str, Any]] = []
    related_memories: List[Dict[str, Any]] = []
    
    # 质量和建议
    quality_score: float = 0.0
    suggestions: List[str] = []
    next_actions: List[Dict[str, Any]] = []
    
    # 元数据
    execution_time: float = 0.0
    metadata: Dict[str, Any] = {}


class IntentAnalysisRequest(BaseModel):
    """意图分析请求"""
    message: str
    context: Optional[Dict[str, Any]] = None


class IntentAnalysisResponse(BaseModel):
    """意图分析响应"""
    primary_intent: str
    intent_category: str
    confidence: float
    entities: Dict[str, List[str]] = {}
    keywords: List[str] = []
    context_clues: List[str] = []
    suggested_mode: str


@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai_assistant(project_id: str, request: ChatRequest):
    """与AI创作助手对话"""
    try:
        agent_service = get_unified_novel_agent_service()
        
        # 构建统一请求
        unified_request = UnifiedAgentRequest(
            query=request.message,
            project_id=project_id,
            mode=AgentMode(request.mode),
            target_length=request.target_length,
            analysis_depth=request.analysis_depth,
            include_suggestions=request.include_suggestions,
            context=request.context
        )
        
        # 设置生成参数
        if request.generation_mode:
            unified_request.generation_mode = GenerationMode(request.generation_mode)
        if request.target_conflict_type:
            unified_request.target_conflict_type = ConflictType(request.target_conflict_type)
        if request.target_emotion_type:
            unified_request.target_emotion_type = EmotionType(request.target_emotion_type)
        if request.target_pleasure_type:
            unified_request.target_pleasure_type = PleasureType(request.target_pleasure_type)
        
        # 处理请求
        result = await agent_service.process_request(unified_request)
        
        # 构建响应文本
        response_text = ""
        if result.analysis_response:
            response_text = result.analysis_response
        elif result.generated_content:
            response_text = f"我为您创作了以下内容：\n\n{result.generated_content}"
        else:
            response_text = "抱歉，我无法处理您的请求。"
        
        return ChatResponse(
            success=result.success,
            mode_used=result.mode_used.value,
            intent_detected=result.intent_detected.value,
            response_text=response_text,
            generated_content=result.generated_content,
            thinking_process=result.thinking_process,
            related_memories=result.related_memories,
            quality_score=result.quality_score,
            suggestions=result.suggestions,
            next_actions=result.next_actions,
            execution_time=result.execution_time,
            metadata=result.metadata
        )
        
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/analyze-intent", response_model=IntentAnalysisResponse)
async def analyze_intent(project_id: str, request: IntentAnalysisRequest):
    """分析用户意图"""
    try:
        intent_recognizer = get_intelligent_intent_recognizer()
        
        result = await intent_recognizer.recognize_intent(
            request.message, 
            request.context
        )
        
        return IntentAnalysisResponse(
            primary_intent=result.primary_intent,
            intent_category=result.intent_category.value,
            confidence=result.confidence,
            entities=result.entities,
            keywords=result.keywords,
            context_clues=result.context_clues,
            suggested_mode=result.suggested_mode
        )
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/capabilities")
async def get_ai_assistant_capabilities(project_id: str):
    """获取AI助手能力说明"""
    try:
        agent_service = get_unified_novel_agent_service()
        capabilities = agent_service.get_capabilities()
        
        return {
            "project_id": project_id,
            "capabilities": capabilities,
            "examples": {
                "analysis_queries": [
                    "林深这个角色是什么性格？",
                    "分析一下当前的剧情发展",
                    "总结一下主要场景设定"
                ],
                "generation_queries": [
                    "帮我写下一章的内容",
                    "生成一段对话场景",
                    "描写一个紧张的追逐场面"
                ],
                "hybrid_queries": [
                    "怎么写好角色的心理描写？",
                    "如何营造悬疑氛围？",
                    "给我一些剧情发展的建议"
                ]
            },
            "supported_modes": [
                {
                    "mode": "auto",
                    "name": "智能模式",
                    "description": "自动识别意图，选择最佳处理方式"
                },
                {
                    "mode": "analysis",
                    "name": "分析模式", 
                    "description": "深入分析小说内容，提供洞察和建议"
                },
                {
                    "mode": "generation",
                    "name": "生成模式",
                    "description": "基于三维模型创作高质量内容"
                },
                {
                    "mode": "hybrid",
                    "name": "混合模式",
                    "description": "结合分析和生成，提供全面支持"
                }
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/conversation-starters")
async def get_conversation_starters(project_id: str):
    """获取对话启动器"""
    try:
        # 这里可以根据项目内容动态生成启动器
        starters = [
            {
                "category": "角色分析",
                "icon": "👥",
                "text": "分析主要角色性格",
                "example": "分析一下主角的性格特点"
            },
            {
                "category": "剧情发展",
                "icon": "📖", 
                "text": "剧情发展建议",
                "example": "接下来剧情可以怎么发展？"
            },
            {
                "category": "场景描述",
                "icon": "🌍",
                "text": "场景描述技巧", 
                "example": "如何描写紧张的氛围？"
            },
            {
                "category": "内容生成",
                "icon": "✍️",
                "text": "生成新内容",
                "example": "帮我写一段对话"
            }
        ]
        
        return {"starters": starters}
        
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.post("/quick-actions")
async def execute_quick_action(project_id: str, action_data: Dict[str, Any]):
    """执行快速操作"""
    try:
        action_type = action_data.get("action")
        
        if action_type == "analyze_characters":
            query = "分析项目中的主要角色"
        elif action_type == "plot_suggestions":
            query = "给出剧情发展建议"
        elif action_type == "scene_tips":
            query = "场景描述技巧"
        elif action_type == "generate_content":
            query = action_data.get("prompt", "生成一段内容")
        else:
            raise ValueError(f"不支持的操作类型: {action_type}")
        
        # 构建请求
        request = ChatRequest(
            message=query,
            mode=action_data.get("mode", "auto"),
            target_length=action_data.get("length", 300)
        )
        
        # 执行操作
        return await chat_with_ai_assistant(project_id, request)
        
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


@router.get("/model-options")
async def get_model_options():
    """获取模型选项"""
    return {
        "generation_modes": [
            {"value": mode.value, "name": mode.name} 
            for mode in GenerationMode
        ],
        "conflict_types": [
            {"value": conflict.value, "name": conflict.name} 
            for conflict in ConflictType
        ],
        "emotion_types": [
            {"value": emotion.value, "name": emotion.name} 
            for emotion in EmotionType
        ],
        "pleasure_types": [
            {"value": pleasure.value, "name": pleasure.name} 
            for pleasure in PleasureType
        ],
        "analysis_depths": [
            {"value": "minimal", "name": "简要分析"},
            {"value": "standard", "name": "标准分析"},
            {"value": "deep", "name": "深度分析"}
        ]
    }
