#!/usr/bin/env python3
"""
测试角色详情API
"""

import requests
import json
import urllib.parse

def test_character_detail_api():
    """测试角色详情API"""
    print("🧪 测试角色详情API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    character_name = "林深"
    
    # URL编码角色名称
    encoded_name = urllib.parse.quote(character_name)
    
    # 测试获取角色详情
    detail_url = f"{base_url}/api/v1/characters/{project_id}/{encoded_name}/detail"
    
    print(f"请求URL: {detail_url}")
    
    try:
        response = requests.get(detail_url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取角色详情成功！")
            print(f"角色名称: {data.get('basic_info', {}).get('name', 'Unknown')}")
            print(f"角色描述: {data.get('basic_info', {}).get('description', 'No description')[:100]}...")
            print(f"性格特征: {data.get('personality', {}).get('traits', [])}")
            print(f"重要性分数: {data.get('statistics', {}).get('importance_score', 0)}")
            
            return True
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：服务器未运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_character_update_api():
    """测试角色更新API"""
    print("\n🧪 测试角色更新API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    character_name = "林深"
    
    # URL编码角色名称
    encoded_name = urllib.parse.quote(character_name)
    
    # 测试更新角色信息
    update_url = f"{base_url}/api/v1/characters/{project_id}/{encoded_name}/detail"
    
    update_data = {
        "description": "更新后的角色描述：林深是一个经验丰富的记忆修复师，在新东京第三区工作。",
        "appearance": "更新后的外貌：身材高大，左眼是金属义眼，后颈有神经接口。",
        "personality_tags": ["冷静", "专业", "谨慎", "技术专家"],
        "current_status": "正在处理一个复杂的记忆修复案例",
        "goals": ["完成当前案例", "提升技术水平", "保护客户安全"],
        "abilities": ["记忆修复", "神经接口操作", "数据分析"],
        "weaknesses": ["对未知技术谨慎", "依赖AI助手"]
    }
    
    print(f"请求URL: {update_url}")
    print(f"更新数据: {json.dumps(update_data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.put(
            update_url, 
            json=update_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 更新角色信息成功！")
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return True
            
        else:
            print(f"❌ 更新失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：服务器未运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_character_list_api():
    """测试角色列表API"""
    print("\n🧪 测试角色列表API")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    
    # 测试获取角色列表
    list_url = f"{base_url}/api/v1/characters/{project_id}/advanced"
    
    print(f"请求URL: {list_url}")
    
    try:
        response = requests.get(list_url, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取角色列表成功！")
            print(f"角色数量: {len(data)}")
            
            for i, char in enumerate(data[:3]):  # 只显示前3个
                print(f"  角色 {i+1}: {char.get('name', 'Unknown')}")
                print(f"    描述: {char.get('description', 'No description')[:50]}...")
                print(f"    重要性: {char.get('importance_score', 0)}")
            
            return True
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：服务器未运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 角色管理API测试")
    print("=" * 60)
    
    success = True
    
    # 测试角色列表
    success &= test_character_list_api()
    
    # 测试角色详情
    success &= test_character_detail_api()
    
    # 测试角色更新
    success &= test_character_update_api()
    
    if success:
        print("\n✅ 所有测试通过！角色管理API正常工作。")
    else:
        print("\n❌ 部分测试失败，请检查服务器状态和日志。")
