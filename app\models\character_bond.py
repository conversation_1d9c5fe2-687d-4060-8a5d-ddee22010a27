"""
角色羁绊关系模型
定义角色之间的各种羁绊类型和强度计算
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum
import uuid


class BondType(str, Enum):
    """羁绊类型"""
    # 正面情感羁绊
    LOVE = "love"                      # 爱情
    FRIENDSHIP = "friendship"          # 友情
    FAMILY = "family"                  # 亲情
    MENTOR = "mentor"                  # 师徒关系
    LOYALTY = "loyalty"                # 忠诚
    TRUST = "trust"                    # 信任
    ADMIRATION = "admiration"          # 敬佩
    PROTECTION = "protection"          # 保护
    GRATITUDE = "gratitude"            # 感激
    RESPECT = "respect"                # 尊重
    
    # 负面羁绊
    HATRED = "hatred"                  # 仇恨
    RIVALRY = "rivalry"                # 竞争
    JEALOUSY = "jealousy"              # 嫉妒
    BETRAYAL = "betrayal"              # 背叛
    FEAR = "fear"                      # 恐惧
    DISTRUST = "distrust"              # 不信任
    CONTEMPT = "contempt"              # 蔑视
    REVENGE = "revenge"                # 复仇
    
    # 复杂羁绊
    MISUNDERSTANDING = "misunderstanding"  # 误会
    COMPLICATED = "complicated"            # 复杂关系
    FORBIDDEN = "forbidden"                # 禁忌关系
    UNREQUITED = "unrequited"              # 单相思
    CONFLICTED = "conflicted"              # 矛盾关系
    DEPENDENCY = "dependency"              # 依赖关系
    MANIPULATION = "manipulation"          # 操控关系
    
    # 中性羁绊
    ALLIANCE = "alliance"              # 联盟
    COOPERATION = "cooperation"        # 合作
    ACQUAINTANCE = "acquaintance"      # 熟人
    STRANGER = "stranger"              # 陌生人


class BondIntensity(str, Enum):
    """羁绊强度等级"""
    NONE = "none"                      # 无关系 (0.0)
    WEAK = "weak"                      # 微弱 (0.1-0.3)
    MODERATE = "moderate"              # 中等 (0.3-0.6)
    STRONG = "strong"                  # 强烈 (0.6-0.8)
    INTENSE = "intense"                # 极强 (0.8-1.0)


class BondDirection(str, Enum):
    """羁绊方向"""
    MUTUAL = "mutual"                  # 双向
    ONE_WAY = "one_way"               # 单向
    ASYMMETRIC = "asymmetric"         # 不对称


@dataclass
class BondEvolution:
    """羁绊演变记录"""
    change_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    chapter_id: str = ""
    old_type: Optional[BondType] = None
    new_type: BondType = BondType.STRANGER
    old_strength: float = 0.0
    new_strength: float = 0.0
    trigger_event: str = ""
    description: str = ""
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class BondMetrics:
    """羁绊度量指标"""
    stability: float = 0.5            # 稳定性 (0.0-1.0)
    volatility: float = 0.0           # 波动性 (0.0-1.0)
    growth_rate: float = 0.0          # 增长率 (-1.0-1.0)
    interaction_frequency: float = 0.0 # 互动频率 (0.0-1.0)
    emotional_depth: float = 0.0      # 情感深度 (0.0-1.0)
    conflict_level: float = 0.0       # 冲突水平 (0.0-1.0)


@dataclass
class CharacterBond:
    """角色羁绊关系"""
    bond_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = ""
    
    # 关系双方
    character_a: str = ""
    character_b: str = ""
    
    # 羁绊属性
    bond_type: BondType = BondType.STRANGER
    strength: float = 0.0              # 羁绊强度 (0.0-1.0)
    direction: BondDirection = BondDirection.MUTUAL
    
    # 详细信息
    description: str = ""
    origin_chapter: str = ""           # 关系起源章节
    current_chapter: str = ""          # 当前章节
    
    # 羁绊度量
    metrics: BondMetrics = field(default_factory=BondMetrics)
    
    # 演变历史
    evolution_history: List[BondEvolution] = field(default_factory=list)
    
    # 元数据
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def get_intensity_level(self) -> BondIntensity:
        """获取强度等级"""
        if self.strength == 0.0:
            return BondIntensity.NONE
        elif self.strength <= 0.3:
            return BondIntensity.WEAK
        elif self.strength <= 0.6:
            return BondIntensity.MODERATE
        elif self.strength <= 0.8:
            return BondIntensity.STRONG
        else:
            return BondIntensity.INTENSE
    
    def is_positive_bond(self) -> bool:
        """判断是否为正面羁绊"""
        positive_bonds = {
            BondType.LOVE, BondType.FRIENDSHIP, BondType.FAMILY,
            BondType.MENTOR, BondType.LOYALTY, BondType.TRUST,
            BondType.ADMIRATION, BondType.PROTECTION, BondType.GRATITUDE,
            BondType.RESPECT, BondType.ALLIANCE, BondType.COOPERATION
        }
        return self.bond_type in positive_bonds
    
    def is_negative_bond(self) -> bool:
        """判断是否为负面羁绊"""
        negative_bonds = {
            BondType.HATRED, BondType.RIVALRY, BondType.JEALOUSY,
            BondType.BETRAYAL, BondType.FEAR, BondType.DISTRUST,
            BondType.CONTEMPT, BondType.REVENGE
        }
        return self.bond_type in negative_bonds
    
    def is_complex_bond(self) -> bool:
        """判断是否为复杂羁绊"""
        complex_bonds = {
            BondType.MISUNDERSTANDING, BondType.COMPLICATED,
            BondType.FORBIDDEN, BondType.UNREQUITED,
            BondType.CONFLICTED, BondType.DEPENDENCY,
            BondType.MANIPULATION
        }
        return self.bond_type in complex_bonds
    
    def add_evolution(self, evolution: BondEvolution):
        """添加演变记录"""
        self.evolution_history.append(evolution)
        self.updated_at = datetime.now()
        
        # 更新度量指标
        self._update_metrics()
    
    def _update_metrics(self):
        """更新度量指标"""
        if not self.evolution_history:
            return
        
        # 计算稳定性（基于变化频率）
        recent_changes = len([e for e in self.evolution_history[-5:]])
        self.metrics.stability = max(0.0, 1.0 - (recent_changes * 0.2))
        
        # 计算波动性（基于强度变化）
        if len(self.evolution_history) > 1:
            strength_changes = [
                abs(e.new_strength - e.old_strength) 
                for e in self.evolution_history[-5:]
            ]
            self.metrics.volatility = min(1.0, sum(strength_changes) / len(strength_changes))
        
        # 计算增长率
        if len(self.evolution_history) >= 2:
            first_strength = self.evolution_history[0].old_strength or 0.0
            last_strength = self.evolution_history[-1].new_strength
            self.metrics.growth_rate = (last_strength - first_strength) / max(1.0, first_strength)
            self.metrics.growth_rate = max(-1.0, min(1.0, self.metrics.growth_rate))
    
    def get_bond_summary(self) -> Dict[str, Any]:
        """获取羁绊摘要"""
        return {
            'bond_id': self.bond_id,
            'characters': [self.character_a, self.character_b],
            'bond_type': self.bond_type.value,
            'strength': self.strength,
            'intensity_level': self.get_intensity_level().value,
            'direction': self.direction.value,
            'is_positive': self.is_positive_bond(),
            'is_negative': self.is_negative_bond(),
            'is_complex': self.is_complex_bond(),
            'description': self.description,
            'origin_chapter': self.origin_chapter,
            'current_chapter': self.current_chapter,
            'evolution_count': len(self.evolution_history),
            'metrics': {
                'stability': self.metrics.stability,
                'volatility': self.metrics.volatility,
                'growth_rate': self.metrics.growth_rate
            },
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }


class BondTypeCompatibility:
    """羁绊类型兼容性和转换规则"""
    
    # 羁绊类型转换概率矩阵
    TRANSITION_PROBABILITIES = {
        # 从友情可能转向的关系
        BondType.FRIENDSHIP: {
            BondType.LOVE: 0.3,
            BondType.LOYALTY: 0.4,
            BondType.TRUST: 0.5,
            BondType.RIVALRY: 0.2,
            BondType.BETRAYAL: 0.1
        },
        # 从爱情可能转向的关系
        BondType.LOVE: {
            BondType.HATRED: 0.2,
            BondType.BETRAYAL: 0.1,
            BondType.FAMILY: 0.1,
            BondType.PROTECTION: 0.4,
            BondType.JEALOUSY: 0.3
        },
        # 从仇恨可能转向的关系
        BondType.HATRED: {
            BondType.REVENGE: 0.4,
            BondType.FEAR: 0.2,
            BondType.RIVALRY: 0.3,
            BondType.LOVE: 0.05,  # 爱恨交织
            BondType.RESPECT: 0.1
        }
        # 可以继续添加更多转换规则
    }
    
    # 羁绊强度影响因子
    STRENGTH_FACTORS = {
        BondType.FAMILY: 1.2,      # 亲情天然较强
        BondType.LOVE: 1.1,        # 爱情强度较高
        BondType.HATRED: 1.0,      # 仇恨可以很强
        BondType.FRIENDSHIP: 0.8,  # 友情相对温和
        BondType.ACQUAINTANCE: 0.3 # 熟人关系较弱
    }
    
    @classmethod
    def get_transition_probability(cls, from_type: BondType, to_type: BondType) -> float:
        """获取羁绊类型转换概率"""
        return cls.TRANSITION_PROBABILITIES.get(from_type, {}).get(to_type, 0.0)
    
    @classmethod
    def get_strength_factor(cls, bond_type: BondType) -> float:
        """获取羁绊类型的强度因子"""
        return cls.STRENGTH_FACTORS.get(bond_type, 1.0)
    
    @classmethod
    def calculate_bond_strength(cls, base_strength: float, bond_type: BondType,
                              interaction_count: int = 1, 
                              emotional_intensity: float = 0.5) -> float:
        """计算羁绊强度"""
        # 基础强度
        strength = base_strength
        
        # 应用类型因子
        strength *= cls.get_strength_factor(bond_type)
        
        # 互动次数影响
        interaction_factor = min(1.0, interaction_count * 0.1)
        strength *= (1.0 + interaction_factor)
        
        # 情感强度影响
        strength *= (0.5 + emotional_intensity * 0.5)
        
        # 确保在有效范围内
        return max(0.0, min(1.0, strength))


# 预定义的羁绊模板
BOND_TEMPLATES = {
    "师徒关系": CharacterBond(
        bond_type=BondType.MENTOR,
        strength=0.7,
        direction=BondDirection.ASYMMETRIC,
        description="师父与弟子之间的传承关系",
        tags=["教学", "传承", "指导"]
    ),
    
    "青梅竹马": CharacterBond(
        bond_type=BondType.FRIENDSHIP,
        strength=0.8,
        direction=BondDirection.MUTUAL,
        description="从小一起长大的深厚友谊",
        tags=["童年", "友谊", "深厚"]
    ),
    
    "生死之交": CharacterBond(
        bond_type=BondType.LOYALTY,
        strength=0.9,
        direction=BondDirection.MUTUAL,
        description="经历生死考验的深厚友谊",
        tags=["生死", "忠诚", "考验"]
    ),
    
    "宿敌关系": CharacterBond(
        bond_type=BondType.RIVALRY,
        strength=0.8,
        direction=BondDirection.MUTUAL,
        description="势均力敌的竞争对手",
        tags=["竞争", "对手", "势均力敌"]
    ),
    
    "血海深仇": CharacterBond(
        bond_type=BondType.HATRED,
        strength=0.9,
        direction=BondDirection.MUTUAL,
        description="不共戴天的深仇大恨",
        tags=["仇恨", "复仇", "深仇"]
    )
}
