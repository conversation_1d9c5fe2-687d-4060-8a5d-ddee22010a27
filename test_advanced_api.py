#!/usr/bin/env python3
"""
测试高级角色管理API
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000/api/v1"
TEST_PROJECT_ID = "63ae07ad-2bc6-4354-92a9-db4be8160215"

def test_api_endpoint(endpoint, description):
    """测试API端点"""
    print(f"\n🧪 测试: {description}")
    print(f"📡 请求: GET {endpoint}")
    
    try:
        response = requests.get(endpoint, timeout=10)
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功! 返回数据类型: {type(data)}")
            if isinstance(data, list):
                print(f"📝 数据数量: {len(data)}")
                if data:
                    print(f"📋 第一项示例: {json.dumps(data[0], ensure_ascii=False, indent=2)[:200]}...")
            elif isinstance(data, dict):
                print(f"📋 数据键: {list(data.keys())}")
            return True
        else:
            print(f"❌ 失败! 错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保后端服务正在运行 (python -m uvicorn app.main:app --reload)")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时!")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试高级角色管理API")
    print(f"🎯 测试项目ID: {TEST_PROJECT_ID}")
    
    # 测试用例
    test_cases = [
        (
            f"{BASE_URL}/characters/{TEST_PROJECT_ID}",
            "获取高级角色列表"
        ),
        (
            f"{BASE_URL}/characters/{TEST_PROJECT_ID}?sort_by=importance&filter_by=all",
            "获取角色列表（带参数）"
        ),
        (
            f"{BASE_URL}/characters/{TEST_PROJECT_ID}/network",
            "获取关系网络"
        ),
        (
            f"{BASE_URL}/characters/{TEST_PROJECT_ID}/search?query=测试&search_type=all",
            "搜索角色"
        )
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for endpoint, description in test_cases:
        if test_api_endpoint(endpoint, description):
            success_count += 1
    
    # 测试结果
    print(f"\n📊 测试结果: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查后端服务和数据库")
    
    # 测试角色详情（需要先有角色数据）
    print(f"\n🔍 尝试获取角色详情...")
    try:
        # 先获取角色列表
        response = requests.get(f"{BASE_URL}/characters/{TEST_PROJECT_ID}", timeout=5)
        if response.status_code == 200:
            characters = response.json()
            if characters:
                first_character = characters[0]
                character_name = first_character['name']
                detail_endpoint = f"{BASE_URL}/characters/{TEST_PROJECT_ID}/{character_name}/detail"
                test_api_endpoint(detail_endpoint, f"获取角色详情: {character_name}")
            else:
                print("📝 项目中暂无角色数据，跳过角色详情测试")
        else:
            print("📝 无法获取角色列表，跳过角色详情测试")
    except Exception as e:
        print(f"📝 角色详情测试跳过: {e}")

if __name__ == "__main__":
    main()
