<template>
  <div class="ai-assistant-view">
    <div class="container-fluid">
      <div class="row">
        <!-- 左侧：项目信息 -->
        <div class="col-md-3">
          <div class="project-info-card">
            <div class="card">
              <div class="card-header">
                <h5>📚 项目信息</h5>
              </div>
              <div class="card-body">
                <div v-if="projectInfo" class="project-details">
                  <h6>{{ projectInfo.name }}</h6>
                  <p class="text-muted">{{ projectInfo.description }}</p>
                  
                  <div class="project-stats">
                    <div class="stat-item">
                      <span class="stat-label">章节数:</span>
                      <span class="stat-value">{{ projectInfo.chapter_count || 0 }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">字数:</span>
                      <span class="stat-value">{{ formatWordCount(projectInfo.word_count || 0) }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">角色数:</span>
                      <span class="stat-value">{{ projectInfo.character_count || 0 }}</span>
                    </div>
                  </div>
                </div>
                
                <div v-else class="text-center">
                  <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">加载中...</span>
                  </div>
                  <p class="mt-2 text-muted">加载项目信息...</p>
                </div>
              </div>
            </div>
          </div>

          <!-- AI助手能力 -->
          <div class="capabilities-card mt-3">
            <div class="card">
              <div class="card-header">
                <h5>🤖 AI能力</h5>
              </div>
              <div class="card-body">
                <div v-if="capabilities" class="capabilities-list">
                  <div class="capability-item" v-for="capability in capabilities.capabilities" :key="capability">
                    <i class="bi bi-check-circle text-success"></i>
                    <span>{{ capability }}</span>
                  </div>
                  
                  <div class="mt-3">
                    <h6>支持模式:</h6>
                    <div class="mode-badges">
                      <span 
                        v-for="mode in capabilities.modes" 
                        :key="mode.value"
                        class="badge bg-primary me-1 mb-1"
                        :title="mode.description"
                      >
                        {{ mode.name }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：AI助手聊天界面 -->
        <div class="col-md-6">
          <div class="ai-chat-container">
            <UnifiedAIAssistant 
              :project-id="projectId"
              :initial-expanded="true"
              @content-generated="onContentGenerated"
              @suggestion-applied="onSuggestionApplied"
            />
          </div>
        </div>

        <!-- 右侧：生成内容和历史 -->
        <div class="col-md-3">
          <!-- 生成内容预览 -->
          <div class="generated-content-card">
            <div class="card">
              <div class="card-header">
                <h5>✍️ 生成内容</h5>
              </div>
              <div class="card-body">
                <div v-if="generatedContents.length > 0">
                  <div 
                    v-for="(content, index) in generatedContents.slice(0, 3)" 
                    :key="index"
                    class="content-item"
                  >
                    <div class="content-preview">
                      {{ content.content.substring(0, 100) }}...
                    </div>
                    <div class="content-meta">
                      <span class="content-time">{{ formatTime(content.timestamp) }}</span>
                      <div class="content-actions">
                        <button 
                          @click="copyContent(content.content)" 
                          class="btn btn-sm btn-outline-primary"
                          title="复制"
                        >
                          <i class="bi bi-clipboard"></i>
                        </button>
                        <button 
                          @click="saveContent(content)" 
                          class="btn btn-sm btn-primary"
                          title="保存到项目"
                        >
                          <i class="bi bi-save"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div v-if="generatedContents.length > 3" class="text-center mt-2">
                    <button @click="showAllContents = !showAllContents" class="btn btn-sm btn-link">
                      {{ showAllContents ? '收起' : `查看全部 ${generatedContents.length} 项` }}
                    </button>
                  </div>
                </div>
                
                <div v-else class="text-center text-muted">
                  <i class="bi bi-chat-dots fs-1 opacity-25"></i>
                  <p class="mt-2">暂无生成内容</p>
                  <p class="small">与AI助手对话开始创作吧！</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 使用统计 -->
          <div class="usage-stats-card mt-3">
            <div class="card">
              <div class="card-header">
                <h5>📊 使用统计</h5>
              </div>
              <div class="card-body">
                <div class="stats-grid">
                  <div class="stat-box">
                    <div class="stat-number">{{ usageStats.totalChats }}</div>
                    <div class="stat-label">对话次数</div>
                  </div>
                  <div class="stat-box">
                    <div class="stat-number">{{ usageStats.totalGenerated }}</div>
                    <div class="stat-label">生成内容</div>
                  </div>
                  <div class="stat-box">
                    <div class="stat-number">{{ usageStats.totalWords }}</div>
                    <div class="stat-label">生成字数</div>
                  </div>
                  <div class="stat-box">
                    <div class="stat-number">{{ usageStats.avgQuality }}%</div>
                    <div class="stat-label">平均质量</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import { unifiedAIAssistantApi } from '@/api/unified-ai-assistant'
import { projectsApi } from '@/api/projects'
import UnifiedAIAssistant from '@/components/ai/UnifiedAIAssistant.vue'

const route = useRoute()
const appStore = useAppStore()

// 状态
const projectId = ref(route.params.projectId)
const projectInfo = ref(null)
const capabilities = ref(null)
const generatedContents = ref([])
const showAllContents = ref(false)
const usageStats = ref({
  totalChats: 0,
  totalGenerated: 0,
  totalWords: 0,
  avgQuality: 0
})

// 计算属性
const displayedContents = computed(() => {
  return showAllContents.value ? generatedContents.value : generatedContents.value.slice(0, 3)
})

// 方法
const formatWordCount = (count) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万`
  }
  return count.toString()
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const onContentGenerated = (data) => {
  // 添加到生成内容列表
  generatedContents.value.unshift({
    content: data.content,
    quality_score: data.quality_score || 0,
    timestamp: new Date(),
    id: Date.now()
  })

  // 更新统计
  usageStats.value.totalGenerated++
  usageStats.value.totalWords += data.content.length
  
  if (data.quality_score) {
    const totalQuality = usageStats.value.avgQuality * (usageStats.value.totalGenerated - 1) + (data.quality_score * 100)
    usageStats.value.avgQuality = Math.round(totalQuality / usageStats.value.totalGenerated)
  }

  appStore.showSuccess('内容已生成并添加到列表')
}

const onSuggestionApplied = (suggestion) => {
  usageStats.value.totalChats++
  appStore.showInfo(`已应用建议: ${suggestion}`)
}

const copyContent = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    appStore.showSuccess('内容已复制到剪贴板')
  } catch (error) {
    appStore.showError('复制失败')
  }
}

const saveContent = async (contentItem) => {
  try {
    // 这里可以实现保存到项目的逻辑
    // 例如保存为草稿、添加到章节等
    appStore.showSuccess('内容已保存到项目')
  } catch (error) {
    appStore.showError('保存失败')
  }
}

// 初始化
onMounted(async () => {
  try {
    // 加载项目信息
    const projectResponse = await projectsApi.getProject(projectId.value)
    projectInfo.value = projectResponse.data

    // 加载AI助手能力
    const capabilitiesResponse = await unifiedAIAssistantApi.getCapabilities(projectId.value)
    capabilities.value = capabilitiesResponse.data.capabilities

    // 初始化使用统计
    usageStats.value.totalChats = 0
    usageStats.value.totalGenerated = 0
    usageStats.value.totalWords = 0
    usageStats.value.avgQuality = 0

  } catch (error) {
    console.error('初始化失败:', error)
    appStore.showError('加载页面数据失败')
  }
})
</script>

<style scoped>
.ai-assistant-view {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 1rem 0;
}

.project-info-card .card,
.capabilities-card .card,
.generated-content-card .card,
.usage-stats-card .card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.project-stats {
  margin-top: 1rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.9rem;
}

.stat-value {
  font-weight: 600;
  color: #374151;
}

.capabilities-list {
  max-height: 300px;
  overflow-y: auto;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.mode-badges {
  margin-top: 0.5rem;
}

.ai-chat-container {
  height: calc(100vh - 2rem);
  overflow: hidden;
}

.content-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  background: white;
}

.content-preview {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #374151;
  margin-bottom: 0.5rem;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-time {
  font-size: 0.8rem;
  color: #6b7280;
}

.content-actions {
  display: flex;
  gap: 0.25rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-box {
  text-align: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
}

.stat-label {
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.25rem;
}
</style>
