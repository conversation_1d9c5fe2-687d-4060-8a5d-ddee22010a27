// https://d3js.org/d3-fetch/ v3.0.1 Copyright 2016-2021 Mike <PERSON>
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("d3-dsv")):"function"==typeof define&&define.amd?define(["exports","d3-dsv"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{},t.d3)}(this,(function(t,n){"use strict";function e(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.blob()}function r(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.arrayBuffer()}function o(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}function u(t,n){return fetch(t,n).then(o)}function f(t){return function(n,e,r){return 2===arguments.length&&"function"==typeof e&&(r=e,e=void 0),u(n,e).then((function(n){return t(n,r)}))}}var s=f(n.csvParse),i=f(n.tsvParse);function a(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);if(204!==t.status&&205!==t.status)return t.json()}function c(t){return(n,e)=>u(n,e).then((n=>(new DOMParser).parseFromString(n,t)))}var d=c("application/xml"),h=c("text/html"),l=c("image/svg+xml");t.blob=function(t,n){return fetch(t,n).then(e)},t.buffer=function(t,n){return fetch(t,n).then(r)},t.csv=s,t.dsv=function(t,e,r,o){3===arguments.length&&"function"==typeof r&&(o=r,r=void 0);var f=n.dsvFormat(t);return u(e,r).then((function(t){return f.parse(t,o)}))},t.html=h,t.image=function(t,n){return new Promise((function(e,r){var o=new Image;for(var u in n)o[u]=n[u];o.onerror=r,o.onload=function(){e(o)},o.src=t}))},t.json=function(t,n){return fetch(t,n).then(a)},t.svg=l,t.text=u,t.tsv=i,t.xml=d,Object.defineProperty(t,"__esModule",{value:!0})}));
