#!/usr/bin/env python3
"""
测试向量化和角色提取流程
"""

import asyncio
import requests
import json
import sys
from datetime import datetime

BASE_URL = "http://localhost:8000"
TEST_PROJECT_ID = "63ae07ad-2bc6-4354-92a9-db4be8160215"

# 测试章节内容
TEST_CHAPTER_CONTENT = """
第一章：穿越异世界

李明是一个25岁的程序员，平时性格内向但很坚韧。这天晚上，他正在加班写代码，突然一阵眩晕袭来。

当他再次睁开眼睛时，发现自己躺在一片陌生的森林里。周围古木参天，空气中弥漫着神秘的魔法气息。

"年轻人，你醒了。"一个慈祥的声音传来。

李明转头看去，只见一位白发苍苍的老者站在不远处。老者身穿深蓝色的法师袍，手持一根古老的法杖，眼神深邃而智慧。

"我是墨老，"老者自我介绍道，"你是从另一个世界来的吧？"

李明惊讶地点点头。墨老微笑着说："不用害怕，我可以教你这个世界的魔法。但首先，你需要了解这里的规则。"

就在这时，一道黑影从树林中闪出。那是一个全身黑衣的刺客，面戴面具，只露出一双充满仇恨的眼睛。

"暗影！"墨老警惕地握紧法杖，"你又来了。"

暗影冷冷地说："墨老，今天就是你的死期！我要为我的师父报仇！"

李明看着眼前的对峙，心中既紧张又好奇。他意识到，自己已经卷入了一个充满魔法和冲突的世界。
"""

def test_chapter_vectorization():
    """测试章节向量化"""
    print("🧪 测试章节向量化...")
    
    endpoint = f"{BASE_URL}/api/v1/projects/{TEST_PROJECT_ID}/chapters/vectorize"
    
    payload = {
        "chapter_id": "test-chapter-001",
        "title": "第一章：穿越异世界",
        "content": TEST_CHAPTER_CONTENT,
        "use_llm": True,
        "enable_multi_dimension": True,
        "force": True
    }
    
    try:
        print(f"📡 发送向量化请求...")
        response = requests.post(endpoint, json=payload, timeout=60)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 向量化成功!")
            print(f"📝 处理时间: {result.get('processing_time', 'N/A')} 秒")
            
            # 检查是否提取了角色
            structured_data = result.get('structured_data', {})
            characters = structured_data.get('characters', [])
            print(f"👥 提取的角色数量: {len(characters)}")
            
            for i, char in enumerate(characters):
                print(f"   {i+1}. {char.get('name', 'Unknown')}")
                print(f"      描述: {char.get('description', 'N/A')[:50]}...")
                print(f"      重要性: {char.get('importance', 'N/A')}")
            
            return True
        else:
            print(f"❌ 向量化失败!")
            print(f"   错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 向量化测试失败: {e}")
        return False

def test_character_list():
    """测试角色列表获取"""
    print("\n🧪 测试角色列表获取...")
    
    # 测试基础角色列表
    basic_endpoint = f"{BASE_URL}/api/v1/projects/{TEST_PROJECT_ID}/characters"
    
    try:
        print(f"📡 获取基础角色列表...")
        response = requests.get(basic_endpoint, timeout=10)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            characters = response.json()
            print(f"✅ 基础角色列表获取成功! 角色数量: {len(characters)}")
            
            for char in characters:
                print(f"   - {char.get('name', 'Unknown')} ({char.get('role', 'N/A')})")
        else:
            print(f"❌ 基础角色列表获取失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 基础角色列表测试失败: {e}")
    
    # 测试高级角色列表
    advanced_endpoint = f"{BASE_URL}/api/v1/characters/{TEST_PROJECT_ID}"
    
    try:
        print(f"\n📡 获取高级角色列表...")
        response = requests.get(advanced_endpoint, timeout=10)
        
        print(f"📊 状态码: {response.status_code}")
        
        if response.status_code == 200:
            characters = response.json()
            print(f"✅ 高级角色列表获取成功! 角色数量: {len(characters)}")
            
            for char in characters:
                print(f"   - {char.get('name', 'Unknown')}")
                print(f"     重要性分数: {char.get('importance_score', 'N/A')}")
                print(f"     性格标签: {char.get('personality_tags', [])}")
                print(f"     出场章节: {char.get('total_chapters', 'N/A')}")
                
            return len(characters) > 0
        else:
            print(f"❌ 高级角色列表获取失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 高级角色列表测试失败: {e}")
        return False

def test_character_search():
    """测试角色搜索"""
    print("\n🧪 测试角色搜索...")
    
    search_endpoint = f"{BASE_URL}/api/v1/characters/{TEST_PROJECT_ID}/search"
    
    test_queries = ["李明", "墨老", "程序员", "魔法"]
    
    for query in test_queries:
        try:
            print(f"📡 搜索关键词: {query}")
            response = requests.get(
                search_endpoint, 
                params={"query": query, "search_type": "all"}, 
                timeout=10
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ 搜索成功! 找到 {len(results)} 个结果")
                
                for result in results:
                    char = result.get('character', {})
                    score = result.get('match_score', 0)
                    reasons = result.get('match_reasons', [])
                    print(f"   - {char.get('name', 'Unknown')} (分数: {score})")
                    print(f"     匹配原因: {', '.join(reasons)}")
            else:
                print(f"❌ 搜索失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 搜索测试失败: {e}")

def test_character_detail():
    """测试角色详情获取"""
    print("\n🧪 测试角色详情获取...")
    
    # 先获取角色列表
    list_endpoint = f"{BASE_URL}/api/v1/characters/{TEST_PROJECT_ID}"
    
    try:
        response = requests.get(list_endpoint, timeout=10)
        
        if response.status_code == 200:
            characters = response.json()
            
            if characters:
                # 测试第一个角色的详情
                first_char = characters[0]
                char_name = first_char['name']
                
                detail_endpoint = f"{BASE_URL}/api/v1/characters/{TEST_PROJECT_ID}/{char_name}/detail"
                
                print(f"📡 获取角色详情: {char_name}")
                detail_response = requests.get(detail_endpoint, timeout=10)
                
                if detail_response.status_code == 200:
                    detail = detail_response.json()
                    print(f"✅ 角色详情获取成功!")
                    
                    basic_info = detail.get('basic_info', {})
                    print(f"   基础信息: {basic_info.get('name')} - {basic_info.get('description', '')[:50]}...")
                    
                    personality = detail.get('personality', {})
                    traits = personality.get('traits', [])
                    print(f"   性格特征: {traits}")
                    
                    stats = detail.get('statistics', {})
                    print(f"   统计信息: 重要性分数 {stats.get('importance_score', 'N/A')}")
                    
                else:
                    print(f"❌ 角色详情获取失败: {detail_response.text}")
            else:
                print("📝 没有角色可以测试详情")
        else:
            print(f"❌ 无法获取角色列表进行详情测试")
            
    except Exception as e:
        print(f"❌ 角色详情测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试向量化和角色提取流程")
    print(f"🎯 测试项目ID: {TEST_PROJECT_ID}")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试步骤
    print("\n" + "="*60)
    print("第一步：测试章节向量化（包含角色提取）")
    print("="*60)
    
    vectorization_success = test_chapter_vectorization()
    
    print("\n" + "="*60)
    print("第二步：测试角色列表获取")
    print("="*60)
    
    character_list_success = test_character_list()
    
    print("\n" + "="*60)
    print("第三步：测试角色搜索")
    print("="*60)
    
    test_character_search()
    
    print("\n" + "="*60)
    print("第四步：测试角色详情")
    print("="*60)
    
    test_character_detail()
    
    # 总结
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    if vectorization_success:
        print("✅ 向量化和角色提取：成功")
    else:
        print("❌ 向量化和角色提取：失败")
    
    if character_list_success:
        print("✅ 角色列表获取：成功")
    else:
        print("❌ 角色列表获取：失败")
    
    print("\n💡 如果测试失败，请检查：")
    print("   1. 后端服务是否正常运行")
    print("   2. 数据库连接是否正常")
    print("   3. LLM服务是否可用")
    print("   4. 项目ID是否正确")

if __name__ == "__main__":
    main()
