#!/usr/bin/env python3
"""
调试角色查找问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.character_storage_service import character_storage_service
from app.storage.memory_storage import storage

def debug_character_lookup():
    """调试角色查找"""
    print("🔍 调试角色查找")
    print("=" * 50)
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    character_name = "林深"
    
    print(f"项目ID: {project_id}")
    print(f"查找角色: {character_name}")
    
    try:
        # 1. 直接查询数据库
        print("\n1. 直接查询数据库:")
        all_characters = storage.get_all('characters')
        print(f"   数据库中总角色数: {len(all_characters)}")
        
        project_characters = [c for c in all_characters if c.get('project_id') == project_id]
        print(f"   项目中角色数: {len(project_characters)}")
        
        if project_characters:
            print("   项目角色列表:")
            for i, char in enumerate(project_characters, 1):
                name = char.get('name', 'Unknown')
                print(f"     {i}. '{name}' (原始: {repr(name)})")
                print(f"        ID: {char.get('id', 'N/A')}")
                print(f"        项目ID: {char.get('project_id', 'N/A')}")
        
        # 2. 使用服务方法查询
        print("\n2. 使用服务方法查询:")
        characters_by_service = character_storage_service.get_project_characters(project_id)
        print(f"   服务返回角色数: {len(characters_by_service)}")
        
        # 3. 按名称查找
        print(f"\n3. 按名称查找 '{character_name}':")
        found_character = character_storage_service.get_character_by_name(project_id, character_name)
        
        if found_character:
            print("   ✅ 找到角色:")
            print(f"     ID: {found_character.get('id', 'N/A')}")
            print(f"     名称: '{found_character.get('name', 'N/A')}'")
            print(f"     项目ID: {found_character.get('project_id', 'N/A')}")
        else:
            print("   ❌ 未找到角色")
            
            # 尝试不同的查找方式
            print("\n   尝试其他查找方式:")
            for char in project_characters:
                char_name = char.get('name', '')
                print(f"     比较: '{char_name}' vs '{character_name}'")
                print(f"       小写比较: '{char_name.lower()}' vs '{character_name.lower()}'")
                print(f"       相等: {char_name.lower() == character_name.lower()}")
                
                # 尝试去除空白字符
                char_name_stripped = char_name.strip()
                character_name_stripped = character_name.strip()
                print(f"       去空白比较: '{char_name_stripped}' vs '{character_name_stripped}'")
                print(f"       去空白相等: {char_name_stripped.lower() == character_name_stripped.lower()}")
        
        # 4. 测试URL编码问题
        print(f"\n4. 测试URL编码:")
        import urllib.parse
        
        encoded_name = urllib.parse.quote(character_name)
        decoded_name = urllib.parse.unquote(encoded_name)
        
        print(f"   原始名称: '{character_name}'")
        print(f"   URL编码: '{encoded_name}'")
        print(f"   URL解码: '{decoded_name}'")
        print(f"   编码解码一致: {character_name == decoded_name}")
        
        # 尝试用解码后的名称查找
        found_with_decoded = character_storage_service.get_character_by_name(project_id, decoded_name)
        print(f"   用解码名称查找: {'找到' if found_with_decoded else '未找到'}")
        
        return found_character is not None
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_lookup():
    """手动测试查找逻辑"""
    print("\n🧪 手动测试查找逻辑")
    print("=" * 50)
    
    project_id = "63ae07ad-2bc6-4354-92a9-db4be8160215"
    character_name = "林深"
    
    try:
        # 手动实现查找逻辑
        characters = storage.get_by_field('characters', 'project_id', project_id)
        print(f"查询到 {len(characters)} 个角色")
        
        for i, char in enumerate(characters):
            name = char.get('name', '')
            print(f"角色 {i+1}: '{name}'")
            print(f"  名称长度: {len(name)}")
            print(f"  名称字节: {name.encode('utf-8')}")
            print(f"  与目标比较: {name.lower() == character_name.lower()}")
            
            if name.lower() == character_name.lower():
                print(f"  ✅ 匹配成功！")
                return char
        
        print("❌ 没有找到匹配的角色")
        return None
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return None

if __name__ == "__main__":
    print("🔧 角色查找调试工具")
    print("=" * 60)
    
    # 调试角色查找
    success = debug_character_lookup()
    
    # 手动测试
    manual_result = test_manual_lookup()
    
    if success or manual_result:
        print("\n✅ 角色查找功能正常")
    else:
        print("\n❌ 角色查找存在问题，需要进一步调试")
