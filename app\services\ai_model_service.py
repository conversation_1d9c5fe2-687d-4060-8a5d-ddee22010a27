"""
AI模型服务层
"""

import time
import random
import requests
import subprocess
import os
import json
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.models.ai_model import (
    AIModel, AIModelCreate, AIModelUpdate, AIModelResponse, AIModelListItem,
    AIModelTestRequest, AIModelTestResponse, AIGenerationRequest, AIGenerationResponse
)
from app.storage.memory_storage import storage

logger = logging.getLogger(__name__)


class AIModelService:
    """AI模型服务"""

    def __init__(self):
        self.table = 'ai_models'
        # 远程Ollama服务器配置
        self.remote_ollama_servers = [
            {
                'name': '腾讯云服务器',
                'host': '*************',
                'port': 6399,
                'base_url': 'http://*************:6399',
                'enabled': True,
                'description': '腾讯云GPU服务器，提供高性能AI模型推理'
            }
        ]
    
    def create_ai_model(self, model_data: AIModelCreate) -> AIModelResponse:
        """创建AI模型"""
        # 检查模型名称是否已存在
        existing_models = storage.get_all(self.table)
        model_names = [model.get('name') for model in existing_models]
        
        if model_data.name in model_names:
            raise ValueError(f"AI模型名称 '{model_data.name}' 已存在")
        
        # 创建模型数据
        model_dict = {
            'name': model_data.name,
            'model_type': model_data.model_type.value,
            'description': model_data.description,
            'version': model_data.version,
            'status': 'offline',  # 默认离线状态
            'config': model_data.config,
            'endpoint': model_data.endpoint,
            'api_key': model_data.api_key,
            'parameters': model_data.parameters,
            'capabilities': [],
            'performance': None
        }
        
        # 保存到存储
        created_model = storage.create(self.table, model_dict)
        
        return AIModelResponse(**created_model)
    
    def get_ai_model(self, model_id: str) -> Optional[AIModelResponse]:
        """获取AI模型详情"""
        model = storage.get(self.table, model_id)
        if not model:
            return None
        
        return AIModelResponse(**model)
    
    def get_ai_models(self) -> List[AIModelListItem]:
        """获取所有AI模型"""
        models = storage.get_all(self.table)

        # 按创建时间排序
        models.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        return [AIModelListItem(**model) for model in models]

    def list_ai_models(self) -> List[AIModelListItem]:
        """获取所有AI模型（别名方法）"""
        return self.get_ai_models()
    
    def get_ollama_models(self) -> List[AIModelListItem]:
        """获取Ollama模型列表（包括本地和远程服务器）"""
        all_models = []

        # 获取本地Ollama模型
        local_models = self._get_local_ollama_models()
        all_models.extend(local_models)

        # 获取远程Ollama模型
        remote_models = self.get_remote_ollama_models()
        all_models.extend(remote_models)

        # 如果没有获取到任何模型，返回预设模型
        if not all_models:
            all_models = self._get_fallback_ollama_models()

        return all_models

    def _get_local_ollama_models(self) -> List[AIModelListItem]:
        """获取本地Ollama模型列表"""
        try:
            response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if response.status_code == 200:
                models_data = response.json().get('models', [])
                ollama_models = []

                for model in models_data:
                    model_name = model['name']
                    size_bytes = model.get('size', 0)
                    size_gb = round(size_bytes / (1024**3), 1) if size_bytes > 0 else 0

                    # 检查是否已在存储中
                    existing = storage.get_by_field(self.table, 'name', f"[本地] {model_name}")
                    if existing:
                        # 更新现有模型的状态
                        model_dict = existing[0].copy()
                        model_dict['status'] = 'available'
                        model_dict['size'] = size_bytes
                        model_dict['last_tested'] = datetime.now().isoformat()
                        storage.update(self.table, model_dict['id'], model_dict)
                        ollama_models.append(AIModelListItem(**model_dict))
                    else:
                        # 创建新的模型记录
                        model_dict = {
                            'name': f"[本地] {model_name}",
                            'model_type': 'ollama',
                            'status': 'available',
                            'description': f'本地Ollama模型: {model_name} ({size_gb}GB)',
                            'size': size_bytes,
                            'version': model.get('digest', '')[:12] if model.get('digest') else '',
                            'endpoint': 'http://localhost:11434',
                            'config': {
                                'is_remote': False,
                                'original_name': model_name
                            },
                            'capabilities': ['文本生成', '对话', '创意写作', '章节分析'],
                            'performance': 85.0,
                            'last_tested': datetime.now().isoformat(),
                            'created_at': datetime.now().isoformat()
                        }
                        created_model = storage.create(self.table, model_dict)
                        ollama_models.append(AIModelListItem(**created_model))

                logger.info(f"从本地Ollama API获取到 {len(ollama_models)} 个模型")
                return ollama_models

        except Exception as e:
            logger.warning(f"无法连接到本地Ollama API: {e}")

        return []

    def _get_fallback_ollama_models(self) -> List[AIModelListItem]:

        # 如果API不可用，返回预设的已知模型或存储中的模型
        stored_models = storage.get_by_field(self.table, 'model_type', 'ollama')

        # 如果存储中没有模型，创建预设的已知模型
        if not stored_models:
            logger.info("创建预设的Ollama模型列表")
            known_models = [
                {
                    'name': 'qwen3:4b',
                    'model_type': 'ollama',
                    'status': 'offline',  # Ollama服务不可用时标记为离线
                    'description': 'Qwen3 4B模型 - 轻量级中文大语言模型',
                    'size': 2600000000,  # 2.6GB
                    'capabilities': ['文本生成', '对话', '创意写作', '章节分析'],
                    'performance': 85.0,
                    'created_at': datetime.now().isoformat()
                },
                {
                    'name': 'qwen3:14b',
                    'model_type': 'ollama',
                    'status': 'offline',
                    'description': 'Qwen3 14B模型 - 高性能中文大语言模型',
                    'size': 9300000000,  # 9.3GB
                    'capabilities': ['文本生成', '对话', '创意写作', '章节分析'],
                    'performance': 90.0,
                    'created_at': datetime.now().isoformat()
                },
                {
                    'name': 'myOwnQwen14b:latest',
                    'model_type': 'ollama',
                    'status': 'offline',
                    'description': '自定义Qwen 14B模型',
                    'size': 9300000000,
                    'capabilities': ['文本生成', '对话', '创意写作', '章节分析'],
                    'performance': 88.0,
                    'created_at': datetime.now().isoformat()
                }
            ]

            ollama_models = []
            for model_dict in known_models:
                created_model = storage.create(self.table, model_dict)
                ollama_models.append(AIModelListItem(**created_model))

            return ollama_models

        # 更新存储中模型的状态为offline（因为无法连接API）
        for model in stored_models:
            if model.get('status') == 'available':
                model['status'] = 'offline'
                storage.update(self.table, model['id'], model)

        return [AIModelListItem(**model) for model in stored_models]

    def get_remote_ollama_models(self) -> List[AIModelListItem]:
        """获取远程Ollama服务器模型列表"""
        all_remote_models = []

        for server in self.remote_ollama_servers:
            if not server.get('enabled', True):
                continue

            try:
                # 检查远程Ollama服务
                tags_url = f"{server['base_url']}/api/tags"
                response = requests.get(tags_url, timeout=10)

                if response.status_code == 200:
                    data = response.json()

                    for model in data.get('models', []):
                        model_name = model['name']
                        size_bytes = model.get('size', 0)
                        size_gb = round(size_bytes / (1024**3), 1) if size_bytes > 0 else 0

                        # 为远程模型创建唯一ID
                        remote_model_id = f"remote_{server['name']}_{model_name}".replace(' ', '_')

                        # 检查是否已在存储中
                        existing = storage.get_by_field(self.table, 'name', f"[{server['name']}] {model_name}")
                        if existing:
                            # 更新现有模型的状态
                            model_dict = existing[0].copy()
                            model_dict['status'] = 'available'
                            model_dict['size'] = size_bytes
                            model_dict['last_tested'] = datetime.now().isoformat()
                            storage.update(self.table, model_dict['id'], model_dict)
                            all_remote_models.append(AIModelListItem(**model_dict))
                        else:
                            # 创建新的远程模型记录
                            model_dict = {
                                'name': f"[{server['name']}] {model_name}",
                                'model_type': 'ollama',
                                'status': 'available',
                                'description': f"{server['description']} - {model_name} ({size_gb}GB)",
                                'size': size_bytes,
                                'version': model.get('digest', '')[:12] if model.get('digest') else '',
                                'endpoint': server['base_url'],
                                'config': {
                                    'server_name': server['name'],
                                    'server_host': server['host'],
                                    'server_port': server['port'],
                                    'is_remote': True
                                },
                                'capabilities': ['文本生成', '对话', '创意写作', '章节分析'],
                                'performance': 90.0,  # 远程服务器通常性能更好
                                'last_tested': datetime.now().isoformat(),
                                'created_at': datetime.now().isoformat()
                            }
                            created_model = storage.create(self.table, model_dict)
                            all_remote_models.append(AIModelListItem(**created_model))

                    logger.info(f"成功获取远程服务器 {server['name']} 的 {len(data.get('models', []))} 个模型")
                else:
                    logger.warning(f"远程Ollama服务器 {server['name']} 响应异常: {response.status_code}")

            except requests.exceptions.RequestException as e:
                logger.warning(f"无法连接到远程Ollama服务器 {server['name']}: {e}")

        return all_remote_models

    def get_huggingface_models(self) -> List[AIModelListItem]:
        """获取Hugging Face模型列表"""
        # 检查本地Hugging Face模型目录
        hf_models_dir = "D:/Users/<USER>/Models/HuggingFace/Qwen"
        hf_models = []

        try:
            if os.path.exists(hf_models_dir):
                for item in os.listdir(hf_models_dir):
                    item_path = os.path.join(hf_models_dir, item)
                    if os.path.isdir(item_path):
                        config_path = os.path.join(item_path, "config.json")
                        if os.path.exists(config_path):
                            # 检查是否已在存储中
                            existing = storage.get_by_field(self.table, 'name', item)
                            if existing:
                                hf_models.append(AIModelListItem(**existing[0]))
                            else:
                                # 读取模型配置
                                try:
                                    with open(config_path, 'r', encoding='utf-8') as f:
                                        config = json.load(f)

                                    model_dict = {
                                        'name': item,
                                        'model_type': 'huggingface',
                                        'status': 'available',
                                        'description': f'Hugging Face模型: {item}',
                                        'version': config.get('model_type', 'unknown'),
                                        'capabilities': ['文本生成', '对话', '创意写作'],
                                        'performance': 80.0,
                                        'config': {'path': item_path, 'vocab_size': config.get('vocab_size', 0)},
                                        'last_tested': datetime.now().isoformat()
                                    }
                                    created_model = storage.create(self.table, model_dict)
                                    hf_models.append(AIModelListItem(**created_model))
                                except Exception as e:
                                    logger.warning(f"读取模型配置失败 {item}: {e}")
        except Exception as e:
            logger.warning(f"扫描Hugging Face模型目录失败: {e}")

        # 如果没有找到本地模型，返回存储中的模型
        if not hf_models:
            models = storage.get_by_field(self.table, 'model_type', 'huggingface')
            hf_models = [AIModelListItem(**model) for model in models]

        return hf_models
    
    def update_ai_model(self, model_id: str, model_data: AIModelUpdate) -> Optional[AIModelResponse]:
        """更新AI模型"""
        model = storage.get(self.table, model_id)
        if not model:
            return None
        
        # 准备更新数据
        update_data = {}
        
        if model_data.name is not None:
            # 检查新名称是否与其他模型冲突
            existing_models = storage.get_all(self.table)
            for m in existing_models:
                if m['id'] != model_id and m.get('name') == model_data.name:
                    raise ValueError(f"AI模型名称 '{model_data.name}' 已存在")
            update_data['name'] = model_data.name
        
        # 更新其他字段
        fields = [
            'description', 'version', 'status', 'config', 
            'endpoint', 'api_key', 'parameters'
        ]
        
        for field in fields:
            value = getattr(model_data, field, None)
            if value is not None:
                if hasattr(value, 'value'):  # 枚举类型
                    update_data[field] = value.value
                else:
                    update_data[field] = value
        
        # 更新模型
        updated_model = storage.update(self.table, model_id, update_data)
        
        if updated_model:
            return AIModelResponse(**updated_model)
        
        return None
    
    def delete_ai_model(self, model_id: str) -> bool:
        """删除AI模型"""
        return storage.delete(self.table, model_id)
    
    def test_ai_model(self, model_id: str, test_request: AIModelTestRequest) -> AIModelTestResponse:
        """测试AI模型"""
        model = storage.get(self.table, model_id)
        if not model:
            raise ValueError("AI模型不存在")

        start_time = time.time()

        try:
            model_type = model.get('model_type', '')
            model_name = model.get('name', '')

            if model_type == 'ollama':
                # 测试Ollama模型
                response_text = self._test_ollama_model(model_name, test_request.prompt)
                response_time = time.time() - start_time

                # 更新模型状态和性能
                update_data = {
                    'status': 'available',
                    'last_tested': datetime.now().isoformat(),
                    'performance': self._calculate_performance(response_time, len(response_text))
                }
                storage.update(self.table, model_id, update_data)

                return AIModelTestResponse(
                    success=True,
                    response_text=response_text,
                    response_time=response_time,
                    metadata={
                        'model_name': model_name,
                        'model_type': model_type,
                        'prompt_length': len(test_request.prompt),
                        'response_length': len(response_text),
                        'tokens_per_second': len(response_text.split()) / max(response_time, 0.1)
                    }
                )

            elif model_type == 'huggingface':
                # 测试Hugging Face模型
                response_text = self._test_huggingface_model(model_name, test_request.prompt)
                response_time = time.time() - start_time

                update_data = {
                    'status': 'available',
                    'last_tested': datetime.now().isoformat(),
                    'performance': self._calculate_performance(response_time, len(response_text))
                }
                storage.update(self.table, model_id, update_data)

                return AIModelTestResponse(
                    success=True,
                    response_text=response_text,
                    response_time=response_time,
                    metadata={
                        'model_name': model_name,
                        'model_type': model_type,
                        'prompt_length': len(test_request.prompt),
                        'response_length': len(response_text)
                    }
                )
            else:
                # 其他类型模型使用模拟测试
                response_text = self._simulate_ai_response(test_request.prompt, model)
                response_time = time.time() - start_time

                return AIModelTestResponse(
                    success=True,
                    response_text=response_text,
                    response_time=response_time,
                    metadata={
                        'model_name': model_name,
                        'model_type': model_type,
                        'prompt_length': len(test_request.prompt),
                        'response_length': len(response_text),
                        'note': '模拟测试'
                    }
                )

        except Exception as e:
            # 更新模型状态为离线
            storage.update(self.table, model_id, {
                'status': 'offline',
                'last_tested': datetime.now().isoformat()
            })

            return AIModelTestResponse(
                success=False,
                error_message=f"测试失败: {str(e)}"
            )
    
    def generate_content(self, generation_request: AIGenerationRequest) -> AIGenerationResponse:
        """AI内容生成"""
        start_time = time.time()

        try:
            # 获取默认可用的模型
            available_models = self.get_ai_models()
            if not available_models:
                return AIGenerationResponse(
                    success=False,
                    error_message="没有可用的AI模型"
                )

            # 选择第一个可用的模型
            selected_model = None
            for model in available_models:
                if model.status == 'available':
                    selected_model = model
                    break

            if not selected_model:
                return AIGenerationResponse(
                    success=False,
                    error_message="没有可用状态的AI模型"
                )

            # 根据模型类型调用相应的生成方法
            if selected_model.model_type == 'ollama':
                generated_content = self._generate_with_ollama(
                    selected_model.name,
                    generation_request
                )
            elif selected_model.model_type == 'huggingface':
                generated_content = self._generate_with_huggingface(
                    selected_model.name,
                    generation_request
                )
            else:
                # 回退到模拟生成
                generated_content = self._simulate_content_generation(generation_request)

            generation_time = time.time() - start_time

            return AIGenerationResponse(
                success=True,
                generated_content=generated_content,
                generation_time=generation_time,
                tokens_used=len(generated_content.split()),
                metadata={
                    'model_name': selected_model.name,
                    'model_type': selected_model.model_type,
                    'generation_type': generation_request.generation_type,
                    'prompt_length': len(generation_request.prompt),
                    'content_length': len(generated_content)
                }
            )

        except Exception as e:
            return AIGenerationResponse(
                success=False,
                error_message=f"生成失败: {str(e)}"
            )
    
    def generate_project_content(self, project_id: str, generation_request: AIGenerationRequest) -> AIGenerationResponse:
        """为项目生成AI内容"""
        try:
            # 获取项目信息（使用文件存储）
            from app.services.project_service import ProjectService
            project_service = ProjectService()

            project = project_service.get_project(project_id)
            if not project:
                raise ValueError(f"项目 {project_id} 不存在")

            # 转换为字典格式
            project_dict = project.dict() if hasattr(project, 'dict') else project

            # 获取项目的AI模型配置
            ai_model_config = project_dict.get('ai_model_config', {})
            model_type = ai_model_config.get('type', 'ollama')
            model_name = ai_model_config.get('name', 'qwen3:4b')

            logger.info(f"项目 {project_dict['name']} 使用AI模型: {model_type}/{model_name}")

            # 确保context是字典
            context = generation_request.context if generation_request.context else {}

            # 添加项目上下文到生成请求
            enhanced_context = {
                'project_name': project_dict.get('name'),
                'project_genre': project_dict.get('genre'),
                'project_description': project_dict.get('description'),
                **context
            }

            # 构建增强的提示
            enhanced_prompt = self._build_enhanced_prompt(generation_request)

            # 根据项目配置的模型类型调用相应的生成方法
            if model_type == 'ollama':
                generated_content = self._generate_with_ollama_direct(
                    model_name,
                    enhanced_prompt,
                    generation_request
                )
            elif model_type == 'huggingface':
                generated_content = self._generate_with_huggingface(
                    model_name,
                    generation_request
                )
            else:
                raise ValueError(f"不支持的模型类型: {model_type}")

            # 解析AI响应
            parsed_response = self._parse_ai_response(generated_content)

            return AIGenerationResponse(
                success=True,
                generated_content=parsed_response['actual_content'],
                generation_time=1.0,
                tokens_used=len(parsed_response['actual_content']),
                metadata={
                    'model_type': model_type,
                    'model_name': model_name,
                    'has_think': parsed_response['has_think'],
                    'think_content': parsed_response['think_content'],
                    'full_response': parsed_response['full_response']
                }
            )

        except Exception as e:
            return AIGenerationResponse(
                success=False,
                error_message=f"生成项目内容失败: {str(e)}"
            )
    
    def refresh_models(self):
        """刷新AI模型列表"""
        # 这里应该实际检测可用的AI模型
        # 目前只是更新现有模型的状态
        models = storage.get_all(self.table)
        
        for model in models:
            # 模拟检测模型状态
            if model.get('model_type') == 'ollama':
                # 模拟检测Ollama模型
                new_status = random.choice(['available', 'offline'])
            elif model.get('model_type') == 'huggingface':
                # 模拟检测Hugging Face模型
                new_status = 'available'
            else:
                new_status = model.get('status', 'offline')
            
            storage.update(self.table, model['id'], {'status': new_status})
    
    def get_ai_model_stats(self) -> Dict[str, Any]:
        """获取AI模型统计信息"""
        models = storage.get_all(self.table)
        
        total_models = len(models)
        
        # 按类型统计
        type_counts = {}
        for model in models:
            model_type = model.get('model_type', 'unknown')
            type_counts[model_type] = type_counts.get(model_type, 0) + 1
        
        # 按状态统计
        status_counts = {}
        for model in models:
            status = model.get('status', 'offline')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 计算总大小
        total_size = sum(model.get('size', 0) for model in models)
        
        # 统计可用模型
        available_models = len([m for m in models if m.get('status') == 'available'])
        
        return {
            'total_models': total_models,
            'available_models': available_models,
            'type_counts': type_counts,
            'status_counts': status_counts,
            'total_size': total_size
        }
    
    def _test_ollama_model(self, model_name: str, prompt: str) -> str:
        """测试Ollama模型"""
        try:
            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': model_name,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': 0.7,
                        'num_predict': 100
                    }
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('response', '模型响应为空')
            else:
                raise Exception(f"Ollama API错误: {response.status_code}")

        except Exception as e:
            raise Exception(f"Ollama模型测试失败: {str(e)}")

    def _test_huggingface_model(self, model_name: str, prompt: str) -> str:
        """测试Hugging Face模型"""
        try:
            # 这里可以实现HuggingFace模型的测试逻辑
            return f"HuggingFace模型 {model_name} 测试成功，提示: {prompt[:50]}..."
        except Exception as e:
            raise Exception(f"HuggingFace模型测试失败: {str(e)}")

    def generate_text(self, prompt: str, max_tokens: int = 500, temperature: float = 0.7) -> str:
        """生成文本"""
        try:
            # 获取默认模型
            models = self.list_ai_models()
            ollama_models = [m for m in models if m.model_type == 'ollama' and m.status == 'available']

            if not ollama_models:
                # 如果没有可用的Ollama模型，返回模拟响应
                return f"基于提示词的智能回答：{prompt[:50]}..."

            # 使用第一个可用的Ollama模型
            model = ollama_models[0]
            model_name = self._get_real_ollama_model_name(model.name)

            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': model_name,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': temperature,
                        'num_predict': max_tokens
                    }
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('response', '生成失败')
            else:
                return f"模型调用失败: {response.status_code}"

        except Exception as e:
            logger.error(f"文本生成失败: {e}")
            return f"生成失败: {str(e)}"

    def _calculate_performance(self, response_time: float, response_length: int) -> float:
        """计算模型性能评分"""
        # 基于响应时间和内容长度计算性能评分
        if response_time <= 1.0:
            time_score = 100
        elif response_time <= 3.0:
            time_score = 80
        elif response_time <= 5.0:
            time_score = 60
        else:
            time_score = 40

        # 内容长度评分
        if response_length >= 50:
            length_score = 100
        elif response_length >= 20:
            length_score = 80
        else:
            length_score = 60

        return (time_score + length_score) / 2

    def _get_real_ollama_model_name(self, display_name: str) -> str:
        """获取真实的Ollama模型名称"""
        try:
            # 获取Ollama可用模型列表
            response = requests.get('http://localhost:11434/api/tags', timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                model_names = [model.get('name', '') for model in models]

                # 如果指定了具体的模型名称，直接使用
                if display_name and display_name in model_names:
                    return display_name

                # 如果没有指定或指定的模型不存在，按优先级选择
                priority_models = ['qwen3:4b', 'qwen3:14b', 'myOwnQwen14b:latest']
                for priority_model in priority_models:
                    if priority_model in model_names:
                        logger.info(f"自动选择优先模型: {priority_model}")
                        return priority_model

                # 如果优先模型都不存在，选择任何qwen模型
                for model_name in model_names:
                    if 'qwen' in model_name.lower():
                        logger.info(f"选择qwen模型: {model_name}")
                        return model_name

                # 如果没有qwen，使用第一个可用模型
                if model_names:
                    logger.info(f"使用第一个可用模型: {model_names[0]}")
                    return model_names[0]

            # 回退到默认模型名称
            logger.warning("无法获取模型列表，使用默认模型")
            return 'qwen3:4b'

        except Exception as e:
            logger.warning(f"获取Ollama模型列表失败: {e}")
            return 'qwen3:4b'

    def _parse_ai_response(self, raw_response: str) -> dict:
        """解析AI响应，分离think部分和实际内容"""
        try:
            # 检查是否包含think标签
            if '<think>' in raw_response and '</think>' in raw_response:
                # 提取think部分
                think_start = raw_response.find('<think>')
                think_end = raw_response.find('</think>') + 8
                think_content = raw_response[think_start:think_end]

                # 提取实际内容（去除think部分）
                actual_content = raw_response[think_end:].strip()

                return {
                    'has_think': True,
                    'think_content': think_content,
                    'actual_content': actual_content,
                    'full_response': raw_response
                }
            else:
                return {
                    'has_think': False,
                    'think_content': '',
                    'actual_content': raw_response.strip(),
                    'full_response': raw_response
                }

        except Exception as e:
            logger.error(f"解析AI响应失败: {e}")
            return {
                'has_think': False,
                'think_content': '',
                'actual_content': raw_response,
                'full_response': raw_response
            }

    def _parse_ai_response_streaming(self, raw_response: str) -> dict:
        """流式解析AI响应，保留think部分的实时显示"""
        try:
            # 检查是否包含think标签
            if '<think>' in raw_response:
                think_start = raw_response.find('<think>')

                # 检查是否有结束标签
                if '</think>' in raw_response:
                    # think部分已完成
                    think_end = raw_response.find('</think>') + 8
                    think_content = raw_response[think_start:think_end]
                    actual_content = raw_response[think_end:].strip()

                    return {
                        'has_think': True,
                        'think_content': think_content,
                        'actual_content': actual_content,
                        'in_think': False,
                        'full_response': raw_response
                    }
                else:
                    # think部分还在进行中
                    think_content = raw_response[think_start:]

                    return {
                        'has_think': True,
                        'think_content': think_content,
                        'actual_content': '',
                        'in_think': True,
                        'full_response': raw_response
                    }
            else:
                # 没有think部分
                return {
                    'has_think': False,
                    'think_content': '',
                    'actual_content': raw_response.strip(),
                    'in_think': False,
                    'full_response': raw_response
                }

        except Exception as e:
            logger.error(f"流式解析AI响应失败: {e}")
            return {
                'has_think': False,
                'think_content': '',
                'actual_content': raw_response,
                'in_think': False,
                'full_response': raw_response
            }

    def _generate_with_ollama(self, model_name: str, generation_request: AIGenerationRequest) -> str:
        """使用Ollama模型生成内容（支持本地和远程服务器）"""
        try:
            # 获取模型信息和端点
            endpoint, real_model_name = self._get_ollama_endpoint_and_model(model_name)
            logger.info(f"使用Ollama模型: {real_model_name} (显示名称: {model_name}) 端点: {endpoint}")

            # 构建针对不同生成类型的提示
            enhanced_prompt = self._build_enhanced_prompt(generation_request)

            # 调用Ollama API
            api_url = f"{endpoint}/api/generate"
            response = requests.post(
                api_url,
                json={
                    'model': real_model_name,
                    'prompt': enhanced_prompt,
                    'stream': False,
                    'options': {
                        'temperature': generation_request.temperature or 0.7,
                        'num_predict': generation_request.max_length or 1000
                    }
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                raw_response = result.get('response', '生成失败：无响应内容')

                # 解析响应，处理think部分
                parsed_response = self._parse_ai_response(raw_response)

                # 记录完整响应（包含think部分）
                logger.info(f"AI完整响应: {parsed_response['full_response'][:500]}...")

                # 返回实际内容（不包含think部分）
                return parsed_response['actual_content']
            else:
                logger.error(f"Ollama API错误: {response.status_code} - {response.text}")
                raise Exception(f"Ollama API调用失败: {response.status_code}")

        except Exception as e:
            logger.error(f"Ollama生成失败: {e}")
            # 不再回退到模拟生成，而是抛出异常
            raise Exception(f"AI生成失败: {str(e)}")

    def _get_ollama_endpoint_and_model(self, model_name: str) -> tuple[str, str]:
        """获取Ollama端点和真实模型名称"""
        # 查找模型信息
        models = storage.get_by_field(self.table, 'name', model_name)
        if models:
            model = models[0]
            endpoint = model.get('endpoint', 'http://localhost:11434')

            # 获取真实模型名称
            if model.get('config', {}).get('is_remote'):
                # 远程模型，从配置中获取原始名称
                real_name = model.get('config', {}).get('original_name')
                if not real_name:
                    # 从显示名称中提取，格式：[服务器名] 模型名
                    if '] ' in model_name:
                        real_name = model_name.split('] ', 1)[1]
                    else:
                        real_name = model_name
            else:
                # 本地模型，从配置中获取原始名称
                real_name = model.get('config', {}).get('original_name')
                if not real_name:
                    # 从显示名称中提取，格式：[本地] 模型名
                    if '] ' in model_name:
                        real_name = model_name.split('] ', 1)[1]
                    else:
                        real_name = model_name

            return endpoint, real_name

        # 如果找不到模型信息，使用默认值
        return 'http://localhost:11434', self._get_real_ollama_model_name(model_name)

    def _generate_with_ollama_direct(self, model_name: str, prompt: str, generation_request: AIGenerationRequest) -> str:
        """直接使用指定的Ollama模型名称生成内容（非流式）"""
        try:
            logger.info(f"直接调用Ollama模型: {model_name}")

            # 调用Ollama API
            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': model_name,
                    'prompt': prompt,
                    'stream': False,
                    'options': {
                        'temperature': generation_request.temperature or 0.7,
                        'num_predict': generation_request.max_length or 1000
                    }
                },
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                raw_response = result.get('response', '生成失败：无响应内容')

                logger.info(f"Ollama原始响应: {raw_response[:200]}...")
                return raw_response
            else:
                logger.error(f"Ollama API错误: {response.status_code} - {response.text}")
                raise Exception(f"Ollama API调用失败: {response.status_code}")

        except Exception as e:
            logger.error(f"Ollama直接调用失败: {e}")
            raise Exception(f"AI生成失败: {str(e)}")

    def generate_stream(self, model_name: str, prompt: str, generation_request: AIGenerationRequest):
        """流式生成内容"""
        try:
            logger.info(f"开始流式生成，模型: {model_name}")

            # 调用Ollama流式API
            response = requests.post(
                'http://localhost:11434/api/generate',
                json={
                    'model': model_name,
                    'prompt': prompt,
                    'stream': True,  # 启用流式输出
                    'options': {
                        'temperature': generation_request.temperature or 0.7,
                        'num_predict': (generation_request.max_length or 1000) * 2  # 增加生成长度
                    }
                },
                stream=True,  # 启用流式响应
                timeout=300  # 增加超时时间到5分钟
            )

            if response.status_code == 200:
                full_response = ""
                last_token_time = time.time()
                no_token_timeout = 30  # 增加到30秒没有新token才认为结束
                token_count = 0

                # 逐行处理流式响应
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk = json.loads(line.decode('utf-8'))
                            token = chunk.get('response', '')
                            done = chunk.get('done', False)

                            if token:
                                full_response += token
                                last_token_time = time.time()
                                token_count += 1

                                # 实时解析当前内容，保留think部分
                                current_parsed = self._parse_ai_response_streaming(full_response)

                                # 记录进度
                                if token_count % 50 == 0:  # 每50个token记录一次
                                    logger.info(f"已生成{token_count}个token，当前长度: {len(full_response)}")

                                # 发送流式数据
                                yield {
                                    'type': 'token',
                                    'content': token,
                                    'full_response': full_response,
                                    'actual_content': current_parsed['actual_content'],
                                    'think_content': current_parsed['think_content'],
                                    'has_think': current_parsed['has_think'],
                                    'in_think': current_parsed['in_think'],
                                    'done': done,
                                    'token_count': token_count
                                }

                            # 检查是否完成
                            if done:
                                logger.info(f"Ollama标记生成完成，总token数: {token_count}")
                                break

                            # 检查超时（防止卡死）
                            current_time = time.time()
                            if current_time - last_token_time > no_token_timeout:
                                logger.warning(f"超过{no_token_timeout}秒没有新token，自动结束。总token数: {token_count}")
                                break

                        except json.JSONDecodeError as e:
                            logger.warning(f"JSON解析失败: {e}")
                            continue

                # 最终解析和发送完成信号
                final_parsed = self._parse_ai_response(full_response)

                logger.info(f"流式生成完成，总长度: {len(full_response)}")
                logger.info(f"实际内容长度: {len(final_parsed['actual_content'])}")
                logger.info(f"Think内容长度: {len(final_parsed['think_content'])}")

                yield {
                    'type': 'complete',
                    'full_response': full_response,
                    'actual_content': final_parsed['actual_content'],
                    'think_content': final_parsed['think_content'],
                    'has_think': final_parsed['has_think'],
                    'done': True
                }

            else:
                logger.error(f"Ollama流式API错误: {response.status_code}")
                yield {
                    'type': 'error',
                    'error': f"API调用失败: {response.status_code}",
                    'done': True
                }

        except Exception as e:
            logger.error(f"流式生成失败: {e}")
            yield {
                'type': 'error',
                'error': f"生成失败: {str(e)}",
                'done': True
            }

    def _generate_with_huggingface(self, model_name: str, generation_request: AIGenerationRequest) -> str:
        """使用Hugging Face模型生成内容"""
        try:
            # 获取模型路径
            model_info = storage.get_by_field(self.table, 'name', model_name)
            if not model_info:
                return self._simulate_content_generation(generation_request)

            model_path = model_info[0].get('config', {}).get('path')
            if not model_path or not os.path.exists(model_path):
                logger.warning(f"Hugging Face模型路径不存在: {model_path}")
                return self._simulate_content_generation(generation_request)

            # 构建增强提示
            enhanced_prompt = self._build_enhanced_prompt(generation_request)

            # 调用Hugging Face推理脚本
            script_path = os.path.join(os.path.dirname(__file__), '..', '..', 'novel_lora_tool', 'hf_chat_inference.py')
            if os.path.exists(script_path):
                cmd = [
                    'python', script_path,
                    '--model_name', model_path,
                    '--prompt', enhanced_prompt,
                    '--max_new_tokens', str(generation_request.max_length or 512),
                    '--temperature', str(generation_request.temperature or 0.7),
                    '--quant_level', '4bit'
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=120,
                    encoding='utf-8'
                )

                if result.returncode == 0:
                    # 解析输出
                    output = result.stdout
                    if '---GENERATED_TEXT_START---' in output and '---GENERATED_TEXT_END---' in output:
                        start_idx = output.find('---GENERATED_TEXT_START---') + len('---GENERATED_TEXT_START---')
                        end_idx = output.find('---GENERATED_TEXT_END---')
                        generated_text = output[start_idx:end_idx].strip()
                        return generated_text
                    else:
                        return output.strip()
                else:
                    logger.error(f"Hugging Face推理失败: {result.stderr}")
                    return self._simulate_content_generation(generation_request)
            else:
                logger.warning("Hugging Face推理脚本不存在")
                return self._simulate_content_generation(generation_request)

        except Exception as e:
            logger.error(f"Hugging Face生成失败: {e}")
            return self._simulate_content_generation(generation_request)

    def _build_enhanced_prompt(self, generation_request: AIGenerationRequest) -> str:
        """构建增强的提示词"""
        base_prompt = generation_request.prompt
        generation_type = generation_request.generation_type
        context = generation_request.context or {}

        # 根据生成类型添加指导性提示
        if generation_type == 'worldbuilding':
            enhanced_prompt = f"""作为一个专业的小说世界观设计师，请根据以下要求创建详细的世界观设定：

{base_prompt}

请从以下几个方面进行详细描述：
1. 世界的基本设定和背景
2. 魔法/科技体系（如适用）
3. 社会结构和政治体系
4. 地理环境和重要地点
5. 历史背景和重要事件

请用生动、具体的语言描述，让读者能够清晰地想象这个世界。"""

        elif generation_type == 'character':
            enhanced_prompt = f"""作为一个专业的角色设计师，请根据以下要求创建详细的角色设定：

{base_prompt}

请包含以下要素：
1. 角色的基本信息（姓名、年龄、外貌）
2. 性格特点和行为习惯
3. 背景故事和成长经历
4. 技能、能力或特殊才能
5. 目标、动机和内心冲突
6. 与其他角色的关系

请创造一个立体、有深度的角色，避免刻板印象。"""

        elif generation_type == 'plot':
            enhanced_prompt = f"""作为一个专业的情节设计师，请根据以下要求创建引人入胜的情节：

{base_prompt}

请考虑以下要素：
1. 情节的起因和背景
2. 主要冲突和转折点
3. 角色的行动和反应
4. 情节的发展和高潮
5. 结果和后续影响

请确保情节逻辑合理，节奏紧凑，能够推动故事发展。"""

        elif generation_type == 'content':
            enhanced_prompt = f"""作为一个专业的小说作家，请根据以下要求创作精彩的小说内容：

{base_prompt}

请注意：
1. 使用生动的描写和对话
2. 保持角色的一致性
3. 营造适当的氛围和情感
4. 推进情节发展
5. 使用恰当的文学技巧

请创作出引人入胜、文笔流畅的小说片段。"""

        else:
            enhanced_prompt = base_prompt

        # 添加项目上下文信息
        if context:
            context_info = []
            if context.get('project_name'):
                context_info.append(f"项目名称：{context['project_name']}")
            if context.get('project_genre'):
                context_info.append(f"小说类型：{context['project_genre']}")
            if context.get('project_description'):
                context_info.append(f"项目描述：{context['project_description']}")

            if context_info:
                enhanced_prompt = f"""项目背景信息：
{chr(10).join(context_info)}

{enhanced_prompt}"""

        return enhanced_prompt

    def _simulate_ai_response(self, prompt: str, model: Dict[str, Any]) -> str:
        """模拟AI响应"""
        model_name = model.get('name', 'Unknown')
        
        responses = [
            f"这是来自 {model_name} 的测试响应。您的提示是: {prompt[:50]}...",
            f"{model_name} 模型运行正常。测试提示已收到并处理。",
            f"Hello! This is a test response from {model_name}. Your prompt: {prompt[:30]}...",
            f"{model_name} 正在正常工作。这是一个测试响应，用于验证模型连接。"
        ]
        
        return random.choice(responses)
    
    def _simulate_content_generation(self, request: AIGenerationRequest) -> str:
        """模拟内容生成"""
        generation_type = request.generation_type
        
        if generation_type == 'worldbuilding':
            return self._generate_worldbuilding_content(request)
        elif generation_type == 'character':
            return self._generate_character_content(request)
        elif generation_type == 'plot':
            return self._generate_plot_content(request)
        elif generation_type == 'content':
            return self._generate_story_content(request)
        else:
            return f"基于您的提示 '{request.prompt}' 生成的内容。这是一个模拟生成的示例文本。"
    
    def _generate_worldbuilding_content(self, request: AIGenerationRequest) -> str:
        """生成世界观内容"""
        templates = [
            "在这个世界中，魔法与科技并存。古老的法师塔矗立在现代都市的中心，而魔法师们使用着融合了魔法与科技的神奇装置。",
            "这是一个多元种族共存的世界。人类、精灵、矮人和兽人建立了联盟，共同对抗来自异次元的威胁。",
            "在遥远的未来，人类已经掌握了星际旅行技术。各个星球都有着独特的文明和生态系统。"
        ]
        return random.choice(templates)
    
    def _generate_character_content(self, request: AIGenerationRequest) -> str:
        """生成角色内容"""
        templates = [
            "这是一个神秘而强大的角色。他拥有着深邃的眼神和不为人知的过去，总是在关键时刻出现。",
            "她是一位年轻的魔法师，虽然天赋异禀，但性格冲动，经常因为好奇心而陷入麻烦。",
            "一个经验丰富的战士，曾经历过无数战斗。虽然外表粗犷，但内心善良，总是保护着弱者。"
        ]
        return random.choice(templates)
    
    def _generate_plot_content(self, request: AIGenerationRequest) -> str:
        """生成情节内容"""
        templates = [
            "故事从一个平凡的日子开始，主角意外发现了一个隐藏的秘密，这个发现将彻底改变他的命运。",
            "在一次危险的任务中，主角遇到了强大的敌人。通过智慧和勇气，他最终找到了克服困难的方法。",
            "当所有希望似乎都破灭时，一个意想不到的盟友出现了，为主角带来了新的可能性。"
        ]
        return random.choice(templates)
    
    def _generate_story_content(self, request: AIGenerationRequest) -> str:
        """生成故事内容"""
        templates = [
            "夜幕降临，城市的灯火开始闪烁。在这个看似平静的夜晚，一场改变一切的冒险即将开始...",
            "阳光透过古老的窗户洒进房间，尘埃在光束中飞舞。这里曾经发生过什么故事呢？",
            "远方传来了神秘的声音，那是来自另一个世界的呼唤。是否应该回应这个召唤呢？"
        ]
        return random.choice(templates)


# 全局AI模型服务实例
ai_model_service = AIModelService()
