// https://d3js.org/d3-axis/ v3.0.0 Copyright 2010-2021 <PERSON>
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t="undefined"!=typeof globalThis?globalThis:t||self).d3=t.d3||{})}(this,(function(t){"use strict";function n(t){return t}var e=1e-6;function r(t){return"translate("+t+",0)"}function i(t){return"translate(0,"+t+")"}function a(t){return n=>+t(n)}function o(t,n){return n=Math.max(0,t.bandwidth()-2*n)/2,t.round()&&(n=Math.round(n)),e=>+t(e)+n}function u(){return!this.__axis}function c(t,c){var l=[],s=null,f=null,d=6,m=6,h=3,p="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,g=1===t||4===t?-1:1,x=4===t||2===t?"x":"y",y=1===t||3===t?r:i;function k(r){var i=null==s?c.ticks?c.ticks.apply(c,l):c.domain():s,k=null==f?c.tickFormat?c.tickFormat.apply(c,l):n:f,M=Math.max(d,0)+h,_=c.range(),b=+_[0]+p,v=+_[_.length-1]+p,A=(c.bandwidth?o:a)(c.copy(),p),w=r.selection?r.selection():r,F=w.selectAll(".domain").data([null]),V=w.selectAll(".tick").data(i,c).order(),z=V.exit(),H=V.enter().append("g").attr("class","tick"),C=V.select("line"),P=V.select("text");F=F.merge(F.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),V=V.merge(H),C=C.merge(H.append("line").attr("stroke","currentColor").attr(x+"2",g*d)),P=P.merge(H.append("text").attr("fill","currentColor").attr(x,g*M).attr("dy",1===t?"0em":3===t?"0.71em":"0.32em")),r!==w&&(F=F.transition(r),V=V.transition(r),C=C.transition(r),P=P.transition(r),z=z.transition(r).attr("opacity",e).attr("transform",(function(t){return isFinite(t=A(t))?y(t+p):this.getAttribute("transform")})),H.attr("opacity",e).attr("transform",(function(t){var n=this.parentNode.__axis;return y((n&&isFinite(n=n(t))?n:A(t))+p)}))),z.remove(),F.attr("d",4===t||2===t?m?"M"+g*m+","+b+"H"+p+"V"+v+"H"+g*m:"M"+p+","+b+"V"+v:m?"M"+b+","+g*m+"V"+p+"H"+v+"V"+g*m:"M"+b+","+p+"H"+v),V.attr("opacity",1).attr("transform",(function(t){return y(A(t)+p)})),C.attr(x+"2",g*d),P.attr(x,g*M).text(k),w.filter(u).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===t?"start":4===t?"end":"middle"),w.each((function(){this.__axis=A}))}return k.scale=function(t){return arguments.length?(c=t,k):c},k.ticks=function(){return l=Array.from(arguments),k},k.tickArguments=function(t){return arguments.length?(l=null==t?[]:Array.from(t),k):l.slice()},k.tickValues=function(t){return arguments.length?(s=null==t?null:Array.from(t),k):s&&s.slice()},k.tickFormat=function(t){return arguments.length?(f=t,k):f},k.tickSize=function(t){return arguments.length?(d=m=+t,k):d},k.tickSizeInner=function(t){return arguments.length?(d=+t,k):d},k.tickSizeOuter=function(t){return arguments.length?(m=+t,k):m},k.tickPadding=function(t){return arguments.length?(h=+t,k):h},k.offset=function(t){return arguments.length?(p=+t,k):p},k}t.axisBottom=function(t){return c(3,t)},t.axisLeft=function(t){return c(4,t)},t.axisRight=function(t){return c(2,t)},t.axisTop=function(t){return c(1,t)},Object.defineProperty(t,"__esModule",{value:!0})}));
