{"hash": "e9e642bf", "configHash": "383e9241", "lockfileHash": "6d3b32ed", "browserHash": "8f970c7b", "optimized": {"axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "322d29f6", "needsInterop": false}, "bootstrap": {"src": "../../bootstrap/dist/js/bootstrap.esm.js", "file": "bootstrap.js", "fileHash": "<PERSON><PERSON><PERSON>", "needsInterop": false}, "bootstrap/dist/js/bootstrap.bundle.min.js": {"src": "../../bootstrap/dist/js/bootstrap.bundle.min.js", "file": "bootstrap_dist_js_bootstrap__bundle__min__js.js", "fileHash": "f2ef7454", "needsInterop": true}, "d3": {"src": "../../d3/src/index.js", "file": "d3.js", "fileHash": "36ba5b89", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "0051c3f7", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "f5d68f27", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "6244e32a", "needsInterop": false}}, "chunks": {"chunk-YFT6OQ5R": {"file": "chunk-YFT6OQ5R.js"}, "chunk-FIAHBV72": {"file": "chunk-FIAHBV72.js"}, "chunk-HKJ2B2AA": {"file": "chunk-HKJ2B2AA.js"}}}