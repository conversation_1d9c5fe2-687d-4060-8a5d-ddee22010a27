// https://d3js.org/d3-polygon/ v3.0.1 Copyright 2010-2021 Mike <PERSON>
!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((n="undefined"!=typeof globalThis?globalThis:n||self).d3=n.d3||{})}(this,(function(n){"use strict";function e(n,e){return n[0]-e[0]||n[1]-e[1]}function t(n){const e=n.length,t=[0,1];let o,r=2;for(o=2;o<e;++o){for(;r>1&&(f=n[t[r-2]],l=n[t[r-1]],u=n[o],(l[0]-f[0])*(u[1]-f[1])-(l[1]-f[1])*(u[0]-f[0])<=0);)--r;t[r++]=o}var f,l,u;return t.slice(0,r)}n.polygonArea=function(n){for(var e,t=-1,o=n.length,r=n[o-1],f=0;++t<o;)e=r,r=n[t],f+=e[1]*r[0]-e[0]*r[1];return f/2},n.polygonCentroid=function(n){for(var e,t,o=-1,r=n.length,f=0,l=0,u=n[r-1],i=0;++o<r;)e=u,u=n[o],i+=t=e[0]*u[1]-u[0]*e[1],f+=(e[0]+u[0])*t,l+=(e[1]+u[1])*t;return[f/(i*=3),l/i]},n.polygonContains=function(n,e){for(var t,o,r=n.length,f=n[r-1],l=e[0],u=e[1],i=f[0],g=f[1],h=!1,a=0;a<r;++a)t=(f=n[a])[0],(o=f[1])>u!=g>u&&l<(i-t)*(u-o)/(g-o)+t&&(h=!h),i=t,g=o;return h},n.polygonHull=function(n){if((r=n.length)<3)return null;var o,r,f=new Array(r),l=new Array(r);for(o=0;o<r;++o)f[o]=[+n[o][0],+n[o][1],o];for(f.sort(e),o=0;o<r;++o)l[o]=[f[o][0],-f[o][1]];var u=t(f),i=t(l),g=i[0]===u[0],h=i[i.length-1]===u[u.length-1],a=[];for(o=u.length-1;o>=0;--o)a.push(n[f[u[o]][2]]);for(o=+g;o<i.length-h;++o)a.push(n[f[i[o]][2]]);return a},n.polygonLength=function(n){for(var e,t,o=-1,r=n.length,f=n[r-1],l=f[0],u=f[1],i=0;++o<r;)e=l,t=u,e-=l=(f=n[o])[0],t-=u=f[1],i+=Math.hypot(e,t);return i},Object.defineProperty(n,"__esModule",{value:!0})}));
