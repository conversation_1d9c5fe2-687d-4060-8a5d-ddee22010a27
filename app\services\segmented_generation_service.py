#!/usr/bin/env python3
"""
分段生成服务
实现章节分段生成功能，支持逐步创作和内容连贯性保证
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.services.ai_model_service import ai_model_service
from app.services.content_retrieval_service import get_content_retrieval_service
from app.services.content_need_analyzer import get_content_need_analyzer
from app.models.three_dimensional_model import ThreeDimensionalModel, ThreeDimensionalAnalyzer

logger = logging.getLogger(__name__)


class SegmentType(Enum):
    """段落类型"""
    OPENING = "opening"  # 开头
    DIALOGUE = "dialogue"  # 对话
    ACTION = "action"  # 动作
    DESCRIPTION = "description"  # 描写
    INNER_THOUGHT = "inner_thought"  # 内心独白
    TRANSITION = "transition"  # 过渡
    CLIMAX = "climax"  # 高潮
    ENDING = "ending"  # 结尾


@dataclass
class GenerationSegment:
    """生成段落"""
    segment_id: str
    segment_type: SegmentType
    content: str
    word_count: int
    
    # 三维模型评分
    model_analysis: Optional[ThreeDimensionalModel] = None
    
    # 连贯性信息
    previous_segment_id: Optional[str] = None
    next_segment_id: Optional[str] = None
    transition_quality: float = 0.0  # 过渡质量 (0-1)
    
    # 元数据
    generation_time: datetime = field(default_factory=datetime.now)
    generation_prompt: str = ""
    context_used: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "segment_id": self.segment_id,
            "segment_type": self.segment_type.value,
            "content": self.content,
            "word_count": self.word_count,
            "model_analysis": self.model_analysis.to_dict() if self.model_analysis else None,
            "previous_segment_id": self.previous_segment_id,
            "next_segment_id": self.next_segment_id,
            "transition_quality": self.transition_quality,
            "generation_time": self.generation_time.isoformat(),
            "generation_prompt": self.generation_prompt,
            "context_used": self.context_used
        }


@dataclass
class GenerationPlan:
    """生成计划"""
    plan_id: str
    target_length: int  # 目标总长度
    segments: List[Dict[str, Any]] = field(default_factory=list)  # 段落计划
    estimated_segments: int = 0
    current_segment_index: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "plan_id": self.plan_id,
            "target_length": self.target_length,
            "segments": self.segments,
            "estimated_segments": self.estimated_segments,
            "current_segment_index": self.current_segment_index
        }


@dataclass
class GenerationSession:
    """生成会话"""
    session_id: str
    project_id: str
    chapter_id: Optional[str] = None
    
    # 生成计划和进度
    plan: Optional[GenerationPlan] = None
    generated_segments: List[GenerationSegment] = field(default_factory=list)
    
    # 上下文信息
    base_context: Dict[str, Any] = field(default_factory=dict)
    current_context: Dict[str, Any] = field(default_factory=dict)
    
    # 会话状态
    is_active: bool = True
    total_generated_words: int = 0
    session_start_time: datetime = field(default_factory=datetime.now)
    last_generation_time: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "session_id": self.session_id,
            "project_id": self.project_id,
            "chapter_id": self.chapter_id,
            "plan": self.plan.to_dict() if self.plan else None,
            "generated_segments": [seg.to_dict() for seg in self.generated_segments],
            "base_context": self.base_context,
            "current_context": self.current_context,
            "is_active": self.is_active,
            "total_generated_words": self.total_generated_words,
            "session_start_time": self.session_start_time.isoformat(),
            "last_generation_time": self.last_generation_time.isoformat() if self.last_generation_time else None
        }


class SegmentedGenerationService:
    """分段生成服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.content_retrieval = get_content_retrieval_service()
        self.need_analyzer = get_content_need_analyzer()
        self.model_analyzer = ThreeDimensionalAnalyzer()
        
        # 活跃会话存储
        self.active_sessions: Dict[str, GenerationSession] = {}
        
        # 段落类型模板
        self.segment_templates = {
            SegmentType.OPENING: {
                "description": "章节开头，设定场景和氛围",
                "typical_length": 150,
                "focus": ["环境描写", "角色状态", "情绪铺垫"]
            },
            SegmentType.DIALOGUE: {
                "description": "角色对话，推进情节和展现关系",
                "typical_length": 200,
                "focus": ["角色个性", "对话内容", "情绪表达"]
            },
            SegmentType.ACTION: {
                "description": "动作场面，营造紧张感",
                "typical_length": 180,
                "focus": ["动作描写", "节奏控制", "冲突展现"]
            },
            SegmentType.DESCRIPTION: {
                "description": "环境或角色描写",
                "typical_length": 120,
                "focus": ["细节描写", "氛围营造", "感官体验"]
            },
            SegmentType.INNER_THOUGHT: {
                "description": "角色内心独白",
                "typical_length": 100,
                "focus": ["心理活动", "情感变化", "思考过程"]
            },
            SegmentType.TRANSITION: {
                "description": "场景或时间过渡",
                "typical_length": 80,
                "focus": ["平滑过渡", "时空转换", "连贯性"]
            },
            SegmentType.CLIMAX: {
                "description": "情节高潮",
                "typical_length": 250,
                "focus": ["冲突爆发", "情绪高涨", "转折点"]
            },
            SegmentType.ENDING: {
                "description": "章节结尾",
                "typical_length": 100,
                "focus": ["情节收束", "悬念设置", "过渡铺垫"]
            }
        }
    
    async def start_generation_session(
        self, 
        project_id: str, 
        user_prompt: str,
        target_length: int = 800,
        chapter_id: Optional[str] = None
    ) -> GenerationSession:
        """开始生成会话"""
        try:
            import uuid
            session_id = str(uuid.uuid4())
            
            self.logger.info(f"开始生成会话: {session_id}")
            
            # 1. 分析内容需求
            analysis_result = await self.need_analyzer.analyze_generation_request(
                project_id, user_prompt
            )
            
            # 2. 获取基础上下文
            base_context = await self.content_retrieval.get_context_for_generation(
                project_id, analysis_result.generation_intent
            )
            
            # 3. 创建生成计划
            plan = await self._create_generation_plan(
                user_prompt, target_length, analysis_result, base_context
            )
            
            # 4. 创建会话
            session = GenerationSession(
                session_id=session_id,
                project_id=project_id,
                chapter_id=chapter_id,
                plan=plan,
                base_context=base_context,
                current_context=base_context.copy()
            )
            
            self.active_sessions[session_id] = session
            
            self.logger.info(f"生成会话创建完成: 计划{plan.estimated_segments}段")
            return session
            
        except Exception as e:
            self.logger.error(f"创建生成会话失败: {e}")
            raise
    
    async def _create_generation_plan(
        self, 
        user_prompt: str, 
        target_length: int,
        analysis_result: Any,
        context: Dict[str, Any]
    ) -> GenerationPlan:
        """创建生成计划"""
        try:
            import uuid
            plan_id = str(uuid.uuid4())
            
            # 基于意图和长度规划段落
            intent = analysis_result.generation_intent
            estimated_segments = max(3, target_length // 200)  # 平均每段200字
            
            segments = []
            
            if intent == "next_chapter":
                # 下一章的典型结构
                segments = [
                    {"type": SegmentType.OPENING.value, "description": "章节开头，承接前文", "target_length": 150},
                    {"type": SegmentType.DIALOGUE.value, "description": "角色互动推进情节", "target_length": 200},
                    {"type": SegmentType.ACTION.value, "description": "关键事件发生", "target_length": 200},
                    {"type": SegmentType.INNER_THOUGHT.value, "description": "角色内心反应", "target_length": 100},
                    {"type": SegmentType.CLIMAX.value, "description": "情节高潮或转折", "target_length": 200},
                    {"type": SegmentType.ENDING.value, "description": "章节结尾，设置悬念", "target_length": 150}
                ]
            elif intent == "dialogue_scene":
                # 对话场景结构
                segments = [
                    {"type": SegmentType.DESCRIPTION.value, "description": "场景设定", "target_length": 100},
                    {"type": SegmentType.DIALOGUE.value, "description": "主要对话内容", "target_length": 300},
                    {"type": SegmentType.INNER_THOUGHT.value, "description": "角色心理活动", "target_length": 100},
                    {"type": SegmentType.DIALOGUE.value, "description": "对话深入", "target_length": 200},
                    {"type": SegmentType.ENDING.value, "description": "对话结束", "target_length": 100}
                ]
            else:
                # 通用结构
                avg_length = target_length // estimated_segments
                for i in range(estimated_segments):
                    if i == 0:
                        seg_type = SegmentType.OPENING
                    elif i == estimated_segments - 1:
                        seg_type = SegmentType.ENDING
                    else:
                        # 交替使用不同类型
                        types = [SegmentType.DIALOGUE, SegmentType.ACTION, SegmentType.DESCRIPTION]
                        seg_type = types[i % len(types)]
                    
                    segments.append({
                        "type": seg_type.value,
                        "description": f"第{i+1}段内容",
                        "target_length": avg_length
                    })
            
            # 调整长度以匹配目标
            total_planned = sum(seg["target_length"] for seg in segments)
            if total_planned != target_length:
                ratio = target_length / total_planned
                for seg in segments:
                    seg["target_length"] = int(seg["target_length"] * ratio)
            
            plan = GenerationPlan(
                plan_id=plan_id,
                target_length=target_length,
                segments=segments,
                estimated_segments=len(segments)
            )
            
            return plan
            
        except Exception as e:
            self.logger.error(f"创建生成计划失败: {e}")
            raise
    
    async def generate_next_segment(self, session_id: str) -> Optional[GenerationSegment]:
        """生成下一个段落"""
        try:
            session = self.active_sessions.get(session_id)
            if not session or not session.is_active:
                raise ValueError("会话不存在或已结束")
            
            plan = session.plan
            if not plan or plan.current_segment_index >= len(plan.segments):
                session.is_active = False
                return None
            
            # 获取当前段落计划
            current_plan = plan.segments[plan.current_segment_index]
            segment_type = SegmentType(current_plan["type"])
            
            self.logger.info(f"生成段落 {plan.current_segment_index + 1}/{len(plan.segments)}: {segment_type.value}")
            
            # 1. 分析当前段落需求
            segment_context = await self._prepare_segment_context(session, current_plan)
            
            # 2. 生成段落内容
            content = await self._generate_segment_content(session, current_plan, segment_context)
            
            # 3. 创建段落对象
            import uuid
            segment = GenerationSegment(
                segment_id=str(uuid.uuid4()),
                segment_type=segment_type,
                content=content,
                word_count=len(content),
                generation_prompt=segment_context.get("prompt", ""),
                context_used=segment_context
            )
            
            # 4. 分析段落质量
            segment.model_analysis = self.model_analyzer.analyze_content(content, segment_context)
            
            # 5. 更新会话状态
            if session.generated_segments:
                segment.previous_segment_id = session.generated_segments[-1].segment_id
                session.generated_segments[-1].next_segment_id = segment.segment_id
            
            session.generated_segments.append(segment)
            session.total_generated_words += segment.word_count
            session.last_generation_time = datetime.now()
            plan.current_segment_index += 1
            
            # 6. 更新上下文
            self._update_session_context(session, segment)
            
            self.logger.info(f"段落生成完成: {segment.word_count}字")
            return segment
            
        except Exception as e:
            self.logger.error(f"生成段落失败: {e}")
            raise
    
    async def _prepare_segment_context(
        self, 
        session: GenerationSession, 
        segment_plan: Dict[str, Any]
    ) -> Dict[str, Any]:
        """准备段落生成上下文"""
        try:
            segment_type = SegmentType(segment_plan["type"])
            template = self.segment_templates[segment_type]
            
            # 基础上下文
            context = session.current_context.copy()
            context.update({
                "segment_type": segment_type.value,
                "segment_description": segment_plan["description"],
                "target_length": segment_plan["target_length"],
                "template": template
            })
            
            # 添加前文内容
            if session.generated_segments:
                recent_content = ""
                for seg in session.generated_segments[-2:]:  # 最近2段
                    recent_content += seg.content + "\n\n"
                context["recent_content"] = recent_content.strip()
            
            # 构建生成提示词
            context["prompt"] = self._build_segment_prompt(context, segment_type)
            
            return context
            
        except Exception as e:
            self.logger.error(f"准备段落上下文失败: {e}")
            return {}
    
    def _build_segment_prompt(self, context: Dict[str, Any], segment_type: SegmentType) -> str:
        """构建段落生成提示词"""
        template = context.get("template", {})
        
        prompt = f"""
请基于以下信息生成小说的{segment_type.value}段落：

段落类型：{segment_type.value}
段落描述：{context.get('segment_description', '')}
目标长度：约{context.get('target_length', 200)}字
重点关注：{', '.join(template.get('focus', []))}

"""
        
        # 添加角色信息
        if context.get("characters"):
            prompt += f"\n主要角色：\n"
            for char in context["characters"][:3]:  # 最多3个主要角色
                prompt += f"- {char.get('name', '')}: {char.get('description', '')[:50]}...\n"
        
        # 添加前文内容
        if context.get("recent_content"):
            prompt += f"\n前文内容：\n{context['recent_content'][-300:]}\n"  # 最近300字
        
        # 添加世界观设定
        if context.get("world_settings"):
            world = context["world_settings"]
            prompt += f"\n世界观设定：{world.get('setting', '')} {world.get('genre', '')}\n"
        
        # 三维模型指导
        prompt += f"""
创作要求：
1. 冲突维度：营造适度的冲突张力，推进情节发展
2. 情绪维度：表达角色真实情感，引起读者共鸣
3. 爽点维度：在适当时机设置吸引点，提升阅读体验

请生成内容，只返回小说正文，不要其他说明：
"""
        
        return prompt
    
    async def _generate_segment_content(
        self, 
        session: GenerationSession, 
        segment_plan: Dict[str, Any], 
        context: Dict[str, Any]
    ) -> str:
        """生成段落内容"""
        try:
            prompt = context["prompt"]
            target_length = segment_plan["target_length"]
            
            # 调用AI生成
            content = ai_model_service.generate_text(
                prompt=prompt,
                max_tokens=int(target_length * 1.5),  # 留出余量
                temperature=0.8  # 较高的创造性
            )
            
            # 清理和优化内容
            content = self._clean_generated_content(content)
            
            # 长度调整
            if len(content) > target_length * 1.3:
                # 内容过长，截取
                content = content[:int(target_length * 1.2)]
                # 确保在句号处结束
                last_period = content.rfind('。')
                if last_period > target_length * 0.8:
                    content = content[:last_period + 1]
            
            return content
            
        except Exception as e:
            self.logger.error(f"生成段落内容失败: {e}")
            return f"[生成失败: {str(e)}]"
    
    def _clean_generated_content(self, content: str) -> str:
        """清理生成的内容"""
        # 移除多余的空行
        content = '\n'.join(line.strip() for line in content.split('\n') if line.strip())
        
        # 移除可能的提示词残留
        unwanted_prefixes = ["请生成", "以下是", "内容如下", "小说正文"]
        for prefix in unwanted_prefixes:
            if content.startswith(prefix):
                content = content[len(prefix):].strip()
        
        return content.strip()
    
    def _update_session_context(self, session: GenerationSession, segment: GenerationSegment):
        """更新会话上下文"""
        # 更新当前上下文，为下一段生成做准备
        session.current_context["last_segment"] = {
            "type": segment.segment_type.value,
            "content": segment.content,
            "word_count": segment.word_count
        }
        
        # 如果有模型分析结果，更新情绪和冲突状态
        if segment.model_analysis:
            session.current_context["current_emotion"] = segment.model_analysis.emotion.primary_emotion.value
            session.current_context["current_conflict"] = segment.model_analysis.conflict.conflict_type.value
    
    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {"error": "会话不存在"}
        
        plan = session.plan
        progress = plan.current_segment_index / len(plan.segments) if plan else 0
        
        return {
            "session_id": session_id,
            "is_active": session.is_active,
            "progress": progress,
            "current_segment": plan.current_segment_index if plan else 0,
            "total_segments": len(plan.segments) if plan else 0,
            "generated_words": session.total_generated_words,
            "target_words": plan.target_length if plan else 0,
            "segments_generated": len(session.generated_segments)
        }
    
    def get_generated_content(self, session_id: str) -> str:
        """获取已生成的完整内容"""
        session = self.active_sessions.get(session_id)
        if not session:
            return ""
        
        return "\n\n".join(segment.content for segment in session.generated_segments)
    
    def end_session(self, session_id: str) -> Dict[str, Any]:
        """结束生成会话"""
        session = self.active_sessions.get(session_id)
        if not session:
            return {"error": "会话不存在"}
        
        session.is_active = False
        complete_content = self.get_generated_content(session_id)
        
        # 清理会话（可选）
        # del self.active_sessions[session_id]
        
        return {
            "session_id": session_id,
            "complete_content": complete_content,
            "total_words": session.total_generated_words,
            "segments_count": len(session.generated_segments),
            "generation_time": (datetime.now() - session.session_start_time).total_seconds()
        }


# 全局实例
segmented_generation_service = SegmentedGenerationService()


def get_segmented_generation_service() -> SegmentedGenerationService:
    """获取分段生成服务实例"""
    return segmented_generation_service
