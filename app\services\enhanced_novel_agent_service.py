#!/usr/bin/env python3
"""
增强小说创作Agent服务
整合三维动态模型、分段生成、人物刻画、环境描写等功能
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from app.services.ai_model_service import ai_model_service
from app.services.content_retrieval_service import get_content_retrieval_service
from app.services.content_need_analyzer import get_content_need_analyzer
from app.services.segmented_generation_service import get_segmented_generation_service
from app.services.character_portrayal_service import get_character_portrayal_service
from app.services.environment_description_service import get_environment_description_service
from app.models.three_dimensional_model import (
    ThreeDimensionalModel, ThreeDimensionalAnalyzer, ConflictType, 
    EmotionType, PleasureType, ConflictIntensity, EmotionIntensity
)

logger = logging.getLogger(__name__)


class GenerationMode(Enum):
    """生成模式"""
    NEXT_CHAPTER = "next_chapter"  # 下一章
    SCENE_EXPANSION = "scene_expansion"  # 场景扩展
    CHARACTER_FOCUS = "character_focus"  # 角色聚焦
    DIALOGUE_SCENE = "dialogue_scene"  # 对话场景
    ACTION_SEQUENCE = "action_sequence"  # 动作序列
    EMOTIONAL_MOMENT = "emotional_moment"  # 情感时刻


@dataclass
class GenerationRequest:
    """生成请求"""
    project_id: str
    user_prompt: str
    generation_mode: GenerationMode = GenerationMode.NEXT_CHAPTER
    target_length: int = 800
    chapter_id: Optional[str] = None
    
    # 三维模型目标
    target_conflict_type: Optional[ConflictType] = None
    target_emotion_type: Optional[EmotionType] = None
    target_pleasure_type: Optional[PleasureType] = None
    
    # 生成偏好
    focus_characters: List[str] = field(default_factory=list)
    environment_emphasis: bool = True
    dialogue_heavy: bool = False
    action_heavy: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "project_id": self.project_id,
            "user_prompt": self.user_prompt,
            "generation_mode": self.generation_mode.value,
            "target_length": self.target_length,
            "chapter_id": self.chapter_id,
            "target_conflict_type": self.target_conflict_type.value if self.target_conflict_type else None,
            "target_emotion_type": self.target_emotion_type.value if self.target_emotion_type else None,
            "target_pleasure_type": self.target_pleasure_type.value if self.target_pleasure_type else None,
            "focus_characters": self.focus_characters,
            "environment_emphasis": self.environment_emphasis,
            "dialogue_heavy": self.dialogue_heavy,
            "action_heavy": self.action_heavy
        }


@dataclass
class GenerationResult:
    """生成结果"""
    success: bool
    content: str = ""
    word_count: int = 0
    
    # 质量评估
    model_analysis: Optional[ThreeDimensionalModel] = None
    quality_score: float = 0.0
    
    # 生成信息
    generation_time: float = 0.0
    segments_generated: int = 0
    session_id: Optional[str] = None
    
    # 改进建议
    suggestions: List[str] = field(default_factory=list)
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "content": self.content,
            "word_count": self.word_count,
            "model_analysis": self.model_analysis.to_dict() if self.model_analysis else None,
            "quality_score": self.quality_score,
            "generation_time": self.generation_time,
            "segments_generated": self.segments_generated,
            "session_id": self.session_id,
            "suggestions": self.suggestions,
            "strengths": self.strengths,
            "weaknesses": self.weaknesses
        }


class EnhancedNovelAgentService:
    """增强小说创作Agent服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 服务依赖
        self.content_retrieval = get_content_retrieval_service()
        self.need_analyzer = get_content_need_analyzer()
        self.segmented_generation = get_segmented_generation_service()
        self.character_portrayal = get_character_portrayal_service()
        self.environment_description = get_environment_description_service()
        self.model_analyzer = ThreeDimensionalAnalyzer()
        
        # 质量评估阈值
        self.quality_thresholds = {
            "excellent": 0.8,
            "good": 0.6,
            "acceptable": 0.4,
            "poor": 0.2
        }
    
    async def generate_content(self, request: GenerationRequest) -> GenerationResult:
        """生成小说内容"""
        start_time = datetime.now()
        
        try:
            self.logger.info(f"开始生成内容: {request.generation_mode.value}")
            
            # 1. 分析内容需求
            need_analysis = await self.need_analyzer.analyze_generation_request(
                request.project_id, request.user_prompt
            )
            
            # 2. 获取上下文信息
            context = await self.content_retrieval.get_comprehensive_context(
                request.project_id, request.chapter_id, "standard"
            )
            
            # 3. 选择生成策略
            if request.target_length > 500:
                # 长内容使用分段生成
                result = await self._generate_with_segmentation(request, context, need_analysis)
            else:
                # 短内容直接生成
                result = await self._generate_direct(request, context, need_analysis)
            
            # 4. 质量评估和优化
            if result.success and result.content:
                await self._evaluate_and_enhance_content(result, request, context)
            
            # 5. 计算生成时间
            result.generation_time = (datetime.now() - start_time).total_seconds()
            
            self.logger.info(f"内容生成完成: {result.word_count}字, 质量分数: {result.quality_score:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"生成内容失败: {e}")
            return GenerationResult(
                success=False,
                content=f"生成失败: {str(e)}",
                generation_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def _generate_with_segmentation(
        self, 
        request: GenerationRequest,
        context: Any,
        need_analysis: Any
    ) -> GenerationResult:
        """使用分段生成"""
        try:
            # 启动分段生成会话
            session = await self.segmented_generation.start_generation_session(
                request.project_id,
                request.user_prompt,
                request.target_length,
                request.chapter_id
            )
            
            # 逐段生成
            all_segments = []
            while True:
                segment = await self.segmented_generation.generate_next_segment(session.session_id)
                if not segment:
                    break
                
                # 增强段落内容
                enhanced_segment = await self._enhance_segment_content(
                    segment, request, context
                )
                all_segments.append(enhanced_segment)
            
            # 合并内容
            complete_content = "\n\n".join(seg.content for seg in all_segments)
            
            return GenerationResult(
                success=True,
                content=complete_content,
                word_count=len(complete_content),
                segments_generated=len(all_segments),
                session_id=session.session_id
            )
            
        except Exception as e:
            self.logger.error(f"分段生成失败: {e}")
            return GenerationResult(success=False, content=f"分段生成失败: {str(e)}")
    
    async def _generate_direct(
        self,
        request: GenerationRequest,
        context: Any,
        need_analysis: Any
    ) -> GenerationResult:
        """直接生成"""
        try:
            # 构建增强提示词
            enhanced_prompt = await self._build_enhanced_prompt(request, context, need_analysis)
            
            # 调用AI生成
            content = ai_model_service.generate_text(
                prompt=enhanced_prompt,
                max_tokens=int(request.target_length * 1.5),
                temperature=0.8
            )
            
            # 清理内容
            content = self._clean_content(content)
            
            return GenerationResult(
                success=True,
                content=content,
                word_count=len(content)
            )
            
        except Exception as e:
            self.logger.error(f"直接生成失败: {e}")
            return GenerationResult(success=False, content=f"直接生成失败: {str(e)}")
    
    async def _build_enhanced_prompt(
        self,
        request: GenerationRequest,
        context: Any,
        need_analysis: Any
    ) -> str:
        """构建增强提示词"""
        prompt_parts = []
        
        # 1. 基础指令
        prompt_parts.append(f"""
请基于"冲突-情绪-爽点"三维动态模型创作小说内容：

用户需求：{request.user_prompt}
生成模式：{request.generation_mode.value}
目标长度：约{request.target_length}字
""")
        
        # 2. 三维模型指导
        if request.target_conflict_type:
            prompt_parts.append(f"冲突类型：{request.target_conflict_type.value}")
        if request.target_emotion_type:
            prompt_parts.append(f"情绪类型：{request.target_emotion_type.value}")
        if request.target_pleasure_type:
            prompt_parts.append(f"爽点类型：{request.target_pleasure_type.value}")
        
        # 3. 角色信息
        if hasattr(context, 'main_characters') and context.main_characters:
            prompt_parts.append("\n主要角色：")
            for char in context.main_characters[:3]:
                char_info = f"- {char.get('name', '')}: {char.get('description', '')[:100]}"
                if request.focus_characters and char.get('name') in request.focus_characters:
                    char_info += " [重点角色]"
                prompt_parts.append(char_info)
        
        # 4. 世界观设定
        if hasattr(context, 'world_settings') and context.world_settings:
            world = context.world_settings
            prompt_parts.append(f"\n世界观：{world.get('setting', '')} {world.get('genre', '')}")
        
        # 5. 前文内容
        if hasattr(context, 'previous_chapters') and context.previous_chapters:
            last_chapter = context.previous_chapters[-1]
            prompt_parts.append(f"\n前文概要：{last_chapter.get('title', '')} - {last_chapter.get('content', '')[:200]}...")
        
        # 6. 创作要求
        requirements = []
        if request.environment_emphasis:
            requirements.append("重视环境描写，营造生动的场景氛围")
        if request.dialogue_heavy:
            requirements.append("以对话为主，展现角色个性和推进情节")
        if request.action_heavy:
            requirements.append("注重动作描写，营造紧张刺激的节奏")
        
        if requirements:
            prompt_parts.append(f"\n创作要求：\n" + "\n".join(f"- {req}" for req in requirements))
        
        # 7. 质量标准
        prompt_parts.append(f"""
质量标准：
1. 冲突维度：设置合理的冲突，推动情节发展
2. 情绪维度：表达真实情感，引起读者共鸣  
3. 爽点维度：在适当时机设置吸引点，提升阅读体验
4. 人物刻画：通过对话、动作、心理描写展现角色特点
5. 环境描写：运用多感官描写营造氛围

请生成小说内容，只返回正文，不要其他说明：
""")
        
        return "\n".join(prompt_parts)
    
    async def _enhance_segment_content(self, segment: Any, request: GenerationRequest, context: Any) -> Any:
        """增强段落内容"""
        try:
            # 根据段落类型进行特定增强
            if segment.segment_type.value == "dialogue":
                # 增强对话
                if hasattr(context, 'main_characters'):
                    for char in context.main_characters:
                        if char.get('name') in segment.content:
                            enhanced_dialogue = self.character_portrayal.enhance_dialogue_with_personality(
                                segment.content, char
                            )
                            segment.content = enhanced_dialogue
                            break
            
            elif segment.segment_type.value == "description":
                # 增强环境描写
                if request.environment_emphasis:
                    # 这里可以进一步增强环境描写
                    pass
            
            return segment
            
        except Exception as e:
            self.logger.error(f"增强段落内容失败: {e}")
            return segment
    
    async def _evaluate_and_enhance_content(
        self,
        result: GenerationResult,
        request: GenerationRequest,
        context: Any
    ):
        """评估和增强内容"""
        try:
            # 1. 三维模型分析
            analysis_result = self.model_analyzer.analyze_content(
                result.content, context.__dict__ if hasattr(context, '__dict__') else {}
            )
            result.model_analysis = analysis_result.model
            
            # 2. 计算质量分数
            result.quality_score = self._calculate_quality_score(analysis_result.model)
            
            # 3. 生成改进建议
            result.suggestions = analysis_result.suggestions
            result.strengths = analysis_result.strengths
            result.weaknesses = analysis_result.weaknesses
            
            # 4. 如果质量不达标，尝试优化
            if result.quality_score < self.quality_thresholds["acceptable"]:
                optimized_content = await self._optimize_content(result.content, analysis_result)
                if optimized_content and len(optimized_content) > len(result.content) * 0.8:
                    result.content = optimized_content
                    result.word_count = len(optimized_content)
                    # 重新评估
                    new_analysis = self.model_analyzer.analyze_content(optimized_content)
                    result.model_analysis = new_analysis.model
                    result.quality_score = self._calculate_quality_score(new_analysis.model)
            
        except Exception as e:
            self.logger.error(f"评估和增强内容失败: {e}")
    
    def _calculate_quality_score(self, model: ThreeDimensionalModel) -> float:
        """计算质量分数"""
        try:
            # 基于三维模型的综合评分
            conflict_score = model.conflict.intensity.value / 4.0
            emotion_score = model.emotion.intensity.value / 4.0
            pleasure_score = model.pleasure.intensity
            
            # 综合评分
            base_score = (conflict_score * 0.3 + emotion_score * 0.3 + pleasure_score * 0.4)
            
            # 考虑其他因素
            bonus = 0
            if model.overall_tension > 0.6:
                bonus += 0.1
            if model.narrative_momentum > 0.6:
                bonus += 0.1
            if model.reader_engagement > 0.6:
                bonus += 0.1
            
            return min(base_score + bonus, 1.0)
            
        except Exception as e:
            self.logger.error(f"计算质量分数失败: {e}")
            return 0.5
    
    async def _optimize_content(self, content: str, analysis_result: Any) -> str:
        """优化内容"""
        try:
            # 基于分析结果生成优化提示
            optimization_prompt = f"""
请优化以下小说内容，重点改进：
{', '.join(analysis_result.weaknesses)}

原内容：
{content}

优化要求：
1. 保持原有情节和角色设定
2. 增强{', '.join(analysis_result.suggestions)}
3. 提升整体质量和可读性

请返回优化后的内容：
"""
            
            optimized = ai_model_service.generate_text(
                prompt=optimization_prompt,
                max_tokens=len(content) + 200,
                temperature=0.7
            )
            
            return self._clean_content(optimized)
            
        except Exception as e:
            self.logger.error(f"优化内容失败: {e}")
            return content
    
    def _clean_content(self, content: str) -> str:
        """清理内容"""
        # 移除多余的空行和空格
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        # 移除可能的提示词残留
        cleaned_lines = []
        for line in lines:
            if not any(prefix in line for prefix in ["请生成", "以下是", "内容如下", "优化后"]):
                cleaned_lines.append(line)
        
        return '\n\n'.join(cleaned_lines)
    
    def get_generation_suggestions(self, project_id: str) -> List[Dict[str, Any]]:
        """获取生成建议"""
        suggestions = [
            {
                "mode": GenerationMode.NEXT_CHAPTER.value,
                "title": "继续下一章",
                "description": "基于现有情节自然发展下一章内容",
                "recommended_length": 800
            },
            {
                "mode": GenerationMode.DIALOGUE_SCENE.value,
                "title": "对话场景",
                "description": "重点刻画角色对话，推进情节发展",
                "recommended_length": 400
            },
            {
                "mode": GenerationMode.ACTION_SEQUENCE.value,
                "title": "动作序列",
                "description": "营造紧张刺激的动作场面",
                "recommended_length": 600
            },
            {
                "mode": GenerationMode.EMOTIONAL_MOMENT.value,
                "title": "情感时刻",
                "description": "深入挖掘角色内心情感",
                "recommended_length": 300
            }
        ]
        
        return suggestions


# 全局实例
enhanced_novel_agent_service = EnhancedNovelAgentService()


def get_enhanced_novel_agent_service() -> EnhancedNovelAgentService:
    """获取增强小说创作Agent服务实例"""
    return enhanced_novel_agent_service
