<template>
  <div class="card model-card h-100">
    <div class="card-header d-flex justify-content-between align-items-center">
      <div class="d-flex align-items-center">
        <span class="badge" :class="getTypeBadgeClass(type)">
          {{ getTypeLabel(type) }}
        </span>
        <span class="badge ms-2" :class="getStatusBadgeClass(model.status)">
          {{ getStatusLabel(model.status) }}
        </span>
      </div>
      <div class="dropdown">
        <button 
          class="btn btn-sm btn-outline-secondary dropdown-toggle"
          data-bs-toggle="dropdown"
          aria-expanded="false"
        >
          <i class="bi bi-three-dots"></i>
        </button>
        <ul class="dropdown-menu">
          <li>
            <a class="dropdown-item" href="#" @click.prevent="$emit('test', model, type)">
              <i class="bi bi-play-circle me-2"></i>测试模型
            </a>
          </li>
          <li>
            <a class="dropdown-item" href="#" @click.prevent="$emit('configure', model)">
              <i class="bi bi-gear me-2"></i>配置
            </a>
          </li>
          <li v-if="type === 'remote'">
            <a class="dropdown-item" href="#" @click.prevent="$emit('use-for-project', model)">
              <i class="bi bi-folder-plus me-2"></i>用于项目
            </a>
          </li>
          <li><hr class="dropdown-divider"></li>
          <li>
            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('remove', model, type)">
              <i class="bi bi-trash me-2"></i>移除
            </a>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="card-body">
      <!-- 模型图标 -->
      <div class="model-icon mb-3">
        <i :class="getModelIcon(type)"></i>
      </div>
      
      <!-- 模型基本信息 -->
      <h5 class="card-title text-center mb-2">{{ model.name }}</h5>
      
      <div class="model-info">
        <div v-if="model.version" class="info-item">
          <i class="bi bi-tag me-2"></i>
          <span>版本 {{ model.version }}</span>
        </div>
        <div v-if="model.size" class="info-item">
          <i class="bi bi-hdd me-2"></i>
          <span>{{ formatSize(model.size) }}</span>
        </div>
        <div v-if="model.parameters" class="info-item">
          <i class="bi bi-cpu me-2"></i>
          <span>{{ model.parameters }} 参数</span>
        </div>
      </div>
      
      <!-- 模型描述 -->
      <p class="card-text text-muted small mt-3" :title="model.description">
        {{ model.description || '暂无描述' }}
      </p>
      
      <!-- 模型特性标签 -->
      <div v-if="model.capabilities && model.capabilities.length > 0" class="model-capabilities">
        <span 
          v-for="capability in model.capabilities.slice(0, 3)" 
          :key="capability"
          class="badge bg-light text-dark me-1 mb-1"
        >
          {{ capability }}
        </span>
        <span v-if="model.capabilities.length > 3" class="text-muted small">
          +{{ model.capabilities.length - 3 }}
        </span>
      </div>
    </div>
    
    <div class="card-footer bg-transparent">
      <div class="d-flex justify-content-between align-items-center">
        <small class="text-muted">
          <i class="bi bi-clock me-1"></i>
          {{ formatDate(model.updated_at || model.created_at) }}
        </small>
        <div class="model-performance">
          <div v-if="model.performance" class="performance-indicator" :class="`performance-${getPerformanceLevel(model.performance)}`">
            <i class="bi bi-speedometer2"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModelCard',
  props: {
    model: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      required: true,
      validator: value => ['ollama', 'huggingface', 'custom'].includes(value)
    }
  },
  emits: ['test', 'configure', 'remove', 'use-for-project'],
  setup() {
    const getTypeLabel = (type) => {
      const typeMap = {
        'ollama': 'Ollama',
        'huggingface': 'Hugging Face',
        'remote': '远程服务器',
        'custom': '自定义'
      }
      return typeMap[type] || type
    }
    
    const getStatusLabel = (status) => {
      const statusMap = {
        'available': '可用',
        'downloading': '下载中',
        'error': '错误',
        'offline': '离线'
      }
      return statusMap[status] || status
    }
    
    const getTypeBadgeClass = (type) => {
      const classMap = {
        'ollama': 'bg-primary',
        'huggingface': 'bg-success',
        'remote': 'bg-info',
        'custom': 'bg-warning'
      }
      return classMap[type] || 'bg-secondary'
    }
    
    const getStatusBadgeClass = (status) => {
      const classMap = {
        'available': 'bg-success',
        'downloading': 'bg-info',
        'error': 'bg-danger',
        'offline': 'bg-secondary'
      }
      return classMap[status] || 'bg-secondary'
    }
    
    const getModelIcon = (type) => {
      const iconMap = {
        'ollama': 'bi bi-hdd-stack',
        'huggingface': 'bi bi-cloud',
        'remote': 'bi bi-cloud-arrow-up',
        'custom': 'bi bi-gear'
      }
      return iconMap[type] || 'bi bi-cpu'
    }
    
    const getPerformanceLevel = (performance) => {
      if (performance >= 80) return 'high'
      if (performance >= 60) return 'medium'
      return 'low'
    }
    
    const formatSize = (bytes) => {
      if (!bytes) return '未知'
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }
    
    return {
      getTypeLabel,
      getStatusLabel,
      getTypeBadgeClass,
      getStatusBadgeClass,
      getModelIcon,
      getPerformanceLevel,
      formatSize,
      formatDate
    }
  }
}
</script>

<style lang="scss" scoped>
.model-card {
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.model-icon {
  text-align: center;
  
  i {
    font-size: 3rem;
    color: #667eea;
  }
}

.model-info {
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: #6c757d;
    
    i {
      width: 1rem;
      text-align: center;
    }
  }
}

.card-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  min-height: 3.6em;
}

.model-capabilities {
  margin-top: 1rem;
  
  .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}

.performance-indicator {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  
  &.performance-high {
    background-color: #28a745;
    color: white;
  }
  
  &.performance-medium {
    background-color: #ffc107;
    color: white;
  }
  
  &.performance-low {
    background-color: #dc3545;
    color: white;
  }
}

.dropdown-toggle::after {
  display: none;
}

.dropdown-menu {
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  &.text-danger:hover {
    background-color: #f8d7da;
    color: #721c24 !important;
  }
}

@media (max-width: 768px) {
  .model-icon i {
    font-size: 2.5rem;
  }
}
</style>
