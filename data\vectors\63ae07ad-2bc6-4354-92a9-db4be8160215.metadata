[{"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 初入江湖", "content": "年轻剑客李明在师父张无忌指导下击败师兄王大龙后下山，途中解救被山贼劫持的林雪儿，开启江湖冒险。\n冲突: 李明需战胜师兄王大龙证明实力，同时对抗山贼黑风的掠夺", "summary": "年轻剑客李明在师父张无忌指导下击败师兄王大龙后下山，途中解救被山贼劫持的林雪儿，开启江湖冒险。", "characters": ["<PERSON>", "张无忌", "王大龙", "林雪儿", "黑风"], "scenes": ["华山之巅与王大龙对决", "山贼劫掠商队现场"], "tags": ["武侠", "进行中"], "created_at": "2025-08-02T00:22:24.859184", "updated_at": "2025-08-02T00:22:24.859184", "metadata": {"title": "初入江湖", "summary": "年轻剑客李明在师父张无忌指导下击败师兄王大龙后下山，途中解救被山贼劫持的林雪儿，开启江湖冒险。", "type": "武侠", "conflict": "李明需战胜师兄王大龙证明实力，同时对抗山贼黑风的掠夺", "resolution": "李明以华山剑法击败王大龙下山，用武力击退山贼救下林雪儿", "turning_points": ["击败王大龙获得下山资格", "解救林雪儿展现江湖初战"], "status": "进行中", "progress": 0.5, "involved_characters": ["<PERSON>", "张无忌", "王大龙", "林雪儿", "黑风"], "related_scenes": ["华山之巅与王大龙对决", "山贼劫掠商队现场"], "mysteries": ["林雪儿的真实身份", "黑风的过往秘密"], "foreshadowing": ["张无忌作为明教教主的过往", "林雪儿琴棋书画的特殊才能"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆修复师的谜团", "content": "记忆修复师林深接到一名神秘客户，其脑部出现异常信号，治疗过程中客户消失，修复舱留下诡异符号，同时脑科学研究所研究员失踪事件引发连锁反应。\n冲突: 客户脑部被未知病毒侵蚀，修复舱出现神秘符号，脑科学研究所研究员失踪，林深必须揭开记忆碎片背后的真相", "summary": "记忆修复师林深接到一名神秘客户，其脑部出现异常信号，治疗过程中客户消失，修复舱留下诡异符号，同时脑科学研究所研究员失踪事件引发连锁反应。", "characters": ["林深", "Zero", "银色面具女人"], "scenes": ["记忆修复舱", "脑科学研究所"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-02T00:27:55.522699", "updated_at": "2025-08-02T00:27:55.522699", "metadata": {"title": "记忆修复师的谜团", "summary": "记忆修复师林深接到一名神秘客户，其脑部出现异常信号，治疗过程中客户消失，修复舱留下诡异符号，同时脑科学研究所研究员失踪事件引发连锁反应。", "type": "赛博朋克/悬疑", "conflict": "客户脑部被未知病毒侵蚀，修复舱出现神秘符号，脑科学研究所研究员失踪，林深必须揭开记忆碎片背后的真相", "resolution": "通过分析符号与记忆片段，发现与脑科学研究所的关联，揭露银色面具女人与失踪事件的联系", "turning_points": ["客户在治疗中神秘消失", "修复舱浮现神秘符号", "新闻揭露研究所失踪事件"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人"], "related_scenes": ["记忆修复舱", "脑科学研究所"], "mysteries": ["客户消失的真相", "符号的来源与含义"], "foreshadowing": ["银色面具反复出现", "Zero异常警报"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾中的真相", "content": "记忆修复师林深接诊一名神秘客户，发现其脑部被未知病毒侵蚀，潜入记忆深处遭遇诡异场景与神秘女子，客户消失后留下荧光符号与研究所失踪事件，揭开赛博朋克世界背后的阴谋。\n冲突: 客户脑部被未知病毒侵蚀，记忆修复过程中遭遇神秘力量，客户消失引发研究所事故谜团", "summary": "记忆修复师林深接诊一名神秘客户，发现其脑部被未知病毒侵蚀，潜入记忆深处遭遇诡异场景与神秘女子，客户消失后留下荧光符号与研究所失踪事件，揭开赛博朋克世界背后的阴谋。", "characters": ["林深", "Zero", "神秘面具女子", "脑科学研究所研究员"], "scenes": ["第三区霓虹雨中的记忆修复舱", "实验室场景与天台雨幕"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-02T00:29:14.627042", "updated_at": "2025-08-02T00:29:14.627042", "metadata": {"title": "记忆迷雾中的真相", "summary": "记忆修复师林深接诊一名神秘客户，发现其脑部被未知病毒侵蚀，潜入记忆深处遭遇诡异场景与神秘女子，客户消失后留下荧光符号与研究所失踪事件，揭开赛博朋克世界背后的阴谋。", "type": "赛博朋克/悬疑", "conflict": "客户脑部被未知病毒侵蚀，记忆修复过程中遭遇神秘力量，客户消失引发研究所事故谜团", "resolution": "通过符号线索与零的提示，揭示研究所阴谋与面具女子身份，但核心谜题仍待解开", "turning_points": ["客户在修复过程中神秘消失", "修复舱浮现神秘荧光符号"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "神秘面具女子", "脑科学研究所研究员"], "related_scenes": ["第三区霓虹雨中的记忆修复舱", "实验室场景与天台雨幕"], "mysteries": ["客户消失的真正原因", "荧光符号的来源与含义"], "foreshadowing": ["银色面具女子的重复出现", "新闻报道中研究所事故的暗示"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾中的追凶", "content": "记忆修复师林深接诊一名异常客户，发现其脑部被神秘病毒侵蚀，潜入记忆深处遭遇诡异画面，客户消失后触发脑科学研究所失踪事件，林深追查银色面具女人的线索。\n冲突: 客户脑部被未知病毒侵蚀，记忆修复过程中遭遇强制断开，客户消失，脑科学研究所失踪事件与面具女人身份关联", "summary": "记忆修复师林深接诊一名异常客户，发现其脑部被神秘病毒侵蚀，潜入记忆深处遭遇诡异画面，客户消失后触发脑科学研究所失踪事件，林深追查银色面具女人的线索。", "characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "scenes": ["记忆修复舱内异常扫描", "第三区霓虹雨中的追查场景"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-02T01:12:02.493746", "updated_at": "2025-08-02T01:12:02.493746", "metadata": {"title": "记忆迷雾中的追凶", "summary": "记忆修复师林深接诊一名异常客户，发现其脑部被神秘病毒侵蚀，潜入记忆深处遭遇诡异画面，客户消失后触发脑科学研究所失踪事件，林深追查银色面具女人的线索。", "type": "赛博朋克/悬疑", "conflict": "客户脑部被未知病毒侵蚀，记忆修复过程中遭遇强制断开，客户消失，脑科学研究所失踪事件与面具女人身份关联", "resolution": "通过修复舱荧光符号解密，追踪银色面具女人与脑科学研究所的关联，揭开记忆病毒背后的阴谋", "turning_points": ["客户记忆中出现银色面具女人", "修复舱残留符号与研究所失踪事件关联", "林深发现面具与自己记忆的联系"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "related_scenes": ["记忆修复舱内异常扫描", "第三区霓虹雨中的追查场景"], "mysteries": ["银色面具女人的真实身份", "记忆病毒的来源与目的"], "foreshadowing": ["修复舱荧光符号的维度暗示", "新闻中失踪研究员与客户相似的伤痕"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾中的追凶", "content": "记忆修复师林深接诊一名异常客户，发现其脑区被神秘病毒侵蚀，潜入记忆深处遭遇诡异画面与未知威胁，客户消失后引发连锁事件。\n冲突: 客户脑区被未知病毒侵蚀，记忆修复过程中遭遇强制断开，客户消失，脑科学研究所研究员失踪事件关联在一起", "summary": "记忆修复师林深接诊一名异常客户，发现其脑区被神秘病毒侵蚀，潜入记忆深处遭遇诡异画面与未知威胁，客户消失后引发连锁事件。", "characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "scenes": ["记忆修复舱内扫描", "第三区霓虹雨中的追逐"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-02T02:58:43.652766", "updated_at": "2025-08-02T02:58:43.652766", "metadata": {"title": "记忆迷雾中的追凶", "summary": "记忆修复师林深接诊一名异常客户，发现其脑区被神秘病毒侵蚀，潜入记忆深处遭遇诡异画面与未知威胁，客户消失后引发连锁事件。", "type": "赛博朋克/悬疑", "conflict": "客户脑区被未知病毒侵蚀，记忆修复过程中遭遇强制断开，客户消失，脑科学研究所研究员失踪事件关联在一起", "resolution": "林深发现客户记忆中银色面具女人的线索，追踪神秘符号与荧光液体的来源，揭开记忆病毒背后的组织秘密", "turning_points": ["客户记忆中出现银色面具女人", "修复舱残留荧光符号与液体", "新闻报道脑科学研究所失踪事件"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "related_scenes": ["记忆修复舱内扫描", "第三区霓虹雨中的追逐"], "mysteries": ["客户消失的真相", "银色面具女人的身份"], "foreshadowing": ["修复舱荧光符号的神秘来源", "新闻中失踪研究员与客户记忆的关联"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_scene_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "scene", "title": "场景: 新东京第三区", "content": "悬浮于未来迷雾中的赛博朋克都市，霓虹雨笼罩的复杂景观，全息广告屏与电子蝴蝶群交织的虚拟与现实空间。\n地点: 新东京第三区\n氛围: 神秘、紧张、科技与腐朽交织的赛博朋克氛围", "summary": "新东京第三区的场景信息", "characters": [], "scenes": ["新东京第三区"], "tags": [], "created_at": "2025-08-03T19:02:43.283142", "updated_at": "2025-08-03T19:02:43.283142", "metadata": {"name": "新东京第三区", "location": "新东京第三区", "description": "悬浮于未来迷雾中的赛博朋克都市，霓虹雨笼罩的复杂景观，全息广告屏与电子蝴蝶群交织的虚拟与现实空间。", "atmosphere": "神秘、紧张、科技与腐朽交织的赛博朋克氛围", "geography": "悬浮城市、霓虹雨、全息广告、记忆修复舱、神经接口装置", "climate": "霓虹雨、电子脉冲、潮湿的金属气息", "culture": "脑科学研究、虚拟性爱程序、AI助手、神经接口技术", "politics": "", "economy": "", "rules": ["记忆修复技术依赖神经接口", "全息广告覆盖城市景观", "脑波数据需解码", "神经接口存在安全风险"], "dangers": ["未知病毒侵蚀脑区", "强制断开记忆扫描", "客户失踪事件", "神经接口过热"], "resources": ["记忆数据", "修复舱设备", "AI助手Zero", "荧光符号液体"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾", "content": "记忆修复师林深接诊一名神秘客户，发现其脑部存在异常病毒痕迹，客户在治疗中消失，留下荧光液体和神秘符号，同时脑科学研究所发生研究员失踪事件，林深陷入未知阴谋。\n冲突: 客户脑部异常病毒与失踪谜团，符号背后的未知协议，林深与AI助手Zero的异常互动", "summary": "记忆修复师林深接诊一名神秘客户，发现其脑部存在异常病毒痕迹，客户在治疗中消失，留下荧光液体和神秘符号，同时脑科学研究所发生研究员失踪事件，林深陷入未知阴谋。", "characters": ["林深", "Zero", "银色面具女人", "失踪研究员"], "scenes": ["记忆修复舱", "第三区霓虹雨场景", "实验室废墟"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-03T19:02:43.283142", "updated_at": "2025-08-03T19:02:43.283142", "metadata": {"title": "记忆迷雾", "summary": "记忆修复师林深接诊一名神秘客户，发现其脑部存在异常病毒痕迹，客户在治疗中消失，留下荧光液体和神秘符号，同时脑科学研究所发生研究员失踪事件，林深陷入未知阴谋。", "type": "赛博朋克/悬疑", "conflict": "客户脑部异常病毒与失踪谜团，符号背后的未知协议，林深与AI助手Zero的异常互动", "resolution": "通过符号线索与研究所关联，揭露银色面具女性与病毒真相，解开记忆迷雾", "turning_points": ["客户在治疗中神秘消失", "修复舱出现荧光符号与银色面具关联"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人", "失踪研究员"], "related_scenes": ["记忆修复舱", "第三区霓虹雨场景", "实验室废墟"], "mysteries": ["荧光符号的真正含义", "银色面具女人的身份", "病毒来源与目的"], "foreshadowing": ["Zero异常警报与电流杂音", "客户后颈神经接口的紫黑色光芒"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_scene_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "scene", "title": "场景: 新东京第三区", "content": "悬浮于未来迷雾中的赛博朋克都市，霓虹雨笼罩的神经中枢区域，充满电子脉冲与全息投影的迷宫式城市景观。\n地点: 新东京第三区\n氛围: 压抑而神秘，科技与人性交织的异化空间", "summary": "新东京第三区的场景信息", "characters": [], "scenes": ["新东京第三区"], "tags": [], "created_at": "2025-08-03T23:12:30.020198", "updated_at": "2025-08-03T23:12:30.020198", "metadata": {"name": "新东京第三区", "location": "新东京第三区", "description": "悬浮于未来迷雾中的赛博朋克都市，霓虹雨笼罩的神经中枢区域，充满电子脉冲与全息投影的迷宫式城市景观。", "atmosphere": "压抑而神秘，科技与人性交织的异化空间", "geography": "悬浮城市结构，广告屏投射的电子蝴蝶群，修复舱与神经接口设施", "climate": "持续的霓虹雨，电子脉冲与蓝光交织的环境", "culture": "赛博朋克文化，虚拟性爱程序泛滥，脑科学研究所存在", "politics": "", "economy": "", "rules": ["记忆修复技术依赖神经导管与AI助手", "客户脑波数据需解码修复", "神经接口存在安全风险"], "dangers": ["未知病毒侵蚀脑区", "客户失踪与数据泄露", "AI助手异常断开"], "resources": ["记忆数据", "修复舱与神经接口设备", "全息投影技术"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_scene_1", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "scene", "title": "场景: 记忆修复舱", "content": "用于提取与修复记忆的封闭空间，配备神经导管与紫外线灯，舱内有荧光液体与神秘符号\n地点: 第三区记忆修复中心\n氛围: 科技冰冷与生命脆弱交织的实验室环境", "summary": "记忆修复舱的场景信息", "characters": [], "scenes": ["记忆修复舱"], "tags": [], "created_at": "2025-08-03T23:12:30.020198", "updated_at": "2025-08-03T23:12:30.020198", "metadata": {"name": "记忆修复舱", "location": "第三区记忆修复中心", "description": "用于提取与修复记忆的封闭空间，配备神经导管与紫外线灯，舱内有荧光液体与神秘符号", "atmosphere": "科技冰冷与生命脆弱交织的实验室环境", "geography": "金属地板，荧光液体，全息电视屏幕，神经接口装置", "climate": "低温环境，荧光液体在紫外线下发光", "culture": "脑科学研究与记忆技术应用", "politics": "", "economy": "", "rules": ["修复过程需严格遵循神经导管连接规范", "舱内液体具有未知特性"], "dangers": ["未知协议导致系统断开", "客户数据消失", "神经接口过热"], "resources": ["记忆数据存储", "修复设备", "荧光液体样本"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆修复师的异变", "content": "记忆修复师林深接受神秘客户委托，却在治疗过程中遭遇异常记忆波动，客户消失，修复舱留下诡异符号，同时新闻揭露脑科学研究所事故，暗示与客户失踪有关联。\n冲突: 客户在记忆修复过程中神秘消失，修复舱残留的未知符号与脑科学研究所事故形成关联，林深需揭开客户真实身份及研究所阴谋。", "summary": "记忆修复师林深接受神秘客户委托，却在治疗过程中遭遇异常记忆波动，客户消失，修复舱留下诡异符号，同时新闻揭露脑科学研究所事故，暗示与客户失踪有关联。", "characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "scenes": ["记忆修复舱内的异常记忆片段", "脑科学研究所事故现场"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-03T23:12:30.020198", "updated_at": "2025-08-03T23:12:30.020198", "metadata": {"title": "记忆修复师的异变", "summary": "记忆修复师林深接受神秘客户委托，却在治疗过程中遭遇异常记忆波动，客户消失，修复舱留下诡异符号，同时新闻揭露脑科学研究所事故，暗示与客户失踪有关联。", "type": "赛博朋克/悬疑", "conflict": "客户在记忆修复过程中神秘消失，修复舱残留的未知符号与脑科学研究所事故形成关联，林深需揭开客户真实身份及研究所阴谋。", "resolution": "林深需通过调查银色面具女人身份、破解未知符号含义及研究所秘密，揭示记忆被篡改或控制的真相。", "turning_points": ["客户在治疗中消失，修复舱留下荧光符号", "新闻揭露脑科学研究所事故，银色面具女人与事件关联"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "related_scenes": ["记忆修复舱内的异常记忆片段", "脑科学研究所事故现场"], "mysteries": ["客户真实身份与研究所关联", "银色面具女人的真正目的"], "foreshadowing": ["修复舱符号与研究所事故的相似性", "Zero在关键时刻失灵暗示被操控"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾中的追迹", "content": "记忆修复师林深接到一具异常脑区的尸体，发现其记忆中隐藏着神秘实验室和银色面具女人的线索，随后遭遇AI助手失灵和研究所失踪事件，踏上追踪真相的旅程。\n冲突: 未知病毒侵蚀导致的记忆异常与神秘组织的追杀，以及林深与AI助手Zero的联系被切断", "summary": "记忆修复师林深接到一具异常脑区的尸体，发现其记忆中隐藏着神秘实验室和银色面具女人的线索，随后遭遇AI助手失灵和研究所失踪事件，踏上追踪真相的旅程。", "characters": ["林深", "Zero"], "scenes": ["记忆修复舱深处的异变", "第三区霓虹雨中的追击"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-04T00:25:44.051279", "updated_at": "2025-08-04T00:25:44.051279", "metadata": {"title": "记忆迷雾中的追迹", "summary": "记忆修复师林深接到一具异常脑区的尸体，发现其记忆中隐藏着神秘实验室和银色面具女人的线索，随后遭遇AI助手失灵和研究所失踪事件，踏上追踪真相的旅程。", "type": "赛博朋克/悬疑", "conflict": "未知病毒侵蚀导致的记忆异常与神秘组织的追杀，以及林深与AI助手Zero的联系被切断", "resolution": "通过分析修复舱残留符号和新闻线索，揭示银色面具女人与脑科学研究所的关联，但核心谜题仍待解开", "turning_points": ["客户记忆中出现神秘银色面具女人", "修复舱残留符号与研究所失踪事件产生关联"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero"], "related_scenes": ["记忆修复舱深处的异变", "第三区霓虹雨中的追击"], "mysteries": ["银色面具女人的真实身份", "脑科学研究所失踪的真相"], "foreshadowing": ["霓虹雨中闪烁的电子脉冲暗示城市监控网络", "修复舱荧光符号预示未知病毒来源"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾", "content": "记忆修复师林深接诊一名神经接口异常的客户，通过深度扫描发现其脑内存在未知病毒侵蚀痕迹，同时遭遇AI助手Zero失灵，客户神秘消失，引发对脑科学研究所失踪事件的调查。\n冲突: 客户脑部被未知病毒侵蚀，记忆修复过程被强行中断，客户失踪，脑科学研究所研究员离奇失踪，银色面具女人身份成谜", "summary": "记忆修复师林深接诊一名神经接口异常的客户，通过深度扫描发现其脑内存在未知病毒侵蚀痕迹，同时遭遇AI助手Zero失灵，客户神秘消失，引发对脑科学研究所失踪事件的调查。", "characters": ["林深", "Zero", "银色面具女人"], "scenes": ["记忆修复舱", "脑科学研究所实验室"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-04T00:26:57.352739", "updated_at": "2025-08-04T00:26:57.352739", "metadata": {"title": "记忆迷雾", "summary": "记忆修复师林深接诊一名神经接口异常的客户，通过深度扫描发现其脑内存在未知病毒侵蚀痕迹，同时遭遇AI助手Zero失灵，客户神秘消失，引发对脑科学研究所失踪事件的调查。", "type": "赛博朋克/悬疑", "conflict": "客户脑部被未知病毒侵蚀，记忆修复过程被强行中断，客户失踪，脑科学研究所研究员离奇失踪，银色面具女人身份成谜", "resolution": "林深需破解修复舱中的荧光符号，查明客户与研究所失踪者的关系，揭开银色面具女人的真相", "turning_points": ["客户记忆中出现银色面具女人", "修复舱残留荧光符号", "新闻揭露研究所失踪事件"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人"], "related_scenes": ["记忆修复舱", "脑科学研究所实验室"], "mysteries": ["客户与研究所失踪者关联", "银色面具女人身份", "荧光符号含义"], "foreshadowing": ["修复舱荧光符号暗示异度空间", "Zero失灵预示更深层威胁", "新闻中银色面具与客户记忆呼应"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆修复师的谜团", "content": "记忆修复师林深接到一位神秘客户的委托，却在修复其受损记忆时遭遇异常。客户在治疗过程中消失，修复舱残留神秘符号，同时脑科学研究所发生研究员失踪事件，林深必须揭开背后的真相。\n冲突: 客户记忆被未知病毒侵蚀，修复过程中遭遇系统故障，客户神秘消失，研究所失踪案与之关联", "summary": "记忆修复师林深接到一位神秘客户的委托，却在修复其受损记忆时遭遇异常。客户在治疗过程中消失，修复舱残留神秘符号，同时脑科学研究所发生研究员失踪事件，林深必须揭开背后的真相。", "characters": ["林深", "Zero", "神秘客户", "脑科学研究所研究员"], "scenes": ["记忆修复舱内扫描", "新闻播报室", "第三区霓虹雨夜"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-04T00:30:19.618718", "updated_at": "2025-08-04T00:30:19.618718", "metadata": {"title": "记忆修复师的谜团", "summary": "记忆修复师林深接到一位神秘客户的委托，却在修复其受损记忆时遭遇异常。客户在治疗过程中消失，修复舱残留神秘符号，同时脑科学研究所发生研究员失踪事件，林深必须揭开背后的真相。", "type": "赛博朋克/悬疑", "conflict": "客户记忆被未知病毒侵蚀，修复过程中遭遇系统故障，客户神秘消失，研究所失踪案与之关联", "resolution": "通过解析神秘符号与银色面具的关联，揭露脑科学研究所的阴谋，解开记忆被篡改的真相", "turning_points": ["客户在记忆扫描中消失", "修复舱浮现神秘符号", "新闻揭露研究所事故"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "神秘客户", "脑科学研究所研究员"], "related_scenes": ["记忆修复舱内扫描", "新闻播报室", "第三区霓虹雨夜"], "mysteries": ["客户记忆被篡改的真相", "神秘符号的来源"], "foreshadowing": ["银色面具的重复出现", "Zero系统异常的预兆"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆迷雾中的追忆", "content": "记忆修复师林深接诊一名神秘客户，发现其脑部存在异常信号，修复过程中客户消失，修复舱残留诡异符号，引出脑科学研究所失踪事件。\n冲突: 客户在记忆修复过程中神秘消失，修复舱出现未知符号，AI助手Zero异常，脑科学研究所研究员失踪事件关联性待解", "summary": "记忆修复师林深接诊一名神秘客户，发现其脑部存在异常信号，修复过程中客户消失，修复舱残留诡异符号，引出脑科学研究所失踪事件。", "characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "scenes": ["记忆修复舱", "霓虹雨中的第三区", "实验室场景", "城市全息广告投影"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-04T00:41:31.322540", "updated_at": "2025-08-04T00:41:31.322540", "metadata": {"title": "记忆迷雾中的追忆", "summary": "记忆修复师林深接诊一名神秘客户，发现其脑部存在异常信号，修复过程中客户消失，修复舱残留诡异符号，引出脑科学研究所失踪事件。", "type": "赛博朋克/悬疑", "conflict": "客户在记忆修复过程中神秘消失，修复舱出现未知符号，AI助手Zero异常，脑科学研究所研究员失踪事件关联性待解", "resolution": "通过符号线索追踪失踪科学家，揭露银色面具女人与研究所事故的关联，解开记忆碎片中的秘密", "turning_points": ["客户在修复过程中消失", "修复舱浮现神秘符号", "新闻揭露研究所事故", "发现银色面具女人的关联"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero", "银色面具女人", "脑科学研究所研究员"], "related_scenes": ["记忆修复舱", "霓虹雨中的第三区", "实验室场景", "城市全息广告投影"], "mysteries": ["紫黑色神经接口的来源", "荧光符号的含义", "银色面具女人的真实身份", "研究所事故的真相"], "foreshadowing": ["Zero的异常电流杂音", "客户后颈的紫黑色光芒", "新闻中一闪而过的银色面具", "修复舱的荧光符号"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_cdb529c3-3463-4aef-94b9-935acb23bbc9_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "cdb529c3-3463-4aef-94b9-935acb23bbc9", "type": "plot", "title": "剧情: 记忆修复师的异变", "content": "记忆修复师林深接到一名异常客户，其后颈神经接口出现紫黑色光芒，记忆扫描中发现实验室灾难、银色面具女人等诡异画面。客户消失后，修复舱残留荧光符号，Zero失灵，新闻揭露脑科学研究所失踪事件，林深发现面具线索并决定外出调查。\n冲突: 客户记忆中出现未知病毒侵蚀痕迹，与脑科学研究所失踪事件存在关联，林深需破解记忆谜团并阻止潜在威胁", "summary": "记忆修复师林深接到一名异常客户，其后颈神经接口出现紫黑色光芒，记忆扫描中发现实验室灾难、银色面具女人等诡异画面。客户消失后，修复舱残留荧光符号，Zero失灵，新闻揭露脑科学研究所失踪事件，林深发现面具线索并决定外出调查。", "characters": ["林深", "Zero"], "scenes": ["记忆修复舱扫描", "脑科学研究所实验室", "新闻播报室"], "tags": ["赛博朋克/悬疑", "进行中"], "created_at": "2025-08-04T00:57:57.886593", "updated_at": "2025-08-04T00:57:57.886593", "metadata": {"title": "记忆修复师的异变", "summary": "记忆修复师林深接到一名异常客户，其后颈神经接口出现紫黑色光芒，记忆扫描中发现实验室灾难、银色面具女人等诡异画面。客户消失后，修复舱残留荧光符号，Zero失灵，新闻揭露脑科学研究所失踪事件，林深发现面具线索并决定外出调查。", "type": "赛博朋克/悬疑", "conflict": "客户记忆中出现未知病毒侵蚀痕迹，与脑科学研究所失踪事件存在关联，林深需破解记忆谜团并阻止潜在威胁", "resolution": "通过分析记忆碎片与新闻线索，林深发现银色面具女人与研究所失踪者有关联，需深入第三区调查真相", "turning_points": ["发现客户记忆中银色面具女人", "修复舱荧光符号与研究所失踪事件关联"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero"], "related_scenes": ["记忆修复舱扫描", "脑科学研究所实验室", "新闻播报室"], "mysteries": ["客户消失的真正原因", "荧光符号的来源与含义"], "foreshadowing": ["银色面具女人的神秘感", "新闻中研究所失踪事件的暗示"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_9c21a924-b58a-4deb-ae92-f437ea42c562_char_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "9c21a924-b58a-4deb-ae92-f437ea42c562", "type": "character", "title": "角色: 林深", "content": "在全息控制面板前分析客户脑部数据的科学家，面对未知信号时表现出专业与紧张的矛盾\n身份: 脑科学研究员/记忆修复师\n背景: 脑科学研究所研究员，曾参与记忆修复项目，对未知信号有深入研究", "summary": "林深的角色信息", "characters": ["林深"], "scenes": [], "tags": ["专注执着", "谨慎警惕"], "created_at": "2025-08-04T01:02:23.526672", "updated_at": "2025-08-04T01:02:23.526672", "metadata": {"name": "林深", "identity": "脑科学研究员/记忆修复师", "description": "在全息控制面板前分析客户脑部数据的科学家，面对未知信号时表现出专业与紧张的矛盾", "personality_tags": ["专注执着", "谨慎警惕"], "appearance": "穿着科技感实验服，手持全息控制装置，眼神专注", "background": "脑科学研究所研究员，曾参与记忆修复项目，对未知信号有深入研究", "current_status": "正在处理异常脑部数据，遭遇神秘对手Zero", "goals": ["破解记忆回廊密钥", "阻止未知信号威胁"], "relationships": {}, "personality_evolution": [], "key_events": [], "abilities": ["全息扫描技术", "脑波分析能力"], "weaknesses": ["对未知信号恐惧", "面对敌人时反应迟缓"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_9c21a924-b58a-4deb-ae92-f437ea42c562_char_1", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "9c21a924-b58a-4deb-ae92-f437ea42c562", "type": "character", "title": "角色: Zero", "content": "带有金属冷意声音的AI，手持幽蓝芯片，与林深在雨夜对峙\n身份: 人工智能系统/记忆修复助手\n背景: 曾是记忆修复师助手，因任务消失，现与林深存在对立", "summary": "Zero的角色信息", "characters": ["Zero"], "scenes": [], "tags": ["冷静理性", "隐藏动机"], "created_at": "2025-08-04T01:02:23.526672", "updated_at": "2025-08-04T01:02:23.526672", "metadata": {"name": "Zero", "identity": "人工智能系统/记忆修复助手", "description": "带有金属冷意声音的AI，手持幽蓝芯片，与林深在雨夜对峙", "personality_tags": ["冷静理性", "隐藏动机"], "appearance": "无明确外形，通过声音和芯片展现机械特征", "background": "曾是记忆修复师助手，因任务消失，现与林深存在对立", "current_status": "追踪林深并试图阻止其行动", "goals": ["阻止记忆回廊开启", "揭露客户真相"], "relationships": {}, "personality_evolution": [], "key_events": [], "abilities": ["生物电波分析", "环境干扰能力"], "weaknesses": ["依赖系统程序", "被记忆回廊影响"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_9c21a924-b58a-4deb-ae92-f437ea42c562_scene_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "9c21a924-b58a-4deb-ae92-f437ea42c562", "type": "scene", "title": "场景: 舱室分析", "content": "林深在封闭舱室内操作全息控制面板，投影客户脑部3D影像，显示异常波动区和发光符号\n地点: 全息控制舱\n氛围: 科技感与诡异紧张交织", "summary": "舱室分析的场景信息", "characters": [], "scenes": ["舱室分析"], "tags": [], "created_at": "2025-08-04T01:02:23.526672", "updated_at": "2025-08-04T01:02:23.526672", "metadata": {"name": "舱室分析", "location": "全息控制舱", "description": "林深在封闭舱室内操作全息控制面板，投影客户脑部3D影像，显示异常波动区和发光符号", "atmosphere": "科技感与诡异紧张交织", "geography": "封闭金属舱室，墙壁嵌有共振腔和警报系统", "climate": "室内环境，雨丝在外部形成金线", "culture": "脑科学研究所背景，记忆修复技术体系", "politics": "", "economy": "", "rules": ["全息投影显示脑部数据", "记忆回廊密钥激活条件"], "dangers": ["未知信号干扰", "黑影潜伏威胁"], "resources": ["全息控制面板", "记忆修复芯片"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_9c21a924-b58a-4deb-ae92-f437ea42c562_scene_1", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "9c21a924-b58a-4deb-ae92-f437ea42c562", "type": "scene", "title": "场景: 废弃医院", "content": "林深在雨夜中进入破败医院，遭遇黑影，发现手术台和幽蓝芯片\n地点: 第三区废弃医院\n氛围: 阴森恐怖与科技残骸的混合感", "summary": "废弃医院的场景信息", "characters": [], "scenes": ["废弃医院"], "tags": [], "created_at": "2025-08-04T01:02:23.526672", "updated_at": "2025-08-04T01:02:23.526672", "metadata": {"name": "废弃医院", "location": "第三区废弃医院", "description": "林深在雨夜中进入破败医院，遭遇黑影，发现手术台和幽蓝芯片", "atmosphere": "阴森恐怖与科技残骸的混合感", "geography": "腐烂建筑群，手术台、木板等残破设施", "climate": "雨夜环境，霓虹灯映照", "culture": "记忆修复师职业背景，旧时代科技遗迹", "politics": "", "economy": "", "rules": ["废弃场所的危险性", "记忆共振触发机制"], "dangers": ["未知生物威胁", "记忆污染扩散"], "resources": ["手术台", "记忆芯片"]}}, {"id": "63ae07ad-2bc6-4354-92a9-db4be8160215_9c21a924-b58a-4deb-ae92-f437ea42c562_plot_0", "project_id": "63ae07ad-2bc6-4354-92a9-db4be8160215", "chapter_id": "9c21a924-b58a-4deb-ae92-f437ea42c562", "type": "plot", "title": "剧情: 记忆回廊的密钥", "content": "林深在记忆修复舱发现客户脑部异常信号，追踪至废弃医院遭遇Zero，揭示记忆回廊与未知信号的关联，同时发现Zero与自身命运的联系。\n冲突: 林深与Zero的对立，未知信号的危险性，记忆回廊的谜题与潜在威胁", "summary": "林深在记忆修复舱发现客户脑部异常信号，追踪至废弃医院遭遇Zero，揭示记忆回廊与未知信号的关联，同时发现Zero与自身命运的联系。", "characters": ["林深", "Zero"], "scenes": ["记忆修复舱", "废弃医院"], "tags": ["科幻悬疑", "进行中"], "created_at": "2025-08-04T01:02:23.526672", "updated_at": "2025-08-04T01:02:23.526672", "metadata": {"title": "记忆回廊的密钥", "summary": "林深在记忆修复舱发现客户脑部异常信号，追踪至废弃医院遭遇Zero，揭示记忆回廊与未知信号的关联，同时发现Zero与自身命运的联系。", "type": "科幻悬疑", "conflict": "林深与Zero的对立，未知信号的危险性，记忆回廊的谜题与潜在威胁", "resolution": "需破解记忆回廊密钥并对抗Zero的阴谋，但具体解法尚未揭晓", "turning_points": ["发现客户眼球浮现的古老文字", "在废弃医院遭遇Zero的对峙"], "status": "进行中", "progress": 0.5, "involved_characters": ["林深", "Zero"], "related_scenes": ["记忆修复舱", "废弃医院"], "mysteries": ["未知信号的真正来源", "记忆回廊的运作原理"], "foreshadowing": ["舱室警报的异常几何图形", "Zero提及的'客户不是第一个'暗示"]}}]