// https://d3js.org/d3-brush/ v3.0.0 Copyright 2010-2021 Mike <PERSON>
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("d3-dispatch"),require("d3-drag"),require("d3-interpolate"),require("d3-selection"),require("d3-transition")):"function"==typeof define&&define.amd?define(["exports","d3-dispatch","d3-drag","d3-interpolate","d3-selection","d3-transition"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).d3=e.d3||{},e.d3,e.d3,e.d3,e.d3,e.d3)}(this,(function(e,t,n,r,i,s){"use strict";var u=e=>()=>e;function o(e,{sourceEvent:t,target:n,selection:r,mode:i,dispatch:s}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},selection:{value:r,enumerable:!0,configurable:!0},mode:{value:i,enumerable:!0,configurable:!0},_:{value:s}})}function a(e){e.stopImmediatePropagation()}function l(e){e.preventDefault(),e.stopImmediatePropagation()}var c={name:"drag"},h={name:"space"},f={name:"handle"},d={name:"center"};const{abs:p,max:b,min:y}=Math;function v(e){return[+e[0],+e[1]]}function m(e){return[v(e[0]),v(e[1])]}var w={name:"x",handles:["w","e"].map(T),input:function(e,t){return null==e?null:[[+e[0],t[0][1]],[+e[1],t[1][1]]]},output:function(e){return e&&[e[0][0],e[1][0]]}},g={name:"y",handles:["n","s"].map(T),input:function(e,t){return null==e?null:[[t[0][0],+e[0]],[t[1][0],+e[1]]]},output:function(e){return e&&[e[0][1],e[1][1]]}},_={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(T),input:function(e){return null==e?null:m(e)},output:function(e){return e}},x={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},k={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},z={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},A={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},E={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function T(e){return{type:e}}function q(e){return!e.ctrlKey&&!e.button}function K(){var e=this.ownerSVGElement||this;return e.hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]}function P(){return navigator.maxTouchPoints||"ontouchstart"in this}function V(e){for(;!e.__brush;)if(!(e=e.parentNode))return;return e.__brush}function j(e){return e[0][0]===e[1][0]||e[0][1]===e[1][1]}function M(e){var v,_=K,M=q,S=P,B=!0,C=t.dispatch("start","brush","end"),D=6;function I(t){var n=t.property("__brush",H).selectAll(".overlay").data([T("overlay")]);n.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",x.overlay).merge(n).each((function(){var e=V(this).extent;i.select(this).attr("x",e[0][0]).attr("y",e[0][1]).attr("width",e[1][0]-e[0][0]).attr("height",e[1][1]-e[0][1])})),t.selectAll(".selection").data([T("selection")]).enter().append("rect").attr("class","selection").attr("cursor",x.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=t.selectAll(".handle").data(e.handles,(function(e){return e.type}));r.exit().remove(),r.enter().append("rect").attr("class",(function(e){return"handle handle--"+e.type})).attr("cursor",(function(e){return x[e.type]})),t.each(O).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",X).filter(S).on("touchstart.brush",X).on("touchmove.brush",Y).on("touchend.brush touchcancel.brush",F).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function O(){var e=i.select(this),t=V(this).selection;t?(e.selectAll(".selection").style("display",null).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1]),e.selectAll(".handle").style("display",null).attr("x",(function(e){return"e"===e.type[e.type.length-1]?t[1][0]-D/2:t[0][0]-D/2})).attr("y",(function(e){return"s"===e.type[0]?t[1][1]-D/2:t[0][1]-D/2})).attr("width",(function(e){return"n"===e.type||"s"===e.type?t[1][0]-t[0][0]+D:D})).attr("height",(function(e){return"e"===e.type||"w"===e.type?t[1][1]-t[0][1]+D:D}))):e.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function G(e,t,n){var r=e.__brush.emitter;return!r||n&&r.clean?new N(e,t,n):r}function N(e,t,n){this.that=e,this.args=t,this.state=e.__brush,this.active=0,this.clean=n}function X(t){if((!v||t.touches)&&M.apply(this,arguments)){var r,u,o,m,_,T,q,K,P,S,C,D=this,I=t.target.__data__.type,N="selection"===(B&&t.metaKey?I="overlay":I)?c:B&&t.altKey?d:f,X=e===g?null:A[I],Y=e===w?null:E[I],F=V(D),H=F.extent,J=F.selection,L=H[0][0],Q=H[0][1],R=H[1][0],U=H[1][1],W=0,Z=0,$=X&&Y&&B&&t.shiftKey,ee=Array.from(t.touches||[t],(e=>{const t=e.identifier;return(e=i.pointer(e,D)).point0=e.slice(),e.identifier=t,e}));s.interrupt(D);var te=G(D,arguments,!0).beforestart();if("overlay"===I){J&&(P=!0);const n=[ee[0],ee[1]||ee[0]];F.selection=J=[[r=e===g?L:y(n[0][0],n[1][0]),o=e===w?Q:y(n[0][1],n[1][1])],[_=e===g?R:b(n[0][0],n[1][0]),q=e===w?U:b(n[0][1],n[1][1])]],ee.length>1&&ue(t)}else r=J[0][0],o=J[0][1],_=J[1][0],q=J[1][1];u=r,m=o,T=_,K=q;var ne=i.select(D).attr("pointer-events","none"),re=ne.selectAll(".overlay").attr("cursor",x[I]);if(t.touches)te.moved=se,te.ended=oe;else{var ie=i.select(t.view).on("mousemove.brush",se,!0).on("mouseup.brush",oe,!0);B&&ie.on("keydown.brush",ae,!0).on("keyup.brush",le,!0),n.dragDisable(t.view)}O.call(D),te.start(t,N.name)}function se(e){for(const t of e.changedTouches||[e])for(const e of ee)e.identifier===t.identifier&&(e.cur=i.pointer(t,D));if($&&!S&&!C&&1===ee.length){const e=ee[0];p(e.cur[0]-e[0])>p(e.cur[1]-e[1])?C=!0:S=!0}for(const e of ee)e.cur&&(e[0]=e.cur[0],e[1]=e.cur[1]);P=!0,l(e),ue(e)}function ue(e){const t=ee[0],n=t.point0;var i;switch(W=t[0]-n[0],Z=t[1]-n[1],N){case h:case c:X&&(W=b(L-r,y(R-_,W)),u=r+W,T=_+W),Y&&(Z=b(Q-o,y(U-q,Z)),m=o+Z,K=q+Z);break;case f:ee[1]?(X&&(u=b(L,y(R,ee[0][0])),T=b(L,y(R,ee[1][0])),X=1),Y&&(m=b(Q,y(U,ee[0][1])),K=b(Q,y(U,ee[1][1])),Y=1)):(X<0?(W=b(L-r,y(R-r,W)),u=r+W,T=_):X>0&&(W=b(L-_,y(R-_,W)),u=r,T=_+W),Y<0?(Z=b(Q-o,y(U-o,Z)),m=o+Z,K=q):Y>0&&(Z=b(Q-q,y(U-q,Z)),m=o,K=q+Z));break;case d:X&&(u=b(L,y(R,r-W*X)),T=b(L,y(R,_+W*X))),Y&&(m=b(Q,y(U,o-Z*Y)),K=b(Q,y(U,q+Z*Y)))}T<u&&(X*=-1,i=r,r=_,_=i,i=u,u=T,T=i,I in k&&re.attr("cursor",x[I=k[I]])),K<m&&(Y*=-1,i=o,o=q,q=i,i=m,m=K,K=i,I in z&&re.attr("cursor",x[I=z[I]])),F.selection&&(J=F.selection),S&&(u=J[0][0],T=J[1][0]),C&&(m=J[0][1],K=J[1][1]),J[0][0]===u&&J[0][1]===m&&J[1][0]===T&&J[1][1]===K||(F.selection=[[u,m],[T,K]],O.call(D),te.brush(e,N.name))}function oe(e){if(a(e),e.touches){if(e.touches.length)return;v&&clearTimeout(v),v=setTimeout((function(){v=null}),500)}else n.dragEnable(e.view,P),ie.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);ne.attr("pointer-events","all"),re.attr("cursor",x.overlay),F.selection&&(J=F.selection),j(J)&&(F.selection=null,O.call(D)),te.end(e,N.name)}function ae(e){switch(e.keyCode){case 16:$=X&&Y;break;case 18:N===f&&(X&&(_=T-W*X,r=u+W*X),Y&&(q=K-Z*Y,o=m+Z*Y),N=d,ue(e));break;case 32:N!==f&&N!==d||(X<0?_=T-W:X>0&&(r=u-W),Y<0?q=K-Z:Y>0&&(o=m-Z),N=h,re.attr("cursor",x.selection),ue(e));break;default:return}l(e)}function le(e){switch(e.keyCode){case 16:$&&(S=C=$=!1,ue(e));break;case 18:N===d&&(X<0?_=T:X>0&&(r=u),Y<0?q=K:Y>0&&(o=m),N=f,ue(e));break;case 32:N===h&&(e.altKey?(X&&(_=T-W*X,r=u+W*X),Y&&(q=K-Z*Y,o=m+Z*Y),N=d):(X<0?_=T:X>0&&(r=u),Y<0?q=K:Y>0&&(o=m),N=f),re.attr("cursor",x[I]),ue(e));break;default:return}l(e)}}function Y(e){G(this,arguments).moved(e)}function F(e){G(this,arguments).ended(e)}function H(){var t=this.__brush||{selection:null};return t.extent=m(_.apply(this,arguments)),t.dim=e,t}return I.move=function(t,n,i){t.tween?t.on("start.brush",(function(e){G(this,arguments).beforestart().start(e)})).on("interrupt.brush end.brush",(function(e){G(this,arguments).end(e)})).tween("brush",(function(){var t=this,i=t.__brush,s=G(t,arguments),u=i.selection,o=e.input("function"==typeof n?n.apply(this,arguments):n,i.extent),a=r.interpolate(u,o);function l(e){i.selection=1===e&&null===o?null:a(e),O.call(t),s.brush()}return null!==u&&null!==o?l:l(1)})):t.each((function(){var t=this,r=arguments,u=t.__brush,o=e.input("function"==typeof n?n.apply(t,r):n,u.extent),a=G(t,r).beforestart();s.interrupt(t),u.selection=null===o?null:o,O.call(t),a.start(i).brush(i).end(i)}))},I.clear=function(e,t){I.move(e,null,t)},N.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(e,t){return this.starting?(this.starting=!1,this.emit("start",e,t)):this.emit("brush",e),this},brush:function(e,t){return this.emit("brush",e,t),this},end:function(e,t){return 0==--this.active&&(delete this.state.emitter,this.emit("end",e,t)),this},emit:function(t,n,r){var s=i.select(this.that).datum();C.call(t,this.that,new o(t,{sourceEvent:n,target:I,selection:e.output(this.state.selection),mode:r,dispatch:C}),s)}},I.extent=function(e){return arguments.length?(_="function"==typeof e?e:u(m(e)),I):_},I.filter=function(e){return arguments.length?(M="function"==typeof e?e:u(!!e),I):M},I.touchable=function(e){return arguments.length?(S="function"==typeof e?e:u(!!e),I):S},I.handleSize=function(e){return arguments.length?(D=+e,I):D},I.keyModifiers=function(e){return arguments.length?(B=!!e,I):B},I.on=function(){var e=C.on.apply(C,arguments);return e===C?I:e},I}e.brush=function(){return M(_)},e.brushSelection=function(e){var t=e.__brush;return t?t.dim.output(t.selection):null},e.brushX=function(){return M(w)},e.brushY=function(){return M(g)},Object.defineProperty(e,"__esModule",{value:!0})}));
